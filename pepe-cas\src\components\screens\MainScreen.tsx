'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useTelegram } from '@/components/providers/TelegramProvider';
import { 
  Wallet, 
  Plus, 
  Minus, 
  TrendingUp, 
  Gamepad2, 
  History, 
  Settings,
  Gift,
  Coins
} from 'lucide-react';

export const MainScreen = () => {
  const { user, webApp } = useTelegram();
  const [balance] = useState(0); // TODO: Connect to real balance

  const menuItems = [
    {
      icon: Plus,
      title: 'Пополнить',
      subtitle: 'Подарки или TON',
      color: 'from-green-400 to-green-600',
      href: '/deposit'
    },
    {
      icon: Gamepad2,
      title: 'Играть',
      subtitle: 'Crash, Coinflip, Double',
      color: 'from-blue-400 to-blue-600',
      href: '/games'
    },
    {
      icon: Minus,
      title: 'Вывод',
      subtitle: 'Получить подарки',
      color: 'from-purple-400 to-purple-600',
      href: '/withdraw'
    },
    {
      icon: History,
      title: 'История',
      subtitle: 'Транзакции и игры',
      color: 'from-orange-400 to-orange-600',
      href: '/history'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black">
      {/* Header */}
      <motion.div
        className="relative overflow-hidden bg-gradient-to-r from-blue-500 via-purple-600 to-pink-500 p-6 pb-8"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ type: "spring", stiffness: 100 }}
      >
        <div className="absolute inset-0 bg-black/10" />
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 rounded-2xl bg-white/20 backdrop-blur-lg flex items-center justify-center">
                <span className="text-2xl">🐸</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">PEPE CAS</h1>
                <p className="text-white/80 text-sm">
                  Привет, {user?.first_name || 'Игрок'}!
                </p>
              </div>
            </div>
            <motion.button
              className="w-10 h-10 rounded-xl bg-white/20 backdrop-blur-lg flex items-center justify-center"
              whileTap={{ scale: 0.95 }}
            >
              <Settings className="w-5 h-5 text-white" />
            </motion.button>
          </div>

          {/* Balance Card */}
          <motion.div
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/80 text-sm mb-1">Баланс</p>
                <div className="flex items-center space-x-2">
                  <Coins className="w-6 h-6 text-yellow-300" />
                  <span className="text-3xl font-bold text-white">
                    {balance.toFixed(2)} TON
                  </span>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-1 text-green-300">
                  <TrendingUp className="w-4 h-4" />
                  <span className="text-sm">+0.00%</span>
                </div>
                <p className="text-white/60 text-xs mt-1">24ч</p>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Menu Grid */}
      <div className="p-6 -mt-4 relative z-20">
        <div className="grid grid-cols-2 gap-4">
          {menuItems.map((item, index) => (
            <motion.div
              key={item.title}
              className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${item.color} p-6 shadow-lg`}
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 * index }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="absolute inset-0 bg-black/10" />
              <div className="relative z-10">
                <item.icon className="w-8 h-8 text-white mb-3" />
                <h3 className="text-lg font-semibold text-white mb-1">
                  {item.title}
                </h3>
                <p className="text-white/80 text-sm">
                  {item.subtitle}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quick Stats */}
        <motion.div
          className="mt-6 bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Быстрая статистика
          </h3>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-2 rounded-xl bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                <Gamepad2 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">0</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">Игр</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-2 rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">0</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">Побед</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-2 rounded-xl bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <Gift className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">0</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">Подарков</p>
            </div>
          </div>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          className="mt-6 bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Последняя активность
          </h3>
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
              <History className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-500 dark:text-gray-400">
              Пока нет активности
            </p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
              Начните играть, чтобы увидеть историю
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
