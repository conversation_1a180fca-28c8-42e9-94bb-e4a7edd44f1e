{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/src/components/screens/MainScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useTelegram } from '@/components/providers/TelegramProvider';\nimport { \n  Wallet, \n  Plus, \n  Minus, \n  TrendingUp, \n  Gamepad2, \n  History, \n  Settings,\n  Gift,\n  Coins\n} from 'lucide-react';\n\nexport const MainScreen = () => {\n  const { user, webApp } = useTelegram();\n  const [balance] = useState(0); // TODO: Connect to real balance\n\n  const menuItems = [\n    {\n      icon: Plus,\n      title: 'Пополнить',\n      subtitle: 'Подарки или TON',\n      color: 'from-green-400 to-green-600',\n      href: '/deposit'\n    },\n    {\n      icon: Gamepad2,\n      title: 'Играть',\n      subtitle: 'Crash, Coinflip, Double',\n      color: 'from-blue-400 to-blue-600',\n      href: '/games'\n    },\n    {\n      icon: Minus,\n      title: 'Вывод',\n      subtitle: 'Получить подарки',\n      color: 'from-purple-400 to-purple-600',\n      href: '/withdraw'\n    },\n    {\n      icon: History,\n      title: 'История',\n      subtitle: 'Транзакции и игры',\n      color: 'from-orange-400 to-orange-600',\n      href: '/history'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black\">\n      {/* Header */}\n      <motion.div\n        className=\"relative overflow-hidden bg-gradient-to-r from-blue-500 via-purple-600 to-pink-500 p-6 pb-8\"\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ type: \"spring\", stiffness: 100 }}\n      >\n        <div className=\"absolute inset-0 bg-black/10\" />\n        <div className=\"relative z-10\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-12 h-12 rounded-2xl bg-white/20 backdrop-blur-lg flex items-center justify-center\">\n                <span className=\"text-2xl\">🐸</span>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-white\">PEPE CAS</h1>\n                <p className=\"text-white/80 text-sm\">\n                  Привет, {user?.first_name || 'Игрок'}!\n                </p>\n              </div>\n            </div>\n            <motion.button\n              className=\"w-10 h-10 rounded-xl bg-white/20 backdrop-blur-lg flex items-center justify-center\"\n              whileTap={{ scale: 0.95 }}\n            >\n              <Settings className=\"w-5 h-5 text-white\" />\n            </motion.button>\n          </div>\n\n          {/* Balance Card */}\n          <motion.div\n            className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20\"\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-white/80 text-sm mb-1\">Баланс</p>\n                <div className=\"flex items-center space-x-2\">\n                  <Coins className=\"w-6 h-6 text-yellow-300\" />\n                  <span className=\"text-3xl font-bold text-white\">\n                    {balance.toFixed(2)} TON\n                  </span>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"flex items-center space-x-1 text-green-300\">\n                  <TrendingUp className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">+0.00%</span>\n                </div>\n                <p className=\"text-white/60 text-xs mt-1\">24ч</p>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n\n      {/* Menu Grid */}\n      <div className=\"p-6 -mt-4 relative z-20\">\n        <div className=\"grid grid-cols-2 gap-4\">\n          {menuItems.map((item, index) => (\n            <motion.div\n              key={item.title}\n              className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${item.color} p-6 shadow-lg`}\n              initial={{ y: 50, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              transition={{ delay: 0.1 * index }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <div className=\"absolute inset-0 bg-black/10\" />\n              <div className=\"relative z-10\">\n                <item.icon className=\"w-8 h-8 text-white mb-3\" />\n                <h3 className=\"text-lg font-semibold text-white mb-1\">\n                  {item.title}\n                </h3>\n                <p className=\"text-white/80 text-sm\">\n                  {item.subtitle}\n                </p>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Quick Stats */}\n        <motion.div\n          className=\"mt-6 bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm\"\n          initial={{ y: 50, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          transition={{ delay: 0.5 }}\n        >\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            Быстрая статистика\n          </h3>\n          <div className=\"grid grid-cols-3 gap-4\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 mx-auto mb-2 rounded-xl bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center\">\n                <Gamepad2 className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" />\n              </div>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">0</p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">Игр</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 mx-auto mb-2 rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center\">\n                <TrendingUp className=\"w-6 h-6 text-green-600 dark:text-green-400\" />\n              </div>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">0</p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">Побед</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 mx-auto mb-2 rounded-xl bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center\">\n                <Gift className=\"w-6 h-6 text-purple-600 dark:text-purple-400\" />\n              </div>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">0</p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">Подарков</p>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Recent Activity */}\n        <motion.div\n          className=\"mt-6 bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm\"\n          initial={{ y: 50, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          transition={{ delay: 0.6 }}\n        >\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            Последняя активность\n          </h3>\n          <div className=\"text-center py-8\">\n            <div className=\"w-16 h-16 mx-auto mb-4 rounded-2xl bg-gray-100 dark:bg-gray-700 flex items-center justify-center\">\n              <History className=\"w-8 h-8 text-gray-400\" />\n            </div>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              Пока нет активности\n            </p>\n            <p className=\"text-sm text-gray-400 dark:text-gray-500 mt-1\">\n              Начните играть, чтобы увидеть историю\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAiBO,MAAM,aAAa;;IACxB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,cAAW,AAAD;IACnC,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,gCAAgC;IAE/D,MAAM,YAAY;QAChB;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,UAAU;YACV,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,iNAAA,CAAA,WAAQ;YACd,OAAO;YACP,UAAU;YACV,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,UAAU;YACV,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,UAAU;YACV,OAAO;YACP,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,MAAM;oBAAU,WAAW;gBAAI;;kCAE7C,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;0DAE7B,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAgC;;;;;;kEAC9C,6LAAC;wDAAE,WAAU;;4DAAwB;4DAC1B,MAAM,cAAc;4DAAQ;;;;;;;;;;;;;;;;;;;kDAI3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;oCAAK,SAAS;gCAAE;gCAClC,SAAS;oCAAE,OAAO;oCAAG,SAAS;gCAAE;gCAChC,YAAY;oCAAE,OAAO;gCAAI;0CAEzB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;;gEACb,QAAQ,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;sDAI1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAW,CAAC,uDAAuD,EAAE,KAAK,KAAK,CAAC,cAAc,CAAC;gCAC/F,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM;gCAAM;gCACjC,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAE,WAAU;0DACV,KAAK,QAAQ;;;;;;;;;;;;;+BAdb,KAAK,KAAK;;;;;;;;;;kCAsBrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDAAE,WAAU;0DAAmD;;;;;;0DAChE,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAE1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,6LAAC;gDAAE,WAAU;0DAAmD;;;;;;0DAChE,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAE1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDAAE,WAAU;0DAAmD;;;;;;0DAChE,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;;kCAM9D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6LAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAGhD,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzE;GArLa;;QACc,sJAAA,CAAA,cAAW;;;KADzB", "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/src/components/ui/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nexport const LoadingScreen = () => {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 via-purple-600 to-pink-500\">\n      <div className=\"text-center\">\n        <motion.div\n          className=\"w-20 h-20 mx-auto mb-8 rounded-2xl bg-white/20 backdrop-blur-lg flex items-center justify-center\"\n          animate={{\n            scale: [1, 1.1, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 2,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        >\n          <div className=\"text-4xl\">🐸</div>\n        </motion.div>\n        \n        <motion.h1\n          className=\"text-3xl font-bold text-white mb-4\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          PEPE CAS\n        </motion.h1>\n        \n        <motion.p\n          className=\"text-white/80 text-lg\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          Loading your casino...\n        </motion.p>\n        \n        <motion.div\n          className=\"mt-8 flex justify-center space-x-2\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.6 }}\n        >\n          {[0, 1, 2].map((i) => (\n            <motion.div\n              key={i}\n              className=\"w-3 h-3 bg-white/60 rounded-full\"\n              animate={{\n                scale: [1, 1.5, 1],\n                opacity: [0.6, 1, 0.6],\n              }}\n              transition={{\n                duration: 1.5,\n                repeat: Infinity,\n                delay: i * 0.2,\n              }}\n            />\n          ))}\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,QAAQ;4BAAC;4BAAG;4BAAK;yBAAI;oBACvB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,MAAM;oBACR;8BAEA,cAAA,6LAAC;wBAAI,WAAU;kCAAW;;;;;;;;;;;8BAG5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;8BAC1B;;;;;;8BAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;8BAC1B;;;;;;8BAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;8BAExB;wBAAC;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCACP,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;gCAClB,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;4BACxB;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,OAAO,IAAI;4BACb;2BAVK;;;;;;;;;;;;;;;;;;;;;AAiBnB;KA9Da", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useTelegram } from '@/components/providers/TelegramProvider';\r\nimport { MainScreen } from '@/components/screens/MainScreen';\r\nimport { LoadingScreen } from '@/components/ui/LoadingScreen';\r\n\r\nexport default function Home() {\r\n  const { isReady } = useTelegram();\r\n\r\n  if (!isReady) {\r\n    return <LoadingScreen />;\r\n  }\r\n\r\n  return <MainScreen />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,cAAW,AAAD;IAE9B,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC,4IAAA,CAAA,gBAAa;;;;;IACvB;IAEA,qBAAO,6LAAC,8IAAA,CAAA,aAAU;;;;;AACpB;GARwB;;QACF,sJAAA,CAAA,cAAW;;;KADT", "debugId": null}}]}