[{"name": "hot-reloader", "duration": 135, "timestamp": 5449695550, "id": 3, "tags": {"version": "15.3.3"}, "startTime": 1748787453977, "traceId": "6f8809e7cee9a9e9"}, {"name": "setup-dev-bundler", "duration": 556763, "timestamp": 5449499461, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748787453781, "traceId": "6f8809e7cee9a9e9"}, {"name": "run-instrumentation-hook", "duration": 24, "timestamp": 5450100710, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748787454382, "traceId": "6f8809e7cee9a9e9"}, {"name": "start-dev-server", "duration": 1091970, "timestamp": 5449026183, "id": 1, "tags": {"cpus": "12", "platform": "win32", "memory.freeMem": "25112731648", "memory.totalMem": "34219769856", "memory.heapSizeLimit": "17159946240", "memory.rss": "180719616", "memory.heapTotal": "98242560", "memory.heapUsed": "71304032"}, "startTime": 1748787453307, "traceId": "6f8809e7cee9a9e9"}, {"name": "compile-path", "duration": 1677040, "timestamp": 5492730678, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748787497012, "traceId": "6f8809e7cee9a9e9"}, {"name": "ensure-page", "duration": 1678214, "timestamp": 5492730079, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748787497011, "traceId": "6f8809e7cee9a9e9"}]