{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/src/components/providers/TelegramProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState, ReactNode } from 'react';\n\ninterface TelegramUser {\n  id: number;\n  first_name: string;\n  last_name?: string;\n  username?: string;\n  language_code?: string;\n  is_premium?: boolean;\n}\n\ninterface TelegramWebApp {\n  initData: string;\n  initDataUnsafe: {\n    user?: TelegramUser;\n    chat_instance?: string;\n    chat_type?: string;\n    start_param?: string;\n  };\n  version: string;\n  platform: string;\n  colorScheme: 'light' | 'dark';\n  themeParams: {\n    bg_color?: string;\n    text_color?: string;\n    hint_color?: string;\n    link_color?: string;\n    button_color?: string;\n    button_text_color?: string;\n  };\n  isExpanded: boolean;\n  viewportHeight: number;\n  viewportStableHeight: number;\n  headerColor: string;\n  backgroundColor: string;\n  isClosingConfirmationEnabled: boolean;\n  ready: () => void;\n  expand: () => void;\n  close: () => void;\n  MainButton: {\n    text: string;\n    color: string;\n    textColor: string;\n    isVisible: boolean;\n    isActive: boolean;\n    isProgressVisible: boolean;\n    setText: (text: string) => void;\n    onClick: (callback: () => void) => void;\n    offClick: (callback: () => void) => void;\n    show: () => void;\n    hide: () => void;\n    enable: () => void;\n    disable: () => void;\n    showProgress: (leaveActive?: boolean) => void;\n    hideProgress: () => void;\n    setParams: (params: any) => void;\n  };\n  BackButton: {\n    isVisible: boolean;\n    onClick: (callback: () => void) => void;\n    offClick: (callback: () => void) => void;\n    show: () => void;\n    hide: () => void;\n  };\n  HapticFeedback: {\n    impactOccurred: (style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft') => void;\n    notificationOccurred: (type: 'error' | 'success' | 'warning') => void;\n    selectionChanged: () => void;\n  };\n  showPopup: (params: {\n    title?: string;\n    message: string;\n    buttons?: Array<{\n      id?: string;\n      type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive';\n      text?: string;\n    }>;\n  }, callback?: (buttonId: string) => void) => void;\n  showAlert: (message: string, callback?: () => void) => void;\n  showConfirm: (message: string, callback?: (confirmed: boolean) => void) => void;\n}\n\ninterface TelegramContextType {\n  webApp: TelegramWebApp | null;\n  user: TelegramUser | null;\n  isReady: boolean;\n}\n\nconst TelegramContext = createContext<TelegramContextType>({\n  webApp: null,\n  user: null,\n  isReady: false,\n});\n\nexport const useTelegram = () => {\n  const context = useContext(TelegramContext);\n  if (!context) {\n    throw new Error('useTelegram must be used within a TelegramProvider');\n  }\n  return context;\n};\n\ninterface TelegramProviderProps {\n  children: ReactNode;\n}\n\nexport const TelegramProvider = ({ children }: TelegramProviderProps) => {\n  const [webApp, setWebApp] = useState<TelegramWebApp | null>(null);\n  const [user, setUser] = useState<TelegramUser | null>(null);\n  const [isReady, setIsReady] = useState(false);\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const tg = (window as any).Telegram?.WebApp;\n      \n      if (tg) {\n        tg.ready();\n        tg.expand();\n        \n        setWebApp(tg);\n        setUser(tg.initDataUnsafe?.user || null);\n        setIsReady(true);\n\n        // Set theme\n        if (tg.colorScheme === 'dark') {\n          document.documentElement.classList.add('dark');\n        } else {\n          document.documentElement.classList.remove('dark');\n        }\n\n        // Apply Telegram theme colors\n        if (tg.themeParams.bg_color) {\n          document.documentElement.style.setProperty('--tg-bg-color', tg.themeParams.bg_color);\n        }\n        if (tg.themeParams.text_color) {\n          document.documentElement.style.setProperty('--tg-text-color', tg.themeParams.text_color);\n        }\n      } else {\n        // Development mode - mock Telegram WebApp\n        console.warn('Telegram WebApp not available, using mock data');\n        setIsReady(true);\n        setUser({\n          id: *********,\n          first_name: 'Test',\n          username: 'testuser',\n          language_code: 'en',\n        });\n      }\n    }\n  }, []);\n\n  return (\n    <TelegramContext.Provider value={{ webApp, user, isReady }}>\n      {children}\n    </TelegramContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AA0FA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAuB;IACzD,QAAQ;IACR,MAAM;IACN,SAAS;AACX;AAEO,MAAM,cAAc;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAyB;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAoCnC;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;YAAQ;YAAM;QAAQ;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/src/components/providers/TonConnectProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { TonConnectUIProvider } from '@tonconnect/ui-react';\nimport { ReactNode } from 'react';\n\nconst manifestUrl = 'https://your-domain.com/tonconnect-manifest.json';\n\ninterface TonConnectProviderProps {\n  children: ReactNode;\n}\n\nexport const TonConnectProvider = ({ children }: TonConnectProviderProps) => {\n  return (\n    <TonConnectUIProvider manifestUrl={manifestUrl}>\n      {children}\n    </TonConnectUIProvider>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAKA,MAAM,cAAc;AAMb,MAAM,qBAAqB,CAAC,EAAE,QAAQ,EAA2B;IACtE,qBACE,8OAAC,4KAAA,CAAA,uBAAoB;QAAC,aAAa;kBAChC;;;;;;AAGP", "debugId": null}}]}