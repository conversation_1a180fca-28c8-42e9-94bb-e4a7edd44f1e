{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/src/components/providers/TelegramProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TelegramProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TelegramProvider() from the server but TelegramProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/TelegramProvider.tsx <module evaluation>\",\n    \"TelegramProvider\",\n);\nexport const useTelegram = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTelegram() from the server but useTelegram is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/TelegramProvider.tsx <module evaluation>\",\n    \"useTelegram\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+EACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+EACA", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/src/components/providers/TelegramProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TelegramProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TelegramProvider() from the server but TelegramProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/TelegramProvider.tsx\",\n    \"TelegramProvider\",\n);\nexport const useTelegram = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTelegram() from the server but useTelegram is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/TelegramProvider.tsx\",\n    \"useTelegram\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2DACA", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/src/components/providers/TonConnectProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TonConnectProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TonConnectProvider() from the server but TonConnectProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/TonConnectProvider.tsx <module evaluation>\",\n    \"TonConnectProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,iFACA", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/src/components/providers/TonConnectProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TonConnectProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TonConnectProvider() from the server but TonConnectProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/TonConnectProvider.tsx\",\n    \"TonConnectProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,6DACA", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport { TelegramProvider } from \"@/components/providers/TelegramProvider\";\nimport { TonConnectProvider } from \"@/components/providers/TonConnectProvider\";\n\nexport const metadata: Metadata = {\n  title: \"PEPE CAS - Telegram Casino\",\n  description: \"Play casino games with Telegram NFT gifts and TON\",\n  viewport: \"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\",\n  themeColor: \"#007AFF\",\n  manifest: \"/manifest.json\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className=\"dark\">\n      <head>\n        <script src=\"https://telegram.org/js/telegram-web-app.js\"></script>\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\" />\n        <meta name=\"theme-color\" content=\"#007AFF\" />\n      </head>\n      <body className=\"safe-area-top safe-area-bottom antialiased\">\n        <TelegramProvider>\n          <TonConnectProvider>\n            <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black\">\n              {children}\n            </div>\n          </TonConnectProvider>\n        </TelegramProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,YAAY;IACZ,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;;0BACxB,8OAAC;;kCACC,8OAAC;wBAAO,KAAI;;;;;;kCACZ,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAEnC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,mJAAA,CAAA,mBAAgB;8BACf,cAAA,8OAAC,qJAAA,CAAA,qBAAkB;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}