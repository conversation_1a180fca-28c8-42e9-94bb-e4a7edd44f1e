{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/tweetnacl-util/nacl-util.js"], "sourcesContent": ["// Written in 2014-2016 by <PERSON> and <PERSON>.\n// Public domain.\n(function(root, f) {\n  'use strict';\n  if (typeof module !== 'undefined' && module.exports) module.exports = f();\n  else if (root.nacl) root.nacl.util = f();\n  else {\n    root.nacl = {};\n    root.nacl.util = f();\n  }\n}(this, function() {\n  'use strict';\n\n  var util = {};\n\n  function validateBase64(s) {\n    if (!(/^(?:[A-Za-z0-9+\\/]{2}[A-Za-z0-9+\\/]{2})*(?:[A-Za-z0-9+\\/]{2}==|[A-Za-z0-9+\\/]{3}=)?$/.test(s))) {\n      throw new TypeError('invalid encoding');\n    }\n  }\n\n  util.decodeUTF8 = function(s) {\n    if (typeof s !== 'string') throw new TypeError('expected string');\n    var i, d = unescape(encodeURIComponent(s)), b = new Uint8Array(d.length);\n    for (i = 0; i < d.length; i++) b[i] = d.charCodeAt(i);\n    return b;\n  };\n\n  util.encodeUTF8 = function(arr) {\n    var i, s = [];\n    for (i = 0; i < arr.length; i++) s.push(String.fromCharCode(arr[i]));\n    return decodeURIComponent(escape(s.join('')));\n  };\n\n  if (typeof atob === 'undefined') {\n    // Node.js\n\n    if (typeof Buffer.from !== 'undefined') {\n       // Node v6 and later\n      util.encodeBase64 = function (arr) { // v6 and later\n          return Buffer.from(arr).toString('base64');\n      };\n\n      util.decodeBase64 = function (s) {\n        validateBase64(s);\n        return new Uint8Array(Array.prototype.slice.call(Buffer.from(s, 'base64'), 0));\n      };\n\n    } else {\n      // Node earlier than v6\n      util.encodeBase64 = function (arr) { // v6 and later\n        return (new Buffer(arr)).toString('base64');\n      };\n\n      util.decodeBase64 = function(s) {\n        validateBase64(s);\n        return new Uint8Array(Array.prototype.slice.call(new Buffer(s, 'base64'), 0));\n      };\n    }\n\n  } else {\n    // Browsers\n\n    util.encodeBase64 = function(arr) {\n      var i, s = [], len = arr.length;\n      for (i = 0; i < len; i++) s.push(String.fromCharCode(arr[i]));\n      return btoa(s.join(''));\n    };\n\n    util.decodeBase64 = function(s) {\n      validateBase64(s);\n      var i, d = atob(s), b = new Uint8Array(d.length);\n      for (i = 0; i < d.length; i++) b[i] = d.charCodeAt(i);\n      return b;\n    };\n\n  }\n\n  return util;\n\n}));\n"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,iBAAiB;AAChB,CAAA,SAAS,IAAI,EAAE,CAAC;IACf;IACA,IAAI,+CAAkB,eAAe,OAAO,OAAO,EAAE,OAAO,OAAO,GAAG;SACjE,IAAI,KAAK,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG;SAChC;QACH,KAAK,IAAI,GAAG,CAAC;QACb,KAAK,IAAI,CAAC,IAAI,GAAG;IACnB;AACF,CAAA,EAAE,IAAI,EAAE;IACN;IAEA,IAAI,OAAO,CAAC;IAEZ,SAAS,eAAe,CAAC;QACvB,IAAI,CAAE,uFAAuF,IAAI,CAAC,IAAK;YACrG,MAAM,IAAI,UAAU;QACtB;IACF;IAEA,KAAK,UAAU,GAAG,SAAS,CAAC;QAC1B,IAAI,OAAO,MAAM,UAAU,MAAM,IAAI,UAAU;QAC/C,IAAI,GAAG,IAAI,SAAS,mBAAmB,KAAK,IAAI,IAAI,WAAW,EAAE,MAAM;QACvE,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC;QACnD,OAAO;IACT;IAEA,KAAK,UAAU,GAAG,SAAS,GAAG;QAC5B,IAAI,GAAG,IAAI,EAAE;QACb,IAAK,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK,EAAE,IAAI,CAAC,OAAO,YAAY,CAAC,GAAG,CAAC,EAAE;QAClE,OAAO,mBAAmB,OAAO,EAAE,IAAI,CAAC;IAC1C;IAEA,IAAI,OAAO,SAAS,aAAa;QAC/B,UAAU;QAEV,IAAI,OAAO,OAAO,IAAI,KAAK,aAAa;YACrC,oBAAoB;YACrB,KAAK,YAAY,GAAG,SAAU,GAAG;gBAC7B,OAAO,OAAO,IAAI,CAAC,KAAK,QAAQ,CAAC;YACrC;YAEA,KAAK,YAAY,GAAG,SAAU,CAAC;gBAC7B,eAAe;gBACf,OAAO,IAAI,WAAW,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,WAAW;YAC7E;QAEF,OAAO;YACL,uBAAuB;YACvB,KAAK,YAAY,GAAG,SAAU,GAAG;gBAC/B,OAAO,AAAC,IAAI,OAAO,KAAM,QAAQ,CAAC;YACpC;YAEA,KAAK,YAAY,GAAG,SAAS,CAAC;gBAC5B,eAAe;gBACf,OAAO,IAAI,WAAW,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,GAAG,WAAW;YAC5E;QACF;IAEF,OAAO;QACL,WAAW;QAEX,KAAK,YAAY,GAAG,SAAS,GAAG;YAC9B,IAAI,GAAG,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC/B,IAAK,IAAI,GAAG,IAAI,KAAK,IAAK,EAAE,IAAI,CAAC,OAAO,YAAY,CAAC,GAAG,CAAC,EAAE;YAC3D,OAAO,KAAK,EAAE,IAAI,CAAC;QACrB;QAEA,KAAK,YAAY,GAAG,SAAS,CAAC;YAC5B,eAAe;YACf,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,WAAW,EAAE,MAAM;YAC/C,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC;YACnD,OAAO;QACT;IAEF;IAEA,OAAO;AAET", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/tweetnacl/nacl-fast.js"], "sourcesContent": ["(function(nacl) {\n'use strict';\n\n// Ported in 2014 by <PERSON> and <PERSON>.\n// Public domain.\n//\n// Implementation derived from TweetNaCl version 20140427.\n// See for details: http://tweetnacl.cr.yp.to/\n\nvar gf = function(init) {\n  var i, r = new Float64Array(16);\n  if (init) for (i = 0; i < init.length; i++) r[i] = init[i];\n  return r;\n};\n\n//  Pluggable, initialized in high-level API below.\nvar randombytes = function(/* x, n */) { throw new Error('no PRNG'); };\n\nvar _0 = new Uint8Array(16);\nvar _9 = new Uint8Array(32); _9[0] = 9;\n\nvar gf0 = gf(),\n    gf1 = gf([1]),\n    _121665 = gf([0xdb41, 1]),\n    D = gf([0x78a3, 0x1359, 0x4dca, 0x75eb, 0xd8ab, 0x4141, 0x0a4d, 0x0070, 0xe898, 0x7779, 0x4079, 0x8cc7, 0xfe73, 0x2b6f, 0x6cee, 0x5203]),\n    D2 = gf([0xf159, 0x26b2, 0x9b94, 0xebd6, 0xb156, 0x8283, 0x149a, 0x00e0, 0xd130, 0xeef3, 0x80f2, 0x198e, 0xfce7, 0x56df, 0xd9dc, 0x2406]),\n    X = gf([0xd51a, 0x8f25, 0x2d60, 0xc956, 0xa7b2, 0x9525, 0xc760, 0x692c, 0xdc5c, 0xfdd6, 0xe231, 0xc0a4, 0x53fe, 0xcd6e, 0x36d3, 0x2169]),\n    Y = gf([0x6658, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666]),\n    I = gf([0xa0b0, 0x4a0e, 0x1b27, 0xc4ee, 0xe478, 0xad2f, 0x1806, 0x2f43, 0xd7a7, 0x3dfb, 0x0099, 0x2b4d, 0xdf0b, 0x4fc1, 0x2480, 0x2b83]);\n\nfunction ts64(x, i, h, l) {\n  x[i]   = (h >> 24) & 0xff;\n  x[i+1] = (h >> 16) & 0xff;\n  x[i+2] = (h >>  8) & 0xff;\n  x[i+3] = h & 0xff;\n  x[i+4] = (l >> 24)  & 0xff;\n  x[i+5] = (l >> 16)  & 0xff;\n  x[i+6] = (l >>  8)  & 0xff;\n  x[i+7] = l & 0xff;\n}\n\nfunction vn(x, xi, y, yi, n) {\n  var i,d = 0;\n  for (i = 0; i < n; i++) d |= x[xi+i]^y[yi+i];\n  return (1 & ((d - 1) >>> 8)) - 1;\n}\n\nfunction crypto_verify_16(x, xi, y, yi) {\n  return vn(x,xi,y,yi,16);\n}\n\nfunction crypto_verify_32(x, xi, y, yi) {\n  return vn(x,xi,y,yi,32);\n}\n\nfunction core_salsa20(o, p, k, c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n   x0 =  x0 +  j0 | 0;\n   x1 =  x1 +  j1 | 0;\n   x2 =  x2 +  j2 | 0;\n   x3 =  x3 +  j3 | 0;\n   x4 =  x4 +  j4 | 0;\n   x5 =  x5 +  j5 | 0;\n   x6 =  x6 +  j6 | 0;\n   x7 =  x7 +  j7 | 0;\n   x8 =  x8 +  j8 | 0;\n   x9 =  x9 +  j9 | 0;\n  x10 = x10 + j10 | 0;\n  x11 = x11 + j11 | 0;\n  x12 = x12 + j12 | 0;\n  x13 = x13 + j13 | 0;\n  x14 = x14 + j14 | 0;\n  x15 = x15 + j15 | 0;\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x1 >>>  0 & 0xff;\n  o[ 5] = x1 >>>  8 & 0xff;\n  o[ 6] = x1 >>> 16 & 0xff;\n  o[ 7] = x1 >>> 24 & 0xff;\n\n  o[ 8] = x2 >>>  0 & 0xff;\n  o[ 9] = x2 >>>  8 & 0xff;\n  o[10] = x2 >>> 16 & 0xff;\n  o[11] = x2 >>> 24 & 0xff;\n\n  o[12] = x3 >>>  0 & 0xff;\n  o[13] = x3 >>>  8 & 0xff;\n  o[14] = x3 >>> 16 & 0xff;\n  o[15] = x3 >>> 24 & 0xff;\n\n  o[16] = x4 >>>  0 & 0xff;\n  o[17] = x4 >>>  8 & 0xff;\n  o[18] = x4 >>> 16 & 0xff;\n  o[19] = x4 >>> 24 & 0xff;\n\n  o[20] = x5 >>>  0 & 0xff;\n  o[21] = x5 >>>  8 & 0xff;\n  o[22] = x5 >>> 16 & 0xff;\n  o[23] = x5 >>> 24 & 0xff;\n\n  o[24] = x6 >>>  0 & 0xff;\n  o[25] = x6 >>>  8 & 0xff;\n  o[26] = x6 >>> 16 & 0xff;\n  o[27] = x6 >>> 24 & 0xff;\n\n  o[28] = x7 >>>  0 & 0xff;\n  o[29] = x7 >>>  8 & 0xff;\n  o[30] = x7 >>> 16 & 0xff;\n  o[31] = x7 >>> 24 & 0xff;\n\n  o[32] = x8 >>>  0 & 0xff;\n  o[33] = x8 >>>  8 & 0xff;\n  o[34] = x8 >>> 16 & 0xff;\n  o[35] = x8 >>> 24 & 0xff;\n\n  o[36] = x9 >>>  0 & 0xff;\n  o[37] = x9 >>>  8 & 0xff;\n  o[38] = x9 >>> 16 & 0xff;\n  o[39] = x9 >>> 24 & 0xff;\n\n  o[40] = x10 >>>  0 & 0xff;\n  o[41] = x10 >>>  8 & 0xff;\n  o[42] = x10 >>> 16 & 0xff;\n  o[43] = x10 >>> 24 & 0xff;\n\n  o[44] = x11 >>>  0 & 0xff;\n  o[45] = x11 >>>  8 & 0xff;\n  o[46] = x11 >>> 16 & 0xff;\n  o[47] = x11 >>> 24 & 0xff;\n\n  o[48] = x12 >>>  0 & 0xff;\n  o[49] = x12 >>>  8 & 0xff;\n  o[50] = x12 >>> 16 & 0xff;\n  o[51] = x12 >>> 24 & 0xff;\n\n  o[52] = x13 >>>  0 & 0xff;\n  o[53] = x13 >>>  8 & 0xff;\n  o[54] = x13 >>> 16 & 0xff;\n  o[55] = x13 >>> 24 & 0xff;\n\n  o[56] = x14 >>>  0 & 0xff;\n  o[57] = x14 >>>  8 & 0xff;\n  o[58] = x14 >>> 16 & 0xff;\n  o[59] = x14 >>> 24 & 0xff;\n\n  o[60] = x15 >>>  0 & 0xff;\n  o[61] = x15 >>>  8 & 0xff;\n  o[62] = x15 >>> 16 & 0xff;\n  o[63] = x15 >>> 24 & 0xff;\n}\n\nfunction core_hsalsa20(o,p,k,c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x5 >>>  0 & 0xff;\n  o[ 5] = x5 >>>  8 & 0xff;\n  o[ 6] = x5 >>> 16 & 0xff;\n  o[ 7] = x5 >>> 24 & 0xff;\n\n  o[ 8] = x10 >>>  0 & 0xff;\n  o[ 9] = x10 >>>  8 & 0xff;\n  o[10] = x10 >>> 16 & 0xff;\n  o[11] = x10 >>> 24 & 0xff;\n\n  o[12] = x15 >>>  0 & 0xff;\n  o[13] = x15 >>>  8 & 0xff;\n  o[14] = x15 >>> 16 & 0xff;\n  o[15] = x15 >>> 24 & 0xff;\n\n  o[16] = x6 >>>  0 & 0xff;\n  o[17] = x6 >>>  8 & 0xff;\n  o[18] = x6 >>> 16 & 0xff;\n  o[19] = x6 >>> 24 & 0xff;\n\n  o[20] = x7 >>>  0 & 0xff;\n  o[21] = x7 >>>  8 & 0xff;\n  o[22] = x7 >>> 16 & 0xff;\n  o[23] = x7 >>> 24 & 0xff;\n\n  o[24] = x8 >>>  0 & 0xff;\n  o[25] = x8 >>>  8 & 0xff;\n  o[26] = x8 >>> 16 & 0xff;\n  o[27] = x8 >>> 24 & 0xff;\n\n  o[28] = x9 >>>  0 & 0xff;\n  o[29] = x9 >>>  8 & 0xff;\n  o[30] = x9 >>> 16 & 0xff;\n  o[31] = x9 >>> 24 & 0xff;\n}\n\nfunction crypto_core_salsa20(out,inp,k,c) {\n  core_salsa20(out,inp,k,c);\n}\n\nfunction crypto_core_hsalsa20(out,inp,k,c) {\n  core_hsalsa20(out,inp,k,c);\n}\n\nvar sigma = new Uint8Array([101, 120, 112, 97, 110, 100, 32, 51, 50, 45, 98, 121, 116, 101, 32, 107]);\n            // \"expand 32-byte k\"\n\nfunction crypto_stream_salsa20_xor(c,cpos,m,mpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n    mpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream_salsa20(c,cpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream(c,cpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20(c,cpos,d,sn,s);\n}\n\nfunction crypto_stream_xor(c,cpos,m,mpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20_xor(c,cpos,m,mpos,d,sn,s);\n}\n\n/*\n* Port of Andrew Moon's Poly1305-donna-16. Public domain.\n* https://github.com/floodyberry/poly1305-donna\n*/\n\nvar poly1305 = function(key) {\n  this.buffer = new Uint8Array(16);\n  this.r = new Uint16Array(10);\n  this.h = new Uint16Array(10);\n  this.pad = new Uint16Array(8);\n  this.leftover = 0;\n  this.fin = 0;\n\n  var t0, t1, t2, t3, t4, t5, t6, t7;\n\n  t0 = key[ 0] & 0xff | (key[ 1] & 0xff) << 8; this.r[0] = ( t0                     ) & 0x1fff;\n  t1 = key[ 2] & 0xff | (key[ 3] & 0xff) << 8; this.r[1] = ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n  t2 = key[ 4] & 0xff | (key[ 5] & 0xff) << 8; this.r[2] = ((t1 >>> 10) | (t2 <<  6)) & 0x1f03;\n  t3 = key[ 6] & 0xff | (key[ 7] & 0xff) << 8; this.r[3] = ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n  t4 = key[ 8] & 0xff | (key[ 9] & 0xff) << 8; this.r[4] = ((t3 >>>  4) | (t4 << 12)) & 0x00ff;\n  this.r[5] = ((t4 >>>  1)) & 0x1ffe;\n  t5 = key[10] & 0xff | (key[11] & 0xff) << 8; this.r[6] = ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n  t6 = key[12] & 0xff | (key[13] & 0xff) << 8; this.r[7] = ((t5 >>> 11) | (t6 <<  5)) & 0x1f81;\n  t7 = key[14] & 0xff | (key[15] & 0xff) << 8; this.r[8] = ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n  this.r[9] = ((t7 >>>  5)) & 0x007f;\n\n  this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n  this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n  this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n  this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n  this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n  this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n  this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n  this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n};\n\npoly1305.prototype.blocks = function(m, mpos, bytes) {\n  var hibit = this.fin ? 0 : (1 << 11);\n  var t0, t1, t2, t3, t4, t5, t6, t7, c;\n  var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n\n  var h0 = this.h[0],\n      h1 = this.h[1],\n      h2 = this.h[2],\n      h3 = this.h[3],\n      h4 = this.h[4],\n      h5 = this.h[5],\n      h6 = this.h[6],\n      h7 = this.h[7],\n      h8 = this.h[8],\n      h9 = this.h[9];\n\n  var r0 = this.r[0],\n      r1 = this.r[1],\n      r2 = this.r[2],\n      r3 = this.r[3],\n      r4 = this.r[4],\n      r5 = this.r[5],\n      r6 = this.r[6],\n      r7 = this.r[7],\n      r8 = this.r[8],\n      r9 = this.r[9];\n\n  while (bytes >= 16) {\n    t0 = m[mpos+ 0] & 0xff | (m[mpos+ 1] & 0xff) << 8; h0 += ( t0                     ) & 0x1fff;\n    t1 = m[mpos+ 2] & 0xff | (m[mpos+ 3] & 0xff) << 8; h1 += ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n    t2 = m[mpos+ 4] & 0xff | (m[mpos+ 5] & 0xff) << 8; h2 += ((t1 >>> 10) | (t2 <<  6)) & 0x1fff;\n    t3 = m[mpos+ 6] & 0xff | (m[mpos+ 7] & 0xff) << 8; h3 += ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n    t4 = m[mpos+ 8] & 0xff | (m[mpos+ 9] & 0xff) << 8; h4 += ((t3 >>>  4) | (t4 << 12)) & 0x1fff;\n    h5 += ((t4 >>>  1)) & 0x1fff;\n    t5 = m[mpos+10] & 0xff | (m[mpos+11] & 0xff) << 8; h6 += ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n    t6 = m[mpos+12] & 0xff | (m[mpos+13] & 0xff) << 8; h7 += ((t5 >>> 11) | (t6 <<  5)) & 0x1fff;\n    t7 = m[mpos+14] & 0xff | (m[mpos+15] & 0xff) << 8; h8 += ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n    h9 += ((t7 >>> 5)) | hibit;\n\n    c = 0;\n\n    d0 = c;\n    d0 += h0 * r0;\n    d0 += h1 * (5 * r9);\n    d0 += h2 * (5 * r8);\n    d0 += h3 * (5 * r7);\n    d0 += h4 * (5 * r6);\n    c = (d0 >>> 13); d0 &= 0x1fff;\n    d0 += h5 * (5 * r5);\n    d0 += h6 * (5 * r4);\n    d0 += h7 * (5 * r3);\n    d0 += h8 * (5 * r2);\n    d0 += h9 * (5 * r1);\n    c += (d0 >>> 13); d0 &= 0x1fff;\n\n    d1 = c;\n    d1 += h0 * r1;\n    d1 += h1 * r0;\n    d1 += h2 * (5 * r9);\n    d1 += h3 * (5 * r8);\n    d1 += h4 * (5 * r7);\n    c = (d1 >>> 13); d1 &= 0x1fff;\n    d1 += h5 * (5 * r6);\n    d1 += h6 * (5 * r5);\n    d1 += h7 * (5 * r4);\n    d1 += h8 * (5 * r3);\n    d1 += h9 * (5 * r2);\n    c += (d1 >>> 13); d1 &= 0x1fff;\n\n    d2 = c;\n    d2 += h0 * r2;\n    d2 += h1 * r1;\n    d2 += h2 * r0;\n    d2 += h3 * (5 * r9);\n    d2 += h4 * (5 * r8);\n    c = (d2 >>> 13); d2 &= 0x1fff;\n    d2 += h5 * (5 * r7);\n    d2 += h6 * (5 * r6);\n    d2 += h7 * (5 * r5);\n    d2 += h8 * (5 * r4);\n    d2 += h9 * (5 * r3);\n    c += (d2 >>> 13); d2 &= 0x1fff;\n\n    d3 = c;\n    d3 += h0 * r3;\n    d3 += h1 * r2;\n    d3 += h2 * r1;\n    d3 += h3 * r0;\n    d3 += h4 * (5 * r9);\n    c = (d3 >>> 13); d3 &= 0x1fff;\n    d3 += h5 * (5 * r8);\n    d3 += h6 * (5 * r7);\n    d3 += h7 * (5 * r6);\n    d3 += h8 * (5 * r5);\n    d3 += h9 * (5 * r4);\n    c += (d3 >>> 13); d3 &= 0x1fff;\n\n    d4 = c;\n    d4 += h0 * r4;\n    d4 += h1 * r3;\n    d4 += h2 * r2;\n    d4 += h3 * r1;\n    d4 += h4 * r0;\n    c = (d4 >>> 13); d4 &= 0x1fff;\n    d4 += h5 * (5 * r9);\n    d4 += h6 * (5 * r8);\n    d4 += h7 * (5 * r7);\n    d4 += h8 * (5 * r6);\n    d4 += h9 * (5 * r5);\n    c += (d4 >>> 13); d4 &= 0x1fff;\n\n    d5 = c;\n    d5 += h0 * r5;\n    d5 += h1 * r4;\n    d5 += h2 * r3;\n    d5 += h3 * r2;\n    d5 += h4 * r1;\n    c = (d5 >>> 13); d5 &= 0x1fff;\n    d5 += h5 * r0;\n    d5 += h6 * (5 * r9);\n    d5 += h7 * (5 * r8);\n    d5 += h8 * (5 * r7);\n    d5 += h9 * (5 * r6);\n    c += (d5 >>> 13); d5 &= 0x1fff;\n\n    d6 = c;\n    d6 += h0 * r6;\n    d6 += h1 * r5;\n    d6 += h2 * r4;\n    d6 += h3 * r3;\n    d6 += h4 * r2;\n    c = (d6 >>> 13); d6 &= 0x1fff;\n    d6 += h5 * r1;\n    d6 += h6 * r0;\n    d6 += h7 * (5 * r9);\n    d6 += h8 * (5 * r8);\n    d6 += h9 * (5 * r7);\n    c += (d6 >>> 13); d6 &= 0x1fff;\n\n    d7 = c;\n    d7 += h0 * r7;\n    d7 += h1 * r6;\n    d7 += h2 * r5;\n    d7 += h3 * r4;\n    d7 += h4 * r3;\n    c = (d7 >>> 13); d7 &= 0x1fff;\n    d7 += h5 * r2;\n    d7 += h6 * r1;\n    d7 += h7 * r0;\n    d7 += h8 * (5 * r9);\n    d7 += h9 * (5 * r8);\n    c += (d7 >>> 13); d7 &= 0x1fff;\n\n    d8 = c;\n    d8 += h0 * r8;\n    d8 += h1 * r7;\n    d8 += h2 * r6;\n    d8 += h3 * r5;\n    d8 += h4 * r4;\n    c = (d8 >>> 13); d8 &= 0x1fff;\n    d8 += h5 * r3;\n    d8 += h6 * r2;\n    d8 += h7 * r1;\n    d8 += h8 * r0;\n    d8 += h9 * (5 * r9);\n    c += (d8 >>> 13); d8 &= 0x1fff;\n\n    d9 = c;\n    d9 += h0 * r9;\n    d9 += h1 * r8;\n    d9 += h2 * r7;\n    d9 += h3 * r6;\n    d9 += h4 * r5;\n    c = (d9 >>> 13); d9 &= 0x1fff;\n    d9 += h5 * r4;\n    d9 += h6 * r3;\n    d9 += h7 * r2;\n    d9 += h8 * r1;\n    d9 += h9 * r0;\n    c += (d9 >>> 13); d9 &= 0x1fff;\n\n    c = (((c << 2) + c)) | 0;\n    c = (c + d0) | 0;\n    d0 = c & 0x1fff;\n    c = (c >>> 13);\n    d1 += c;\n\n    h0 = d0;\n    h1 = d1;\n    h2 = d2;\n    h3 = d3;\n    h4 = d4;\n    h5 = d5;\n    h6 = d6;\n    h7 = d7;\n    h8 = d8;\n    h9 = d9;\n\n    mpos += 16;\n    bytes -= 16;\n  }\n  this.h[0] = h0;\n  this.h[1] = h1;\n  this.h[2] = h2;\n  this.h[3] = h3;\n  this.h[4] = h4;\n  this.h[5] = h5;\n  this.h[6] = h6;\n  this.h[7] = h7;\n  this.h[8] = h8;\n  this.h[9] = h9;\n};\n\npoly1305.prototype.finish = function(mac, macpos) {\n  var g = new Uint16Array(10);\n  var c, mask, f, i;\n\n  if (this.leftover) {\n    i = this.leftover;\n    this.buffer[i++] = 1;\n    for (; i < 16; i++) this.buffer[i] = 0;\n    this.fin = 1;\n    this.blocks(this.buffer, 0, 16);\n  }\n\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  for (i = 2; i < 10; i++) {\n    this.h[i] += c;\n    c = this.h[i] >>> 13;\n    this.h[i] &= 0x1fff;\n  }\n  this.h[0] += (c * 5);\n  c = this.h[0] >>> 13;\n  this.h[0] &= 0x1fff;\n  this.h[1] += c;\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  this.h[2] += c;\n\n  g[0] = this.h[0] + 5;\n  c = g[0] >>> 13;\n  g[0] &= 0x1fff;\n  for (i = 1; i < 10; i++) {\n    g[i] = this.h[i] + c;\n    c = g[i] >>> 13;\n    g[i] &= 0x1fff;\n  }\n  g[9] -= (1 << 13);\n\n  mask = (c ^ 1) - 1;\n  for (i = 0; i < 10; i++) g[i] &= mask;\n  mask = ~mask;\n  for (i = 0; i < 10; i++) this.h[i] = (this.h[i] & mask) | g[i];\n\n  this.h[0] = ((this.h[0]       ) | (this.h[1] << 13)                    ) & 0xffff;\n  this.h[1] = ((this.h[1] >>>  3) | (this.h[2] << 10)                    ) & 0xffff;\n  this.h[2] = ((this.h[2] >>>  6) | (this.h[3] <<  7)                    ) & 0xffff;\n  this.h[3] = ((this.h[3] >>>  9) | (this.h[4] <<  4)                    ) & 0xffff;\n  this.h[4] = ((this.h[4] >>> 12) | (this.h[5] <<  1) | (this.h[6] << 14)) & 0xffff;\n  this.h[5] = ((this.h[6] >>>  2) | (this.h[7] << 11)                    ) & 0xffff;\n  this.h[6] = ((this.h[7] >>>  5) | (this.h[8] <<  8)                    ) & 0xffff;\n  this.h[7] = ((this.h[8] >>>  8) | (this.h[9] <<  5)                    ) & 0xffff;\n\n  f = this.h[0] + this.pad[0];\n  this.h[0] = f & 0xffff;\n  for (i = 1; i < 8; i++) {\n    f = (((this.h[i] + this.pad[i]) | 0) + (f >>> 16)) | 0;\n    this.h[i] = f & 0xffff;\n  }\n\n  mac[macpos+ 0] = (this.h[0] >>> 0) & 0xff;\n  mac[macpos+ 1] = (this.h[0] >>> 8) & 0xff;\n  mac[macpos+ 2] = (this.h[1] >>> 0) & 0xff;\n  mac[macpos+ 3] = (this.h[1] >>> 8) & 0xff;\n  mac[macpos+ 4] = (this.h[2] >>> 0) & 0xff;\n  mac[macpos+ 5] = (this.h[2] >>> 8) & 0xff;\n  mac[macpos+ 6] = (this.h[3] >>> 0) & 0xff;\n  mac[macpos+ 7] = (this.h[3] >>> 8) & 0xff;\n  mac[macpos+ 8] = (this.h[4] >>> 0) & 0xff;\n  mac[macpos+ 9] = (this.h[4] >>> 8) & 0xff;\n  mac[macpos+10] = (this.h[5] >>> 0) & 0xff;\n  mac[macpos+11] = (this.h[5] >>> 8) & 0xff;\n  mac[macpos+12] = (this.h[6] >>> 0) & 0xff;\n  mac[macpos+13] = (this.h[6] >>> 8) & 0xff;\n  mac[macpos+14] = (this.h[7] >>> 0) & 0xff;\n  mac[macpos+15] = (this.h[7] >>> 8) & 0xff;\n};\n\npoly1305.prototype.update = function(m, mpos, bytes) {\n  var i, want;\n\n  if (this.leftover) {\n    want = (16 - this.leftover);\n    if (want > bytes)\n      want = bytes;\n    for (i = 0; i < want; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    bytes -= want;\n    mpos += want;\n    this.leftover += want;\n    if (this.leftover < 16)\n      return;\n    this.blocks(this.buffer, 0, 16);\n    this.leftover = 0;\n  }\n\n  if (bytes >= 16) {\n    want = bytes - (bytes % 16);\n    this.blocks(m, mpos, want);\n    mpos += want;\n    bytes -= want;\n  }\n\n  if (bytes) {\n    for (i = 0; i < bytes; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    this.leftover += bytes;\n  }\n};\n\nfunction crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n  var s = new poly1305(k);\n  s.update(m, mpos, n);\n  s.finish(out, outpos);\n  return 0;\n}\n\nfunction crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n  var x = new Uint8Array(16);\n  crypto_onetimeauth(x,0,m,mpos,n,k);\n  return crypto_verify_16(h,hpos,x,0);\n}\n\nfunction crypto_secretbox(c,m,d,n,k) {\n  var i;\n  if (d < 32) return -1;\n  crypto_stream_xor(c,0,m,0,d,n,k);\n  crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n  for (i = 0; i < 16; i++) c[i] = 0;\n  return 0;\n}\n\nfunction crypto_secretbox_open(m,c,d,n,k) {\n  var i;\n  var x = new Uint8Array(32);\n  if (d < 32) return -1;\n  crypto_stream(x,0,32,n,k);\n  if (crypto_onetimeauth_verify(c, 16,c, 32,d - 32,x) !== 0) return -1;\n  crypto_stream_xor(m,0,c,0,d,n,k);\n  for (i = 0; i < 32; i++) m[i] = 0;\n  return 0;\n}\n\nfunction set25519(r, a) {\n  var i;\n  for (i = 0; i < 16; i++) r[i] = a[i]|0;\n}\n\nfunction car25519(o) {\n  var i, v, c = 1;\n  for (i = 0; i < 16; i++) {\n    v = o[i] + c + 65535;\n    c = Math.floor(v / 65536);\n    o[i] = v - c * 65536;\n  }\n  o[0] += c-1 + 37 * (c-1);\n}\n\nfunction sel25519(p, q, b) {\n  var t, c = ~(b-1);\n  for (var i = 0; i < 16; i++) {\n    t = c & (p[i] ^ q[i]);\n    p[i] ^= t;\n    q[i] ^= t;\n  }\n}\n\nfunction pack25519(o, n) {\n  var i, j, b;\n  var m = gf(), t = gf();\n  for (i = 0; i < 16; i++) t[i] = n[i];\n  car25519(t);\n  car25519(t);\n  car25519(t);\n  for (j = 0; j < 2; j++) {\n    m[0] = t[0] - 0xffed;\n    for (i = 1; i < 15; i++) {\n      m[i] = t[i] - 0xffff - ((m[i-1]>>16) & 1);\n      m[i-1] &= 0xffff;\n    }\n    m[15] = t[15] - 0x7fff - ((m[14]>>16) & 1);\n    b = (m[15]>>16) & 1;\n    m[14] &= 0xffff;\n    sel25519(t, m, 1-b);\n  }\n  for (i = 0; i < 16; i++) {\n    o[2*i] = t[i] & 0xff;\n    o[2*i+1] = t[i]>>8;\n  }\n}\n\nfunction neq25519(a, b) {\n  var c = new Uint8Array(32), d = new Uint8Array(32);\n  pack25519(c, a);\n  pack25519(d, b);\n  return crypto_verify_32(c, 0, d, 0);\n}\n\nfunction par25519(a) {\n  var d = new Uint8Array(32);\n  pack25519(d, a);\n  return d[0] & 1;\n}\n\nfunction unpack25519(o, n) {\n  var i;\n  for (i = 0; i < 16; i++) o[i] = n[2*i] + (n[2*i+1] << 8);\n  o[15] &= 0x7fff;\n}\n\nfunction A(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] + b[i];\n}\n\nfunction Z(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] - b[i];\n}\n\nfunction M(o, a, b) {\n  var v, c,\n     t0 = 0,  t1 = 0,  t2 = 0,  t3 = 0,  t4 = 0,  t5 = 0,  t6 = 0,  t7 = 0,\n     t8 = 0,  t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0,\n    t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0,\n    t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0,\n    b0 = b[0],\n    b1 = b[1],\n    b2 = b[2],\n    b3 = b[3],\n    b4 = b[4],\n    b5 = b[5],\n    b6 = b[6],\n    b7 = b[7],\n    b8 = b[8],\n    b9 = b[9],\n    b10 = b[10],\n    b11 = b[11],\n    b12 = b[12],\n    b13 = b[13],\n    b14 = b[14],\n    b15 = b[15];\n\n  v = a[0];\n  t0 += v * b0;\n  t1 += v * b1;\n  t2 += v * b2;\n  t3 += v * b3;\n  t4 += v * b4;\n  t5 += v * b5;\n  t6 += v * b6;\n  t7 += v * b7;\n  t8 += v * b8;\n  t9 += v * b9;\n  t10 += v * b10;\n  t11 += v * b11;\n  t12 += v * b12;\n  t13 += v * b13;\n  t14 += v * b14;\n  t15 += v * b15;\n  v = a[1];\n  t1 += v * b0;\n  t2 += v * b1;\n  t3 += v * b2;\n  t4 += v * b3;\n  t5 += v * b4;\n  t6 += v * b5;\n  t7 += v * b6;\n  t8 += v * b7;\n  t9 += v * b8;\n  t10 += v * b9;\n  t11 += v * b10;\n  t12 += v * b11;\n  t13 += v * b12;\n  t14 += v * b13;\n  t15 += v * b14;\n  t16 += v * b15;\n  v = a[2];\n  t2 += v * b0;\n  t3 += v * b1;\n  t4 += v * b2;\n  t5 += v * b3;\n  t6 += v * b4;\n  t7 += v * b5;\n  t8 += v * b6;\n  t9 += v * b7;\n  t10 += v * b8;\n  t11 += v * b9;\n  t12 += v * b10;\n  t13 += v * b11;\n  t14 += v * b12;\n  t15 += v * b13;\n  t16 += v * b14;\n  t17 += v * b15;\n  v = a[3];\n  t3 += v * b0;\n  t4 += v * b1;\n  t5 += v * b2;\n  t6 += v * b3;\n  t7 += v * b4;\n  t8 += v * b5;\n  t9 += v * b6;\n  t10 += v * b7;\n  t11 += v * b8;\n  t12 += v * b9;\n  t13 += v * b10;\n  t14 += v * b11;\n  t15 += v * b12;\n  t16 += v * b13;\n  t17 += v * b14;\n  t18 += v * b15;\n  v = a[4];\n  t4 += v * b0;\n  t5 += v * b1;\n  t6 += v * b2;\n  t7 += v * b3;\n  t8 += v * b4;\n  t9 += v * b5;\n  t10 += v * b6;\n  t11 += v * b7;\n  t12 += v * b8;\n  t13 += v * b9;\n  t14 += v * b10;\n  t15 += v * b11;\n  t16 += v * b12;\n  t17 += v * b13;\n  t18 += v * b14;\n  t19 += v * b15;\n  v = a[5];\n  t5 += v * b0;\n  t6 += v * b1;\n  t7 += v * b2;\n  t8 += v * b3;\n  t9 += v * b4;\n  t10 += v * b5;\n  t11 += v * b6;\n  t12 += v * b7;\n  t13 += v * b8;\n  t14 += v * b9;\n  t15 += v * b10;\n  t16 += v * b11;\n  t17 += v * b12;\n  t18 += v * b13;\n  t19 += v * b14;\n  t20 += v * b15;\n  v = a[6];\n  t6 += v * b0;\n  t7 += v * b1;\n  t8 += v * b2;\n  t9 += v * b3;\n  t10 += v * b4;\n  t11 += v * b5;\n  t12 += v * b6;\n  t13 += v * b7;\n  t14 += v * b8;\n  t15 += v * b9;\n  t16 += v * b10;\n  t17 += v * b11;\n  t18 += v * b12;\n  t19 += v * b13;\n  t20 += v * b14;\n  t21 += v * b15;\n  v = a[7];\n  t7 += v * b0;\n  t8 += v * b1;\n  t9 += v * b2;\n  t10 += v * b3;\n  t11 += v * b4;\n  t12 += v * b5;\n  t13 += v * b6;\n  t14 += v * b7;\n  t15 += v * b8;\n  t16 += v * b9;\n  t17 += v * b10;\n  t18 += v * b11;\n  t19 += v * b12;\n  t20 += v * b13;\n  t21 += v * b14;\n  t22 += v * b15;\n  v = a[8];\n  t8 += v * b0;\n  t9 += v * b1;\n  t10 += v * b2;\n  t11 += v * b3;\n  t12 += v * b4;\n  t13 += v * b5;\n  t14 += v * b6;\n  t15 += v * b7;\n  t16 += v * b8;\n  t17 += v * b9;\n  t18 += v * b10;\n  t19 += v * b11;\n  t20 += v * b12;\n  t21 += v * b13;\n  t22 += v * b14;\n  t23 += v * b15;\n  v = a[9];\n  t9 += v * b0;\n  t10 += v * b1;\n  t11 += v * b2;\n  t12 += v * b3;\n  t13 += v * b4;\n  t14 += v * b5;\n  t15 += v * b6;\n  t16 += v * b7;\n  t17 += v * b8;\n  t18 += v * b9;\n  t19 += v * b10;\n  t20 += v * b11;\n  t21 += v * b12;\n  t22 += v * b13;\n  t23 += v * b14;\n  t24 += v * b15;\n  v = a[10];\n  t10 += v * b0;\n  t11 += v * b1;\n  t12 += v * b2;\n  t13 += v * b3;\n  t14 += v * b4;\n  t15 += v * b5;\n  t16 += v * b6;\n  t17 += v * b7;\n  t18 += v * b8;\n  t19 += v * b9;\n  t20 += v * b10;\n  t21 += v * b11;\n  t22 += v * b12;\n  t23 += v * b13;\n  t24 += v * b14;\n  t25 += v * b15;\n  v = a[11];\n  t11 += v * b0;\n  t12 += v * b1;\n  t13 += v * b2;\n  t14 += v * b3;\n  t15 += v * b4;\n  t16 += v * b5;\n  t17 += v * b6;\n  t18 += v * b7;\n  t19 += v * b8;\n  t20 += v * b9;\n  t21 += v * b10;\n  t22 += v * b11;\n  t23 += v * b12;\n  t24 += v * b13;\n  t25 += v * b14;\n  t26 += v * b15;\n  v = a[12];\n  t12 += v * b0;\n  t13 += v * b1;\n  t14 += v * b2;\n  t15 += v * b3;\n  t16 += v * b4;\n  t17 += v * b5;\n  t18 += v * b6;\n  t19 += v * b7;\n  t20 += v * b8;\n  t21 += v * b9;\n  t22 += v * b10;\n  t23 += v * b11;\n  t24 += v * b12;\n  t25 += v * b13;\n  t26 += v * b14;\n  t27 += v * b15;\n  v = a[13];\n  t13 += v * b0;\n  t14 += v * b1;\n  t15 += v * b2;\n  t16 += v * b3;\n  t17 += v * b4;\n  t18 += v * b5;\n  t19 += v * b6;\n  t20 += v * b7;\n  t21 += v * b8;\n  t22 += v * b9;\n  t23 += v * b10;\n  t24 += v * b11;\n  t25 += v * b12;\n  t26 += v * b13;\n  t27 += v * b14;\n  t28 += v * b15;\n  v = a[14];\n  t14 += v * b0;\n  t15 += v * b1;\n  t16 += v * b2;\n  t17 += v * b3;\n  t18 += v * b4;\n  t19 += v * b5;\n  t20 += v * b6;\n  t21 += v * b7;\n  t22 += v * b8;\n  t23 += v * b9;\n  t24 += v * b10;\n  t25 += v * b11;\n  t26 += v * b12;\n  t27 += v * b13;\n  t28 += v * b14;\n  t29 += v * b15;\n  v = a[15];\n  t15 += v * b0;\n  t16 += v * b1;\n  t17 += v * b2;\n  t18 += v * b3;\n  t19 += v * b4;\n  t20 += v * b5;\n  t21 += v * b6;\n  t22 += v * b7;\n  t23 += v * b8;\n  t24 += v * b9;\n  t25 += v * b10;\n  t26 += v * b11;\n  t27 += v * b12;\n  t28 += v * b13;\n  t29 += v * b14;\n  t30 += v * b15;\n\n  t0  += 38 * t16;\n  t1  += 38 * t17;\n  t2  += 38 * t18;\n  t3  += 38 * t19;\n  t4  += 38 * t20;\n  t5  += 38 * t21;\n  t6  += 38 * t22;\n  t7  += 38 * t23;\n  t8  += 38 * t24;\n  t9  += 38 * t25;\n  t10 += 38 * t26;\n  t11 += 38 * t27;\n  t12 += 38 * t28;\n  t13 += 38 * t29;\n  t14 += 38 * t30;\n  // t15 left as is\n\n  // first car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  // second car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  o[ 0] = t0;\n  o[ 1] = t1;\n  o[ 2] = t2;\n  o[ 3] = t3;\n  o[ 4] = t4;\n  o[ 5] = t5;\n  o[ 6] = t6;\n  o[ 7] = t7;\n  o[ 8] = t8;\n  o[ 9] = t9;\n  o[10] = t10;\n  o[11] = t11;\n  o[12] = t12;\n  o[13] = t13;\n  o[14] = t14;\n  o[15] = t15;\n}\n\nfunction S(o, a) {\n  M(o, a, a);\n}\n\nfunction inv25519(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 253; a >= 0; a--) {\n    S(c, c);\n    if(a !== 2 && a !== 4) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction pow2523(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 250; a >= 0; a--) {\n      S(c, c);\n      if(a !== 1) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction crypto_scalarmult(q, n, p) {\n  var z = new Uint8Array(32);\n  var x = new Float64Array(80), r, i;\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf();\n  for (i = 0; i < 31; i++) z[i] = n[i];\n  z[31]=(n[31]&127)|64;\n  z[0]&=248;\n  unpack25519(x,p);\n  for (i = 0; i < 16; i++) {\n    b[i]=x[i];\n    d[i]=a[i]=c[i]=0;\n  }\n  a[0]=d[0]=1;\n  for (i=254; i>=0; --i) {\n    r=(z[i>>>3]>>>(i&7))&1;\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n    A(e,a,c);\n    Z(a,a,c);\n    A(c,b,d);\n    Z(b,b,d);\n    S(d,e);\n    S(f,a);\n    M(a,c,a);\n    M(c,b,e);\n    A(e,a,c);\n    Z(a,a,c);\n    S(b,a);\n    Z(c,d,f);\n    M(a,c,_121665);\n    A(a,a,d);\n    M(c,c,a);\n    M(a,d,f);\n    M(d,b,x);\n    S(b,e);\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n  }\n  for (i = 0; i < 16; i++) {\n    x[i+16]=a[i];\n    x[i+32]=c[i];\n    x[i+48]=b[i];\n    x[i+64]=d[i];\n  }\n  var x32 = x.subarray(32);\n  var x16 = x.subarray(16);\n  inv25519(x32,x32);\n  M(x16,x16,x32);\n  pack25519(q,x16);\n  return 0;\n}\n\nfunction crypto_scalarmult_base(q, n) {\n  return crypto_scalarmult(q, n, _9);\n}\n\nfunction crypto_box_keypair(y, x) {\n  randombytes(x, 32);\n  return crypto_scalarmult_base(y, x);\n}\n\nfunction crypto_box_beforenm(k, y, x) {\n  var s = new Uint8Array(32);\n  crypto_scalarmult(s, x, y);\n  return crypto_core_hsalsa20(k, _0, s, sigma);\n}\n\nvar crypto_box_afternm = crypto_secretbox;\nvar crypto_box_open_afternm = crypto_secretbox_open;\n\nfunction crypto_box(c, m, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_afternm(c, m, d, n, k);\n}\n\nfunction crypto_box_open(m, c, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_open_afternm(m, c, d, n, k);\n}\n\nvar K = [\n  0x428a2f98, 0xd728ae22, 0x71374491, 0x23ef65cd,\n  0xb5c0fbcf, 0xec4d3b2f, 0xe9b5dba5, 0x8189dbbc,\n  0x3956c25b, 0xf348b538, 0x59f111f1, 0xb605d019,\n  0x923f82a4, 0xaf194f9b, 0xab1c5ed5, 0xda6d8118,\n  0xd807aa98, 0xa3030242, 0x12835b01, 0x45706fbe,\n  0x243185be, 0x4ee4b28c, 0x550c7dc3, 0xd5ffb4e2,\n  0x72be5d74, 0xf27b896f, 0x80deb1fe, 0x3b1696b1,\n  0x9bdc06a7, 0x25c71235, 0xc19bf174, 0xcf692694,\n  0xe49b69c1, 0x9ef14ad2, 0xefbe4786, 0x384f25e3,\n  0x0fc19dc6, 0x8b8cd5b5, 0x240ca1cc, 0x77ac9c65,\n  0x2de92c6f, 0x592b0275, 0x4a7484aa, 0x6ea6e483,\n  0x5cb0a9dc, 0xbd41fbd4, 0x76f988da, 0x831153b5,\n  0x983e5152, 0xee66dfab, 0xa831c66d, 0x2db43210,\n  0xb00327c8, 0x98fb213f, 0xbf597fc7, 0xbeef0ee4,\n  0xc6e00bf3, 0x3da88fc2, 0xd5a79147, 0x930aa725,\n  0x06ca6351, 0xe003826f, 0x14292967, 0x0a0e6e70,\n  0x27b70a85, 0x46d22ffc, 0x2e1b2138, 0x5c26c926,\n  0x4d2c6dfc, 0x5ac42aed, 0x53380d13, 0x9d95b3df,\n  0x650a7354, 0x8baf63de, 0x766a0abb, 0x3c77b2a8,\n  0x81c2c92e, 0x47edaee6, 0x92722c85, 0x1482353b,\n  0xa2bfe8a1, 0x4cf10364, 0xa81a664b, 0xbc423001,\n  0xc24b8b70, 0xd0f89791, 0xc76c51a3, 0x0654be30,\n  0xd192e819, 0xd6ef5218, 0xd6990624, 0x5565a910,\n  0xf40e3585, 0x5771202a, 0x106aa070, 0x32bbd1b8,\n  0x19a4c116, 0xb8d2d0c8, 0x1e376c08, 0x5141ab53,\n  0x2748774c, 0xdf8eeb99, 0x34b0bcb5, 0xe19b48a8,\n  0x391c0cb3, 0xc5c95a63, 0x4ed8aa4a, 0xe3418acb,\n  0x5b9cca4f, 0x7763e373, 0x682e6ff3, 0xd6b2b8a3,\n  0x748f82ee, 0x5defb2fc, 0x78a5636f, 0x43172f60,\n  0x84c87814, 0xa1f0ab72, 0x8cc70208, 0x1a6439ec,\n  0x90befffa, 0x23631e28, 0xa4506ceb, 0xde82bde9,\n  0xbef9a3f7, 0xb2c67915, 0xc67178f2, 0xe372532b,\n  0xca273ece, 0xea26619c, 0xd186b8c7, 0x21c0c207,\n  0xeada7dd6, 0xcde0eb1e, 0xf57d4f7f, 0xee6ed178,\n  0x06f067aa, 0x72176fba, 0x0a637dc5, 0xa2c898a6,\n  0x113f9804, 0xbef90dae, 0x1b710b35, 0x131c471b,\n  0x28db77f5, 0x23047d84, 0x32caab7b, 0x40c72493,\n  0x3c9ebe0a, 0x15c9bebc, 0x431d67c4, 0x9c100d4c,\n  0x4cc5d4be, 0xcb3e42b6, 0x597f299c, 0xfc657e2a,\n  0x5fcb6fab, 0x3ad6faec, 0x6c44198c, 0x4a475817\n];\n\nfunction crypto_hashblocks_hl(hh, hl, m, n) {\n  var wh = new Int32Array(16), wl = new Int32Array(16),\n      bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7,\n      bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7,\n      th, tl, i, j, h, l, a, b, c, d;\n\n  var ah0 = hh[0],\n      ah1 = hh[1],\n      ah2 = hh[2],\n      ah3 = hh[3],\n      ah4 = hh[4],\n      ah5 = hh[5],\n      ah6 = hh[6],\n      ah7 = hh[7],\n\n      al0 = hl[0],\n      al1 = hl[1],\n      al2 = hl[2],\n      al3 = hl[3],\n      al4 = hl[4],\n      al5 = hl[5],\n      al6 = hl[6],\n      al7 = hl[7];\n\n  var pos = 0;\n  while (n >= 128) {\n    for (i = 0; i < 16; i++) {\n      j = 8 * i + pos;\n      wh[i] = (m[j+0] << 24) | (m[j+1] << 16) | (m[j+2] << 8) | m[j+3];\n      wl[i] = (m[j+4] << 24) | (m[j+5] << 16) | (m[j+6] << 8) | m[j+7];\n    }\n    for (i = 0; i < 80; i++) {\n      bh0 = ah0;\n      bh1 = ah1;\n      bh2 = ah2;\n      bh3 = ah3;\n      bh4 = ah4;\n      bh5 = ah5;\n      bh6 = ah6;\n      bh7 = ah7;\n\n      bl0 = al0;\n      bl1 = al1;\n      bl2 = al2;\n      bl3 = al3;\n      bl4 = al4;\n      bl5 = al5;\n      bl6 = al6;\n      bl7 = al7;\n\n      // add\n      h = ah7;\n      l = al7;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma1\n      h = ((ah4 >>> 14) | (al4 << (32-14))) ^ ((ah4 >>> 18) | (al4 << (32-18))) ^ ((al4 >>> (41-32)) | (ah4 << (32-(41-32))));\n      l = ((al4 >>> 14) | (ah4 << (32-14))) ^ ((al4 >>> 18) | (ah4 << (32-18))) ^ ((ah4 >>> (41-32)) | (al4 << (32-(41-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Ch\n      h = (ah4 & ah5) ^ (~ah4 & ah6);\n      l = (al4 & al5) ^ (~al4 & al6);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // K\n      h = K[i*2];\n      l = K[i*2+1];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // w\n      h = wh[i%16];\n      l = wl[i%16];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      th = c & 0xffff | d << 16;\n      tl = a & 0xffff | b << 16;\n\n      // add\n      h = th;\n      l = tl;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma0\n      h = ((ah0 >>> 28) | (al0 << (32-28))) ^ ((al0 >>> (34-32)) | (ah0 << (32-(34-32)))) ^ ((al0 >>> (39-32)) | (ah0 << (32-(39-32))));\n      l = ((al0 >>> 28) | (ah0 << (32-28))) ^ ((ah0 >>> (34-32)) | (al0 << (32-(34-32)))) ^ ((ah0 >>> (39-32)) | (al0 << (32-(39-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Maj\n      h = (ah0 & ah1) ^ (ah0 & ah2) ^ (ah1 & ah2);\n      l = (al0 & al1) ^ (al0 & al2) ^ (al1 & al2);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh7 = (c & 0xffff) | (d << 16);\n      bl7 = (a & 0xffff) | (b << 16);\n\n      // add\n      h = bh3;\n      l = bl3;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      h = th;\n      l = tl;\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh3 = (c & 0xffff) | (d << 16);\n      bl3 = (a & 0xffff) | (b << 16);\n\n      ah1 = bh0;\n      ah2 = bh1;\n      ah3 = bh2;\n      ah4 = bh3;\n      ah5 = bh4;\n      ah6 = bh5;\n      ah7 = bh6;\n      ah0 = bh7;\n\n      al1 = bl0;\n      al2 = bl1;\n      al3 = bl2;\n      al4 = bl3;\n      al5 = bl4;\n      al6 = bl5;\n      al7 = bl6;\n      al0 = bl7;\n\n      if (i%16 === 15) {\n        for (j = 0; j < 16; j++) {\n          // add\n          h = wh[j];\n          l = wl[j];\n\n          a = l & 0xffff; b = l >>> 16;\n          c = h & 0xffff; d = h >>> 16;\n\n          h = wh[(j+9)%16];\n          l = wl[(j+9)%16];\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma0\n          th = wh[(j+1)%16];\n          tl = wl[(j+1)%16];\n          h = ((th >>> 1) | (tl << (32-1))) ^ ((th >>> 8) | (tl << (32-8))) ^ (th >>> 7);\n          l = ((tl >>> 1) | (th << (32-1))) ^ ((tl >>> 8) | (th << (32-8))) ^ ((tl >>> 7) | (th << (32-7)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma1\n          th = wh[(j+14)%16];\n          tl = wl[(j+14)%16];\n          h = ((th >>> 19) | (tl << (32-19))) ^ ((tl >>> (61-32)) | (th << (32-(61-32)))) ^ (th >>> 6);\n          l = ((tl >>> 19) | (th << (32-19))) ^ ((th >>> (61-32)) | (tl << (32-(61-32)))) ^ ((tl >>> 6) | (th << (32-6)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          b += a >>> 16;\n          c += b >>> 16;\n          d += c >>> 16;\n\n          wh[j] = (c & 0xffff) | (d << 16);\n          wl[j] = (a & 0xffff) | (b << 16);\n        }\n      }\n    }\n\n    // add\n    h = ah0;\n    l = al0;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[0];\n    l = hl[0];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[0] = ah0 = (c & 0xffff) | (d << 16);\n    hl[0] = al0 = (a & 0xffff) | (b << 16);\n\n    h = ah1;\n    l = al1;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[1];\n    l = hl[1];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[1] = ah1 = (c & 0xffff) | (d << 16);\n    hl[1] = al1 = (a & 0xffff) | (b << 16);\n\n    h = ah2;\n    l = al2;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[2];\n    l = hl[2];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[2] = ah2 = (c & 0xffff) | (d << 16);\n    hl[2] = al2 = (a & 0xffff) | (b << 16);\n\n    h = ah3;\n    l = al3;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[3];\n    l = hl[3];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[3] = ah3 = (c & 0xffff) | (d << 16);\n    hl[3] = al3 = (a & 0xffff) | (b << 16);\n\n    h = ah4;\n    l = al4;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[4];\n    l = hl[4];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[4] = ah4 = (c & 0xffff) | (d << 16);\n    hl[4] = al4 = (a & 0xffff) | (b << 16);\n\n    h = ah5;\n    l = al5;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[5];\n    l = hl[5];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[5] = ah5 = (c & 0xffff) | (d << 16);\n    hl[5] = al5 = (a & 0xffff) | (b << 16);\n\n    h = ah6;\n    l = al6;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[6];\n    l = hl[6];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[6] = ah6 = (c & 0xffff) | (d << 16);\n    hl[6] = al6 = (a & 0xffff) | (b << 16);\n\n    h = ah7;\n    l = al7;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[7];\n    l = hl[7];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[7] = ah7 = (c & 0xffff) | (d << 16);\n    hl[7] = al7 = (a & 0xffff) | (b << 16);\n\n    pos += 128;\n    n -= 128;\n  }\n\n  return n;\n}\n\nfunction crypto_hash(out, m, n) {\n  var hh = new Int32Array(8),\n      hl = new Int32Array(8),\n      x = new Uint8Array(256),\n      i, b = n;\n\n  hh[0] = 0x6a09e667;\n  hh[1] = 0xbb67ae85;\n  hh[2] = 0x3c6ef372;\n  hh[3] = 0xa54ff53a;\n  hh[4] = 0x510e527f;\n  hh[5] = 0x9b05688c;\n  hh[6] = 0x1f83d9ab;\n  hh[7] = 0x5be0cd19;\n\n  hl[0] = 0xf3bcc908;\n  hl[1] = 0x84caa73b;\n  hl[2] = 0xfe94f82b;\n  hl[3] = 0x5f1d36f1;\n  hl[4] = 0xade682d1;\n  hl[5] = 0x2b3e6c1f;\n  hl[6] = 0xfb41bd6b;\n  hl[7] = 0x137e2179;\n\n  crypto_hashblocks_hl(hh, hl, m, n);\n  n %= 128;\n\n  for (i = 0; i < n; i++) x[i] = m[b-n+i];\n  x[n] = 128;\n\n  n = 256-128*(n<112?1:0);\n  x[n-9] = 0;\n  ts64(x, n-8,  (b / 0x20000000) | 0, b << 3);\n  crypto_hashblocks_hl(hh, hl, x, n);\n\n  for (i = 0; i < 8; i++) ts64(out, 8*i, hh[i], hl[i]);\n\n  return 0;\n}\n\nfunction add(p, q) {\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf(),\n      g = gf(), h = gf(), t = gf();\n\n  Z(a, p[1], p[0]);\n  Z(t, q[1], q[0]);\n  M(a, a, t);\n  A(b, p[0], p[1]);\n  A(t, q[0], q[1]);\n  M(b, b, t);\n  M(c, p[3], q[3]);\n  M(c, c, D2);\n  M(d, p[2], q[2]);\n  A(d, d, d);\n  Z(e, b, a);\n  Z(f, d, c);\n  A(g, d, c);\n  A(h, b, a);\n\n  M(p[0], e, f);\n  M(p[1], h, g);\n  M(p[2], g, f);\n  M(p[3], e, h);\n}\n\nfunction cswap(p, q, b) {\n  var i;\n  for (i = 0; i < 4; i++) {\n    sel25519(p[i], q[i], b);\n  }\n}\n\nfunction pack(r, p) {\n  var tx = gf(), ty = gf(), zi = gf();\n  inv25519(zi, p[2]);\n  M(tx, p[0], zi);\n  M(ty, p[1], zi);\n  pack25519(r, ty);\n  r[31] ^= par25519(tx) << 7;\n}\n\nfunction scalarmult(p, q, s) {\n  var b, i;\n  set25519(p[0], gf0);\n  set25519(p[1], gf1);\n  set25519(p[2], gf1);\n  set25519(p[3], gf0);\n  for (i = 255; i >= 0; --i) {\n    b = (s[(i/8)|0] >> (i&7)) & 1;\n    cswap(p, q, b);\n    add(q, p);\n    add(p, p);\n    cswap(p, q, b);\n  }\n}\n\nfunction scalarbase(p, s) {\n  var q = [gf(), gf(), gf(), gf()];\n  set25519(q[0], X);\n  set25519(q[1], Y);\n  set25519(q[2], gf1);\n  M(q[3], X, Y);\n  scalarmult(p, q, s);\n}\n\nfunction crypto_sign_keypair(pk, sk, seeded) {\n  var d = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n  var i;\n\n  if (!seeded) randombytes(sk, 32);\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  scalarbase(p, d);\n  pack(pk, p);\n\n  for (i = 0; i < 32; i++) sk[i+32] = pk[i];\n  return 0;\n}\n\nvar L = new Float64Array([0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0x10]);\n\nfunction modL(r, x) {\n  var carry, i, j, k;\n  for (i = 63; i >= 32; --i) {\n    carry = 0;\n    for (j = i - 32, k = i - 12; j < k; ++j) {\n      x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n      carry = Math.floor((x[j] + 128) / 256);\n      x[j] -= carry * 256;\n    }\n    x[j] += carry;\n    x[i] = 0;\n  }\n  carry = 0;\n  for (j = 0; j < 32; j++) {\n    x[j] += carry - (x[31] >> 4) * L[j];\n    carry = x[j] >> 8;\n    x[j] &= 255;\n  }\n  for (j = 0; j < 32; j++) x[j] -= carry * L[j];\n  for (i = 0; i < 32; i++) {\n    x[i+1] += x[i] >> 8;\n    r[i] = x[i] & 255;\n  }\n}\n\nfunction reduce(r) {\n  var x = new Float64Array(64), i;\n  for (i = 0; i < 64; i++) x[i] = r[i];\n  for (i = 0; i < 64; i++) r[i] = 0;\n  modL(r, x);\n}\n\n// Note: difference from C - smlen returned, not passed as argument.\nfunction crypto_sign(sm, m, n, sk) {\n  var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);\n  var i, j, x = new Float64Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  var smlen = n + 64;\n  for (i = 0; i < n; i++) sm[64 + i] = m[i];\n  for (i = 0; i < 32; i++) sm[32 + i] = d[32 + i];\n\n  crypto_hash(r, sm.subarray(32), n+32);\n  reduce(r);\n  scalarbase(p, r);\n  pack(sm, p);\n\n  for (i = 32; i < 64; i++) sm[i] = sk[i];\n  crypto_hash(h, sm, n + 64);\n  reduce(h);\n\n  for (i = 0; i < 64; i++) x[i] = 0;\n  for (i = 0; i < 32; i++) x[i] = r[i];\n  for (i = 0; i < 32; i++) {\n    for (j = 0; j < 32; j++) {\n      x[i+j] += h[i] * d[j];\n    }\n  }\n\n  modL(sm.subarray(32), x);\n  return smlen;\n}\n\nfunction unpackneg(r, p) {\n  var t = gf(), chk = gf(), num = gf(),\n      den = gf(), den2 = gf(), den4 = gf(),\n      den6 = gf();\n\n  set25519(r[2], gf1);\n  unpack25519(r[1], p);\n  S(num, r[1]);\n  M(den, num, D);\n  Z(num, num, r[2]);\n  A(den, r[2], den);\n\n  S(den2, den);\n  S(den4, den2);\n  M(den6, den4, den2);\n  M(t, den6, num);\n  M(t, t, den);\n\n  pow2523(t, t);\n  M(t, t, num);\n  M(t, t, den);\n  M(t, t, den);\n  M(r[0], t, den);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) M(r[0], r[0], I);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) return -1;\n\n  if (par25519(r[0]) === (p[31]>>7)) Z(r[0], gf0, r[0]);\n\n  M(r[3], r[0], r[1]);\n  return 0;\n}\n\nfunction crypto_sign_open(m, sm, n, pk) {\n  var i;\n  var t = new Uint8Array(32), h = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()],\n      q = [gf(), gf(), gf(), gf()];\n\n  if (n < 64) return -1;\n\n  if (unpackneg(q, pk)) return -1;\n\n  for (i = 0; i < n; i++) m[i] = sm[i];\n  for (i = 0; i < 32; i++) m[i+32] = pk[i];\n  crypto_hash(h, m, n);\n  reduce(h);\n  scalarmult(p, q, h);\n\n  scalarbase(q, sm.subarray(32));\n  add(p, q);\n  pack(t, p);\n\n  n -= 64;\n  if (crypto_verify_32(sm, 0, t, 0)) {\n    for (i = 0; i < n; i++) m[i] = 0;\n    return -1;\n  }\n\n  for (i = 0; i < n; i++) m[i] = sm[i + 64];\n  return n;\n}\n\nvar crypto_secretbox_KEYBYTES = 32,\n    crypto_secretbox_NONCEBYTES = 24,\n    crypto_secretbox_ZEROBYTES = 32,\n    crypto_secretbox_BOXZEROBYTES = 16,\n    crypto_scalarmult_BYTES = 32,\n    crypto_scalarmult_SCALARBYTES = 32,\n    crypto_box_PUBLICKEYBYTES = 32,\n    crypto_box_SECRETKEYBYTES = 32,\n    crypto_box_BEFORENMBYTES = 32,\n    crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES,\n    crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES,\n    crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES,\n    crypto_sign_BYTES = 64,\n    crypto_sign_PUBLICKEYBYTES = 32,\n    crypto_sign_SECRETKEYBYTES = 64,\n    crypto_sign_SEEDBYTES = 32,\n    crypto_hash_BYTES = 64;\n\nnacl.lowlevel = {\n  crypto_core_hsalsa20: crypto_core_hsalsa20,\n  crypto_stream_xor: crypto_stream_xor,\n  crypto_stream: crypto_stream,\n  crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n  crypto_stream_salsa20: crypto_stream_salsa20,\n  crypto_onetimeauth: crypto_onetimeauth,\n  crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n  crypto_verify_16: crypto_verify_16,\n  crypto_verify_32: crypto_verify_32,\n  crypto_secretbox: crypto_secretbox,\n  crypto_secretbox_open: crypto_secretbox_open,\n  crypto_scalarmult: crypto_scalarmult,\n  crypto_scalarmult_base: crypto_scalarmult_base,\n  crypto_box_beforenm: crypto_box_beforenm,\n  crypto_box_afternm: crypto_box_afternm,\n  crypto_box: crypto_box,\n  crypto_box_open: crypto_box_open,\n  crypto_box_keypair: crypto_box_keypair,\n  crypto_hash: crypto_hash,\n  crypto_sign: crypto_sign,\n  crypto_sign_keypair: crypto_sign_keypair,\n  crypto_sign_open: crypto_sign_open,\n\n  crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n  crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n  crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n  crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n  crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n  crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n  crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n  crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n  crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n  crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n  crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n  crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n  crypto_sign_BYTES: crypto_sign_BYTES,\n  crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n  crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n  crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n  crypto_hash_BYTES: crypto_hash_BYTES,\n\n  gf: gf,\n  D: D,\n  L: L,\n  pack25519: pack25519,\n  unpack25519: unpack25519,\n  M: M,\n  A: A,\n  S: S,\n  Z: Z,\n  pow2523: pow2523,\n  add: add,\n  set25519: set25519,\n  modL: modL,\n  scalarmult: scalarmult,\n  scalarbase: scalarbase,\n};\n\n/* High-level API */\n\nfunction checkLengths(k, n) {\n  if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');\n  if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');\n}\n\nfunction checkBoxLengths(pk, sk) {\n  if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');\n  if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n}\n\nfunction checkArrayTypes() {\n  for (var i = 0; i < arguments.length; i++) {\n    if (!(arguments[i] instanceof Uint8Array))\n      throw new TypeError('unexpected type, use Uint8Array');\n  }\n}\n\nfunction cleanup(arr) {\n  for (var i = 0; i < arr.length; i++) arr[i] = 0;\n}\n\nnacl.randomBytes = function(n) {\n  var b = new Uint8Array(n);\n  randombytes(b, n);\n  return b;\n};\n\nnacl.secretbox = function(msg, nonce, key) {\n  checkArrayTypes(msg, nonce, key);\n  checkLengths(key, nonce);\n  var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n  var c = new Uint8Array(m.length);\n  for (var i = 0; i < msg.length; i++) m[i+crypto_secretbox_ZEROBYTES] = msg[i];\n  crypto_secretbox(c, m, m.length, nonce, key);\n  return c.subarray(crypto_secretbox_BOXZEROBYTES);\n};\n\nnacl.secretbox.open = function(box, nonce, key) {\n  checkArrayTypes(box, nonce, key);\n  checkLengths(key, nonce);\n  var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n  var m = new Uint8Array(c.length);\n  for (var i = 0; i < box.length; i++) c[i+crypto_secretbox_BOXZEROBYTES] = box[i];\n  if (c.length < 32) return null;\n  if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n  return m.subarray(crypto_secretbox_ZEROBYTES);\n};\n\nnacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\nnacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\nnacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n\nnacl.scalarMult = function(n, p) {\n  checkArrayTypes(n, p);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult(q, n, p);\n  return q;\n};\n\nnacl.scalarMult.base = function(n) {\n  checkArrayTypes(n);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult_base(q, n);\n  return q;\n};\n\nnacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\nnacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n\nnacl.box = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox(msg, nonce, k);\n};\n\nnacl.box.before = function(publicKey, secretKey) {\n  checkArrayTypes(publicKey, secretKey);\n  checkBoxLengths(publicKey, secretKey);\n  var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n  crypto_box_beforenm(k, publicKey, secretKey);\n  return k;\n};\n\nnacl.box.after = nacl.secretbox;\n\nnacl.box.open = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox.open(msg, nonce, k);\n};\n\nnacl.box.open.after = nacl.secretbox.open;\n\nnacl.box.keyPair = function() {\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n  crypto_box_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.box.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_box_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  crypto_scalarmult_base(pk, secretKey);\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\nnacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\nnacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\nnacl.box.nonceLength = crypto_box_NONCEBYTES;\nnacl.box.overheadLength = nacl.secretbox.overheadLength;\n\nnacl.sign = function(msg, secretKey) {\n  checkArrayTypes(msg, secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var signedMsg = new Uint8Array(crypto_sign_BYTES+msg.length);\n  crypto_sign(signedMsg, msg, msg.length, secretKey);\n  return signedMsg;\n};\n\nnacl.sign.open = function(signedMsg, publicKey) {\n  checkArrayTypes(signedMsg, publicKey);\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var tmp = new Uint8Array(signedMsg.length);\n  var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n  if (mlen < 0) return null;\n  var m = new Uint8Array(mlen);\n  for (var i = 0; i < m.length; i++) m[i] = tmp[i];\n  return m;\n};\n\nnacl.sign.detached = function(msg, secretKey) {\n  var signedMsg = nacl.sign(msg, secretKey);\n  var sig = new Uint8Array(crypto_sign_BYTES);\n  for (var i = 0; i < sig.length; i++) sig[i] = signedMsg[i];\n  return sig;\n};\n\nnacl.sign.detached.verify = function(msg, sig, publicKey) {\n  checkArrayTypes(msg, sig, publicKey);\n  if (sig.length !== crypto_sign_BYTES)\n    throw new Error('bad signature size');\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var i;\n  for (i = 0; i < crypto_sign_BYTES; i++) sm[i] = sig[i];\n  for (i = 0; i < msg.length; i++) sm[i+crypto_sign_BYTES] = msg[i];\n  return (crypto_sign_open(m, sm, sm.length, publicKey) >= 0);\n};\n\nnacl.sign.keyPair = function() {\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  crypto_sign_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  for (var i = 0; i < pk.length; i++) pk[i] = secretKey[32+i];\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.sign.keyPair.fromSeed = function(seed) {\n  checkArrayTypes(seed);\n  if (seed.length !== crypto_sign_SEEDBYTES)\n    throw new Error('bad seed size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  for (var i = 0; i < 32; i++) sk[i] = seed[i];\n  crypto_sign_keypair(pk, sk, true);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\nnacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\nnacl.sign.seedLength = crypto_sign_SEEDBYTES;\nnacl.sign.signatureLength = crypto_sign_BYTES;\n\nnacl.hash = function(msg) {\n  checkArrayTypes(msg);\n  var h = new Uint8Array(crypto_hash_BYTES);\n  crypto_hash(h, msg, msg.length);\n  return h;\n};\n\nnacl.hash.hashLength = crypto_hash_BYTES;\n\nnacl.verify = function(x, y) {\n  checkArrayTypes(x, y);\n  // Zero length arguments are considered not equal.\n  if (x.length === 0 || y.length === 0) return false;\n  if (x.length !== y.length) return false;\n  return (vn(x, 0, y, 0, x.length) === 0) ? true : false;\n};\n\nnacl.setPRNG = function(fn) {\n  randombytes = fn;\n};\n\n(function() {\n  // Initialize PRNG if environment provides CSPRNG.\n  // If not, methods calling randombytes will throw.\n  var crypto = typeof self !== 'undefined' ? (self.crypto || self.msCrypto) : null;\n  if (crypto && crypto.getRandomValues) {\n    // Browsers.\n    var QUOTA = 65536;\n    nacl.setPRNG(function(x, n) {\n      var i, v = new Uint8Array(n);\n      for (i = 0; i < n; i += QUOTA) {\n        crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n      }\n      for (i = 0; i < n; i++) x[i] = v[i];\n      cleanup(v);\n    });\n  } else if (typeof require !== 'undefined') {\n    // Node.js.\n    crypto = require('crypto');\n    if (crypto && crypto.randomBytes) {\n      nacl.setPRNG(function(x, n) {\n        var i, v = crypto.randomBytes(n);\n        for (i = 0; i < n; i++) x[i] = v[i];\n        cleanup(v);\n      });\n    }\n  }\n})();\n\n})(typeof module !== 'undefined' && module.exports ? module.exports : (self.nacl = self.nacl || {}));\n"], "names": [], "mappings": "AAAA,CAAC,SAAS,IAAI;IACd;IAEA,uDAAuD;IACvD,iBAAiB;IACjB,EAAE;IACF,0DAA0D;IAC1D,8CAA8C;IAE9C,IAAI,KAAK,SAAS,IAAI;QACpB,IAAI,GAAG,IAAI,IAAI,aAAa;QAC5B,IAAI,MAAM,IAAK,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QAC1D,OAAO;IACT;IAEA,mDAAmD;IACnD,IAAI,cAAc;QAAuB,MAAM,IAAI,MAAM;IAAY;IAErE,IAAI,KAAK,IAAI,WAAW;IACxB,IAAI,KAAK,IAAI,WAAW;IAAK,EAAE,CAAC,EAAE,GAAG;IAErC,IAAI,MAAM,MACN,MAAM,GAAG;QAAC;KAAE,GACZ,UAAU,GAAG;QAAC;QAAQ;KAAE,GACxB,IAAI,GAAG;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO,GACvI,KAAK,GAAG;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO,GACxI,IAAI,GAAG;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO,GACvI,IAAI,GAAG;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO,GACvI,IAAI,GAAG;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAE3I,SAAS,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACtB,CAAC,CAAC,EAAE,GAAK,AAAC,KAAK,KAAM;QACrB,CAAC,CAAC,IAAE,EAAE,GAAG,AAAC,KAAK,KAAM;QACrB,CAAC,CAAC,IAAE,EAAE,GAAG,AAAC,KAAM,IAAK;QACrB,CAAC,CAAC,IAAE,EAAE,GAAG,IAAI;QACb,CAAC,CAAC,IAAE,EAAE,GAAG,AAAC,KAAK,KAAO;QACtB,CAAC,CAAC,IAAE,EAAE,GAAG,AAAC,KAAK,KAAO;QACtB,CAAC,CAAC,IAAE,EAAE,GAAG,AAAC,KAAM,IAAM;QACtB,CAAC,CAAC,IAAE,EAAE,GAAG,IAAI;IACf;IAEA,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;QACzB,IAAI,GAAE,IAAI;QACV,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,KAAK,CAAC,CAAC,KAAG,EAAE,GAAC,CAAC,CAAC,KAAG,EAAE;QAC5C,OAAO,CAAC,IAAK,AAAC,IAAI,MAAO,CAAE,IAAI;IACjC;IAEA,SAAS,iBAAiB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACpC,OAAO,GAAG,GAAE,IAAG,GAAE,IAAG;IACtB;IAEA,SAAS,iBAAiB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACpC,OAAO,GAAG,GAAE,IAAG,GAAE,IAAG;IACtB;IAEA,SAAS,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAC9B,IAAI,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG;QAElF,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IACpE,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KACpE,MAAM,KAAK;QAEf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,EAAG;YAC9B,IAAI,KAAK,MAAM;YACf,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,OAAO,KAAG,KAAK,MAAK,KAAG;YACvB,IAAI,MAAM,KAAK;YACf,MAAM,KAAG,KAAK,MAAK,KAAG;YAEtB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,OAAO,KAAG,IAAI,MAAK,KAAG;YACtB,IAAI,MAAM,KAAK;YACf,MAAM,KAAG,KAAK,MAAK,KAAG;YACtB,IAAI,KAAK,MAAM;YACf,MAAM,KAAG,KAAK,MAAK,KAAG;YAEtB,IAAI,MAAM,KAAK;YACf,OAAO,KAAG,IAAI,MAAK,KAAG;YACtB,IAAI,MAAM,MAAM;YAChB,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,MAAM;YACf,MAAM,KAAG,KAAK,MAAK,KAAG;YACtB,IAAI,KAAK,KAAK;YACd,OAAO,KAAG,KAAK,MAAK,KAAG;YAEvB,IAAI,MAAM,MAAM;YAChB,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,MAAM;YACf,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,OAAO,KAAG,KAAK,MAAK,KAAG;YACvB,IAAI,MAAM,KAAK;YACf,OAAO,KAAG,KAAK,MAAK,KAAG;YAEvB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,KAAK,MAAK,KAAG;YACtB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,KAAK,MAAK,KAAG;YAEtB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,KAAK,MAAK,KAAG;YACtB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,KAAK,MAAK,KAAG;YAEtB,IAAI,MAAM,KAAK;YACf,OAAO,KAAG,IAAI,MAAK,KAAG;YACtB,IAAI,MAAM,MAAM;YAChB,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,MAAM;YACf,MAAM,KAAG,KAAK,MAAK,KAAG;YACtB,IAAI,KAAK,KAAK;YACd,OAAO,KAAG,KAAK,MAAK,KAAG;YAEvB,IAAI,MAAM,MAAM;YAChB,OAAO,KAAG,IAAI,MAAK,KAAG;YACtB,IAAI,MAAM,MAAM;YAChB,OAAO,KAAG,IAAI,MAAK,KAAG;YACtB,IAAI,MAAM,MAAM;YAChB,OAAO,KAAG,KAAK,MAAK,KAAG;YACvB,IAAI,MAAM,MAAM;YAChB,OAAO,KAAG,KAAK,MAAK,KAAG;QACzB;QACC,KAAM,KAAM,KAAK;QACjB,KAAM,KAAM,KAAK;QACjB,KAAM,KAAM,KAAK;QACjB,KAAM,KAAM,KAAK;QACjB,KAAM,KAAM,KAAK;QACjB,KAAM,KAAM,KAAK;QACjB,KAAM,KAAM,KAAK;QACjB,KAAM,KAAM,KAAK;QACjB,KAAM,KAAM,KAAK;QACjB,KAAM,KAAM,KAAK;QAClB,MAAM,MAAM,MAAM;QAClB,MAAM,MAAM,MAAM;QAClB,MAAM,MAAM,MAAM;QAClB,MAAM,MAAM,MAAM;QAClB,MAAM,MAAM,MAAM;QAClB,MAAM,MAAM,MAAM;QAElB,CAAC,CAAE,EAAE,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAE,EAAE,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAE,EAAE,GAAG,OAAO,KAAK;QACpB,CAAC,CAAE,EAAE,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAE,EAAE,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAE,EAAE,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAE,EAAE,GAAG,OAAO,KAAK;QACpB,CAAC,CAAE,EAAE,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAE,EAAE,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAE,EAAE,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QAErB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QAErB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QAErB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QAErB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QAErB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;IACvB;IAEA,SAAS,cAAc,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QAC5B,IAAI,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,KAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAE,EAAE,GAAG,OAAO,CAAC,CAAC,CAAE,EAAE,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAC9E,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAG;QAElF,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IACpE,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KACpE,MAAM,KAAK;QAEf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,EAAG;YAC9B,IAAI,KAAK,MAAM;YACf,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,OAAO,KAAG,KAAK,MAAK,KAAG;YACvB,IAAI,MAAM,KAAK;YACf,MAAM,KAAG,KAAK,MAAK,KAAG;YAEtB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,OAAO,KAAG,IAAI,MAAK,KAAG;YACtB,IAAI,MAAM,KAAK;YACf,MAAM,KAAG,KAAK,MAAK,KAAG;YACtB,IAAI,KAAK,MAAM;YACf,MAAM,KAAG,KAAK,MAAK,KAAG;YAEtB,IAAI,MAAM,KAAK;YACf,OAAO,KAAG,IAAI,MAAK,KAAG;YACtB,IAAI,MAAM,MAAM;YAChB,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,MAAM;YACf,MAAM,KAAG,KAAK,MAAK,KAAG;YACtB,IAAI,KAAK,KAAK;YACd,OAAO,KAAG,KAAK,MAAK,KAAG;YAEvB,IAAI,MAAM,MAAM;YAChB,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,MAAM;YACf,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,OAAO,KAAG,KAAK,MAAK,KAAG;YACvB,IAAI,MAAM,KAAK;YACf,OAAO,KAAG,KAAK,MAAK,KAAG;YAEvB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,KAAK,MAAK,KAAG;YACtB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,KAAK,MAAK,KAAG;YAEtB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,KAAK,MAAK,KAAG;YACtB,IAAI,KAAK,KAAK;YACd,MAAM,KAAG,KAAK,MAAK,KAAG;YAEtB,IAAI,MAAM,KAAK;YACf,OAAO,KAAG,IAAI,MAAK,KAAG;YACtB,IAAI,MAAM,MAAM;YAChB,MAAM,KAAG,IAAI,MAAK,KAAG;YACrB,IAAI,KAAK,MAAM;YACf,MAAM,KAAG,KAAK,MAAK,KAAG;YACtB,IAAI,KAAK,KAAK;YACd,OAAO,KAAG,KAAK,MAAK,KAAG;YAEvB,IAAI,MAAM,MAAM;YAChB,OAAO,KAAG,IAAI,MAAK,KAAG;YACtB,IAAI,MAAM,MAAM;YAChB,OAAO,KAAG,IAAI,MAAK,KAAG;YACtB,IAAI,MAAM,MAAM;YAChB,OAAO,KAAG,KAAK,MAAK,KAAG;YACvB,IAAI,MAAM,MAAM;YAChB,OAAO,KAAG,KAAK,MAAK,KAAG;QACzB;QAEA,CAAC,CAAE,EAAE,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAE,EAAE,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAE,EAAE,GAAG,OAAO,KAAK;QACpB,CAAC,CAAE,EAAE,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAE,EAAE,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAE,EAAE,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAE,EAAE,GAAG,OAAO,KAAK;QACpB,CAAC,CAAE,EAAE,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAE,EAAE,GAAG,QAAS,IAAI;QACrB,CAAC,CAAE,EAAE,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QAErB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAS,IAAI;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QACrB,CAAC,CAAC,GAAG,GAAG,QAAQ,KAAK;QAErB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QAEpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAQ,IAAI;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;QACpB,CAAC,CAAC,GAAG,GAAG,OAAO,KAAK;IACtB;IAEA,SAAS,oBAAoB,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC;QACtC,aAAa,KAAI,KAAI,GAAE;IACzB;IAEA,SAAS,qBAAqB,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC;QACvC,cAAc,KAAI,KAAI,GAAE;IAC1B;IAEA,IAAI,QAAQ,IAAI,WAAW;QAAC;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;KAAI;IACxF,qBAAqB;IAEjC,SAAS,0BAA0B,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QACpD,IAAI,IAAI,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW;QAC/C,IAAI,GAAG;QACP,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG;QAChC,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACnC,MAAO,KAAK,GAAI;YACd,oBAAoB,GAAE,GAAE,GAAE;YAC1B,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,OAAK,EAAE,GAAG,CAAC,CAAC,OAAK,EAAE,GAAG,CAAC,CAAC,EAAE;YACrD,IAAI;YACJ,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;gBACvB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,IAAI;gBACxB,CAAC,CAAC,EAAE,GAAG,IAAI;gBACX,OAAO;YACT;YACA,KAAK;YACL,QAAQ;YACR,QAAQ;QACV;QACA,IAAI,IAAI,GAAG;YACT,oBAAoB,GAAE,GAAE,GAAE;YAC1B,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,OAAK,EAAE,GAAG,CAAC,CAAC,OAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QACtD;QACA,OAAO;IACT;IAEA,SAAS,sBAAsB,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QACzC,IAAI,IAAI,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW;QAC/C,IAAI,GAAG;QACP,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG;QAChC,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACnC,MAAO,KAAK,GAAI;YACd,oBAAoB,GAAE,GAAE,GAAE;YAC1B,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,OAAK,EAAE,GAAG,CAAC,CAAC,EAAE;YACzC,IAAI;YACJ,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;gBACvB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,IAAI;gBACxB,CAAC,CAAC,EAAE,GAAG,IAAI;gBACX,OAAO;YACT;YACA,KAAK;YACL,QAAQ;QACV;QACA,IAAI,IAAI,GAAG;YACT,oBAAoB,GAAE,GAAE,GAAE;YAC1B,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,OAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QAC1C;QACA,OAAO;IACT;IAEA,SAAS,cAAc,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QACjC,IAAI,IAAI,IAAI,WAAW;QACvB,qBAAqB,GAAE,GAAE,GAAE;QAC3B,IAAI,KAAK,IAAI,WAAW;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,IAAE,GAAG;QAC3C,OAAO,sBAAsB,GAAE,MAAK,GAAE,IAAG;IAC3C;IAEA,SAAS,kBAAkB,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QAC5C,IAAI,IAAI,IAAI,WAAW;QACvB,qBAAqB,GAAE,GAAE,GAAE;QAC3B,IAAI,KAAK,IAAI,WAAW;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,IAAE,GAAG;QAC3C,OAAO,0BAA0B,GAAE,MAAK,GAAE,MAAK,GAAE,IAAG;IACtD;IAEA;;;AAGA,GAEA,IAAI,WAAW,SAAS,GAAG;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,WAAW;QAC7B,IAAI,CAAC,CAAC,GAAG,IAAI,YAAY;QACzB,IAAI,CAAC,CAAC,GAAG,IAAI,YAAY;QACzB,IAAI,CAAC,GAAG,GAAG,IAAI,YAAY;QAC3B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG;QAEX,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QAEhC,KAAK,GAAG,CAAE,EAAE,GAAG,OAAO,CAAC,GAAG,CAAE,EAAE,GAAG,IAAI,KAAK;QAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,AAAE,KAA2B;QACtF,KAAK,GAAG,CAAE,EAAE,GAAG,OAAO,CAAC,GAAG,CAAE,EAAE,GAAG,IAAI,KAAK;QAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,OAAO,KAAO,MAAO,CAAE,IAAI;QACtF,KAAK,GAAG,CAAE,EAAE,GAAG,OAAO,CAAC,GAAG,CAAE,EAAE,GAAG,IAAI,KAAK;QAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,OAAO,KAAO,MAAO,CAAE,IAAI;QACtF,KAAK,GAAG,CAAE,EAAE,GAAG,OAAO,CAAC,GAAG,CAAE,EAAE,GAAG,IAAI,KAAK;QAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,OAAQ,IAAM,MAAO,CAAE,IAAI;QACtF,KAAK,GAAG,CAAE,EAAE,GAAG,OAAO,CAAC,GAAG,CAAE,EAAE,GAAG,IAAI,KAAK;QAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,OAAQ,IAAM,MAAM,EAAG,IAAI;QACtF,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,AAAE,OAAQ,IAAM;QAC5B,KAAK,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;QAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,OAAO,KAAO,MAAO,CAAE,IAAI;QACtF,KAAK,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;QAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,OAAO,KAAO,MAAO,CAAE,IAAI;QACtF,KAAK,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;QAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,OAAQ,IAAM,MAAO,CAAE,IAAI;QACtF,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,AAAE,OAAQ,IAAM;QAE5B,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;QACnD,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;QACnD,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;QACnD,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;QACnD,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;QACnD,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;QACnD,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;QACnD,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK;IACrD;IAEA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,IAAI,EAAE,KAAK;QACjD,IAAI,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAK,KAAK;QACjC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QACpC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QAExC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;QAElB,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,EACd,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;QAElB,MAAO,SAAS,GAAI;YAClB,KAAK,CAAC,CAAC,OAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,OAAM,EAAE,GAAG,IAAI,KAAK;YAAG,MAAM,AAAE,KAA2B;YACtF,KAAK,CAAC,CAAC,OAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,OAAM,EAAE,GAAG,IAAI,KAAK;YAAG,MAAM,CAAC,AAAC,OAAO,KAAO,MAAO,CAAE,IAAI;YACtF,KAAK,CAAC,CAAC,OAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,OAAM,EAAE,GAAG,IAAI,KAAK;YAAG,MAAM,CAAC,AAAC,OAAO,KAAO,MAAO,CAAE,IAAI;YACtF,KAAK,CAAC,CAAC,OAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,OAAM,EAAE,GAAG,IAAI,KAAK;YAAG,MAAM,CAAC,AAAC,OAAQ,IAAM,MAAO,CAAE,IAAI;YACtF,KAAK,CAAC,CAAC,OAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,OAAM,EAAE,GAAG,IAAI,KAAK;YAAG,MAAM,CAAC,AAAC,OAAQ,IAAM,MAAM,EAAG,IAAI;YACtF,MAAM,AAAE,OAAQ,IAAM;YACtB,KAAK,CAAC,CAAC,OAAK,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,OAAK,GAAG,GAAG,IAAI,KAAK;YAAG,MAAM,CAAC,AAAC,OAAO,KAAO,MAAO,CAAE,IAAI;YACtF,KAAK,CAAC,CAAC,OAAK,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,OAAK,GAAG,GAAG,IAAI,KAAK;YAAG,MAAM,CAAC,AAAC,OAAO,KAAO,MAAO,CAAE,IAAI;YACtF,KAAK,CAAC,CAAC,OAAK,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,OAAK,GAAG,GAAG,IAAI,KAAK;YAAG,MAAM,CAAC,AAAC,OAAQ,IAAM,MAAO,CAAE,IAAI;YACtF,MAAM,AAAE,OAAO,IAAM;YAErB,IAAI;YAEJ,KAAK;YACL,MAAM,KAAK;YACX,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,IAAK,OAAO;YAAK,MAAM;YACvB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,KAAM,OAAO;YAAK,MAAM;YAExB,KAAK;YACL,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,IAAK,OAAO;YAAK,MAAM;YACvB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,KAAM,OAAO;YAAK,MAAM;YAExB,KAAK;YACL,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,IAAK,OAAO;YAAK,MAAM;YACvB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,KAAM,OAAO;YAAK,MAAM;YAExB,KAAK;YACL,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,IAAK,OAAO;YAAK,MAAM;YACvB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,KAAM,OAAO;YAAK,MAAM;YAExB,KAAK;YACL,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,IAAK,OAAO;YAAK,MAAM;YACvB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,KAAM,OAAO;YAAK,MAAM;YAExB,KAAK;YACL,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,IAAK,OAAO;YAAK,MAAM;YACvB,MAAM,KAAK;YACX,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,KAAM,OAAO;YAAK,MAAM;YAExB,KAAK;YACL,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,IAAK,OAAO;YAAK,MAAM;YACvB,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,KAAM,OAAO;YAAK,MAAM;YAExB,KAAK;YACL,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,IAAK,OAAO;YAAK,MAAM;YACvB,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,KAAM,OAAO;YAAK,MAAM;YAExB,KAAK;YACL,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,IAAK,OAAO;YAAK,MAAM;YACvB,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK,CAAC,IAAI,EAAE;YAClB,KAAM,OAAO;YAAK,MAAM;YAExB,KAAK;YACL,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,IAAK,OAAO;YAAK,MAAM;YACvB,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,MAAM,KAAK;YACX,KAAM,OAAO;YAAK,MAAM;YAExB,IAAI,AAAE,CAAC,KAAK,CAAC,IAAI,IAAM;YACvB,IAAI,AAAC,IAAI,KAAM;YACf,KAAK,IAAI;YACT,IAAK,MAAM;YACX,MAAM;YAEN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YAEL,QAAQ;YACR,SAAS;QACX;QACA,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;QACZ,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;QACZ,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;QACZ,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;QACZ,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;QACZ,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;QACZ,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;QACZ,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;QACZ,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;QACZ,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;IACd;IAEA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAS,GAAG,EAAE,MAAM;QAC9C,IAAI,IAAI,IAAI,YAAY;QACxB,IAAI,GAAG,MAAM,GAAG;QAEhB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,IAAI,CAAC,QAAQ;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;YACnB,MAAO,IAAI,IAAI,IAAK,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;YACrC,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG;QAC9B;QAEA,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK;QAClB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;QACb,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;YACvB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;YACb,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK;YAClB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;QACf;QACA,IAAI,CAAC,CAAC,CAAC,EAAE,IAAK,IAAI;QAClB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK;QAClB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;QACb,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;QACb,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK;QAClB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;QACb,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;QAEb,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;QACnB,IAAI,CAAC,CAAC,EAAE,KAAK;QACb,CAAC,CAAC,EAAE,IAAI;QACR,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;YACvB,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;YACnB,IAAI,CAAC,CAAC,EAAE,KAAK;YACb,CAAC,CAAC,EAAE,IAAI;QACV;QACA,CAAC,CAAC,EAAE,IAAK,KAAK;QAEd,OAAO,CAAC,IAAI,CAAC,IAAI;QACjB,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,IAAI;QACjC,OAAO,CAAC;QACR,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,OAAQ,CAAC,CAAC,EAAE;QAE9D,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAY,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAuB,IAAI;QAC3E,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAM,IAAM,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAuB,IAAI;QAC3E,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAM,IAAM,IAAI,CAAC,CAAC,CAAC,EAAE,IAAK,CAAsB,IAAI;QAC3E,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAM,IAAM,IAAI,CAAC,CAAC,CAAC,EAAE,IAAK,CAAsB,IAAI;QAC3E,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,KAAO,IAAI,CAAC,CAAC,CAAC,EAAE,IAAK,IAAM,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAG,IAAI;QAC3E,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAM,IAAM,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAuB,IAAI;QAC3E,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAM,IAAM,IAAI,CAAC,CAAC,CAAC,EAAE,IAAK,CAAsB,IAAI;QAC3E,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAM,IAAM,IAAI,CAAC,CAAC,CAAC,EAAE,IAAK,CAAsB,IAAI;QAE3E,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE;QAC3B,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI;QAChB,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK;YACtB,IAAI,AAAC,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;YACrD,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI;QAClB;QAEA,GAAG,CAAC,SAAQ,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAQ,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAQ,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAQ,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAQ,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAQ,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAQ,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAQ,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAQ,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAQ,EAAE,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAO,GAAG,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAO,GAAG,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAO,GAAG,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAO,GAAG,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAO,GAAG,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;QACrC,GAAG,CAAC,SAAO,GAAG,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAK;IACvC;IAEA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,IAAI,EAAE,KAAK;QACjD,IAAI,GAAG;QAEP,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAQ,KAAK,IAAI,CAAC,QAAQ;YAC1B,IAAI,OAAO,OACT,OAAO;YACT,IAAK,IAAI,GAAG,IAAI,MAAM,IACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,CAAC,CAAC,OAAK,EAAE;YAC5C,SAAS;YACT,QAAQ;YACR,IAAI,CAAC,QAAQ,IAAI;YACjB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAClB;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG;YAC5B,IAAI,CAAC,QAAQ,GAAG;QAClB;QAEA,IAAI,SAAS,IAAI;YACf,OAAO,QAAS,QAAQ;YACxB,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM;YACrB,QAAQ;YACR,SAAS;QACX;QAEA,IAAI,OAAO;YACT,IAAK,IAAI,GAAG,IAAI,OAAO,IACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,CAAC,CAAC,OAAK,EAAE;YAC5C,IAAI,CAAC,QAAQ,IAAI;QACnB;IACF;IAEA,SAAS,mBAAmB,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;QACpD,IAAI,IAAI,IAAI,SAAS;QACrB,EAAE,MAAM,CAAC,GAAG,MAAM;QAClB,EAAE,MAAM,CAAC,KAAK;QACd,OAAO;IACT;IAEA,SAAS,0BAA0B,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;QACvD,IAAI,IAAI,IAAI,WAAW;QACvB,mBAAmB,GAAE,GAAE,GAAE,MAAK,GAAE;QAChC,OAAO,iBAAiB,GAAE,MAAK,GAAE;IACnC;IAEA,SAAS,iBAAiB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QACjC,IAAI;QACJ,IAAI,IAAI,IAAI,OAAO,CAAC;QACpB,kBAAkB,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;QAC9B,mBAAmB,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI;QACzC,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG;QAChC,OAAO;IACT;IAEA,SAAS,sBAAsB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QACtC,IAAI;QACJ,IAAI,IAAI,IAAI,WAAW;QACvB,IAAI,IAAI,IAAI,OAAO,CAAC;QACpB,cAAc,GAAE,GAAE,IAAG,GAAE;QACvB,IAAI,0BAA0B,GAAG,IAAG,GAAG,IAAG,IAAI,IAAG,OAAO,GAAG,OAAO,CAAC;QACnE,kBAAkB,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;QAC9B,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG;QAChC,OAAO;IACT;IAEA,SAAS,SAAS,CAAC,EAAE,CAAC;QACpB,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAC;IACvC;IAEA,SAAS,SAAS,CAAC;QACjB,IAAI,GAAG,GAAG,IAAI;QACd,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;YACvB,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI;YACf,IAAI,KAAK,KAAK,CAAC,IAAI;YACnB,CAAC,CAAC,EAAE,GAAG,IAAI,IAAI;QACjB;QACA,CAAC,CAAC,EAAE,IAAI,IAAE,IAAI,KAAK,CAAC,IAAE,CAAC;IACzB;IAEA,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;QACvB,IAAI,GAAG,IAAI,CAAC,CAAC,IAAE,CAAC;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YAC3B,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,EAAE,IAAI;YACR,CAAC,CAAC,EAAE,IAAI;QACV;IACF;IAEA,SAAS,UAAU,CAAC,EAAE,CAAC;QACrB,IAAI,GAAG,GAAG;QACV,IAAI,IAAI,MAAM,IAAI;QAClB,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACpC,SAAS;QACT,SAAS;QACT,SAAS;QACT,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK;YACtB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG;YACd,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;gBACvB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC,AAAC,CAAC,CAAC,IAAE,EAAE,IAAE,KAAM,CAAC;gBACxC,CAAC,CAAC,IAAE,EAAE,IAAI;YACZ;YACA,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,AAAC,CAAC,CAAC,GAAG,IAAE,KAAM,CAAC;YACzC,IAAI,AAAC,CAAC,CAAC,GAAG,IAAE,KAAM;YAClB,CAAC,CAAC,GAAG,IAAI;YACT,SAAS,GAAG,GAAG,IAAE;QACnB;QACA,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;YACvB,CAAC,CAAC,IAAE,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG;YAChB,CAAC,CAAC,IAAE,IAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAE;QACnB;IACF;IAEA,SAAS,SAAS,CAAC,EAAE,CAAC;QACpB,IAAI,IAAI,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW;QAC/C,UAAU,GAAG;QACb,UAAU,GAAG;QACb,OAAO,iBAAiB,GAAG,GAAG,GAAG;IACnC;IAEA,SAAS,SAAS,CAAC;QACjB,IAAI,IAAI,IAAI,WAAW;QACvB,UAAU,GAAG;QACb,OAAO,CAAC,CAAC,EAAE,GAAG;IAChB;IAEA,SAAS,YAAY,CAAC,EAAE,CAAC;QACvB,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAE,EAAE,GAAG,CAAC,CAAC,CAAC,IAAE,IAAE,EAAE,IAAI,CAAC;QACvD,CAAC,CAAC,GAAG,IAAI;IACX;IAEA,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACjD;IAEA,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACjD;IAEA,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAChB,IAAI,GAAG,GACJ,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GACpE,KAAK,GAAI,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GACrE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GACrE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAC5D,KAAK,CAAC,CAAC,EAAE,EACT,KAAK,CAAC,CAAC,EAAE,EACT,KAAK,CAAC,CAAC,EAAE,EACT,KAAK,CAAC,CAAC,EAAE,EACT,KAAK,CAAC,CAAC,EAAE,EACT,KAAK,CAAC,CAAC,EAAE,EACT,KAAK,CAAC,CAAC,EAAE,EACT,KAAK,CAAC,CAAC,EAAE,EACT,KAAK,CAAC,CAAC,EAAE,EACT,KAAK,CAAC,CAAC,EAAE,EACT,MAAM,CAAC,CAAC,GAAG,EACX,MAAM,CAAC,CAAC,GAAG,EACX,MAAM,CAAC,CAAC,GAAG,EACX,MAAM,CAAC,CAAC,GAAG,EACX,MAAM,CAAC,CAAC,GAAG,EACX,MAAM,CAAC,CAAC,GAAG;QAEb,IAAI,CAAC,CAAC,EAAE;QACR,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,EAAE;QACR,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,EAAE;QACR,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,EAAE;QACR,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,EAAE;QACR,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,EAAE;QACR,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,EAAE;QACR,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,EAAE;QACR,MAAM,IAAI;QACV,MAAM,IAAI;QACV,MAAM,IAAI;QACV,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,EAAE;QACR,MAAM,IAAI;QACV,MAAM,IAAI;QACV,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,EAAE;QACR,MAAM,IAAI;QACV,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,GAAG;QACT,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,GAAG;QACT,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,GAAG;QACT,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,GAAG;QACT,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,GAAG;QACT,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,IAAI,CAAC,CAAC,GAAG;QACT,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QACX,OAAO,IAAI;QAEX,MAAO,KAAK;QACZ,MAAO,KAAK;QACZ,MAAO,KAAK;QACZ,MAAO,KAAK;QACZ,MAAO,KAAK;QACZ,MAAO,KAAK;QACZ,MAAO,KAAK;QACZ,MAAO,KAAK;QACZ,MAAO,KAAK;QACZ,MAAO,KAAK;QACZ,OAAO,KAAK;QACZ,OAAO,KAAK;QACZ,OAAO,KAAK;QACZ,OAAO,KAAK;QACZ,OAAO,KAAK;QACZ,iBAAiB;QAEjB,YAAY;QACZ,IAAI;QACJ,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,MAAM,IAAE,IAAI,KAAK,CAAC,IAAE,CAAC;QAErB,aAAa;QACb,IAAI;QACJ,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAK,KAAK,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAS,KAAK,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,IAAI,MAAM,IAAI;QAAO,IAAI,KAAK,KAAK,CAAC,IAAI;QAAQ,MAAM,IAAI,IAAI;QAC9D,MAAM,IAAE,IAAI,KAAK,CAAC,IAAE,CAAC;QAErB,CAAC,CAAE,EAAE,GAAG;QACR,CAAC,CAAE,EAAE,GAAG;QACR,CAAC,CAAE,EAAE,GAAG;QACR,CAAC,CAAE,EAAE,GAAG;QACR,CAAC,CAAE,EAAE,GAAG;QACR,CAAC,CAAE,EAAE,GAAG;QACR,CAAC,CAAE,EAAE,GAAG;QACR,CAAC,CAAE,EAAE,GAAG;QACR,CAAC,CAAE,EAAE,GAAG;QACR,CAAC,CAAE,EAAE,GAAG;QACR,CAAC,CAAC,GAAG,GAAG;QACR,CAAC,CAAC,GAAG,GAAG;QACR,CAAC,CAAC,GAAG,GAAG;QACR,CAAC,CAAC,GAAG,GAAG;QACR,CAAC,CAAC,GAAG,GAAG;QACR,CAAC,CAAC,GAAG,GAAG;IACV;IAEA,SAAS,EAAE,CAAC,EAAE,CAAC;QACb,EAAE,GAAG,GAAG;IACV;IAEA,SAAS,SAAS,CAAC,EAAE,CAAC;QACpB,IAAI,IAAI;QACR,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACpC,IAAK,IAAI,KAAK,KAAK,GAAG,IAAK;YACzB,EAAE,GAAG;YACL,IAAG,MAAM,KAAK,MAAM,GAAG,EAAE,GAAG,GAAG;QACjC;QACA,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACtC;IAEA,SAAS,QAAQ,CAAC,EAAE,CAAC;QACnB,IAAI,IAAI;QACR,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACpC,IAAK,IAAI,KAAK,KAAK,GAAG,IAAK;YACvB,EAAE,GAAG;YACL,IAAG,MAAM,GAAG,EAAE,GAAG,GAAG;QACxB;QACA,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACtC;IAEA,SAAS,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC;QAChC,IAAI,IAAI,IAAI,WAAW;QACvB,IAAI,IAAI,IAAI,aAAa,KAAK,GAAG;QACjC,IAAI,IAAI,MAAM,IAAI,MAAM,IAAI,MACxB,IAAI,MAAM,IAAI,MAAM,IAAI;QAC5B,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACpC,CAAC,CAAC,GAAG,GAAC,AAAC,CAAC,CAAC,GAAG,GAAC,MAAK;QAClB,CAAC,CAAC,EAAE,IAAE;QACN,YAAY,GAAE;QACd,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;YACvB,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC;QACjB;QACA,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC;QACV,IAAK,IAAE,KAAK,KAAG,GAAG,EAAE,EAAG;YACrB,IAAE,AAAC,CAAC,CAAC,MAAI,EAAE,KAAG,CAAC,IAAE,CAAC,IAAG;YACrB,SAAS,GAAE,GAAE;YACb,SAAS,GAAE,GAAE;YACb,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE;YACJ,EAAE,GAAE;YACJ,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE;YACJ,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE,GAAE;YACN,EAAE,GAAE;YACJ,SAAS,GAAE,GAAE;YACb,SAAS,GAAE,GAAE;QACf;QACA,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;YACvB,CAAC,CAAC,IAAE,GAAG,GAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,IAAE,GAAG,GAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,IAAE,GAAG,GAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,IAAE,GAAG,GAAC,CAAC,CAAC,EAAE;QACd;QACA,IAAI,MAAM,EAAE,QAAQ,CAAC;QACrB,IAAI,MAAM,EAAE,QAAQ,CAAC;QACrB,SAAS,KAAI;QACb,EAAE,KAAI,KAAI;QACV,UAAU,GAAE;QACZ,OAAO;IACT;IAEA,SAAS,uBAAuB,CAAC,EAAE,CAAC;QAClC,OAAO,kBAAkB,GAAG,GAAG;IACjC;IAEA,SAAS,mBAAmB,CAAC,EAAE,CAAC;QAC9B,YAAY,GAAG;QACf,OAAO,uBAAuB,GAAG;IACnC;IAEA,SAAS,oBAAoB,CAAC,EAAE,CAAC,EAAE,CAAC;QAClC,IAAI,IAAI,IAAI,WAAW;QACvB,kBAAkB,GAAG,GAAG;QACxB,OAAO,qBAAqB,GAAG,IAAI,GAAG;IACxC;IAEA,IAAI,qBAAqB;IACzB,IAAI,0BAA0B;IAE9B,SAAS,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAClC,IAAI,IAAI,IAAI,WAAW;QACvB,oBAAoB,GAAG,GAAG;QAC1B,OAAO,mBAAmB,GAAG,GAAG,GAAG,GAAG;IACxC;IAEA,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACvC,IAAI,IAAI,IAAI,WAAW;QACvB,oBAAoB,GAAG,GAAG;QAC1B,OAAO,wBAAwB,GAAG,GAAG,GAAG,GAAG;IAC7C;IAEA,IAAI,IAAI;QACN;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;QACpC;QAAY;QAAY;QAAY;KACrC;IAED,SAAS,qBAAqB,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;QACxC,IAAI,KAAK,IAAI,WAAW,KAAK,KAAK,IAAI,WAAW,KAC7C,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KACnC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KACnC,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;QAEjC,IAAI,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EAEX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE,EACX,MAAM,EAAE,CAAC,EAAE;QAEf,IAAI,MAAM;QACV,MAAO,KAAK,IAAK;YACf,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;gBACvB,IAAI,IAAI,IAAI;gBACZ,EAAE,CAAC,EAAE,GAAG,AAAC,CAAC,CAAC,IAAE,EAAE,IAAI,KAAO,CAAC,CAAC,IAAE,EAAE,IAAI,KAAO,CAAC,CAAC,IAAE,EAAE,IAAI,IAAK,CAAC,CAAC,IAAE,EAAE;gBAChE,EAAE,CAAC,EAAE,GAAG,AAAC,CAAC,CAAC,IAAE,EAAE,IAAI,KAAO,CAAC,CAAC,IAAE,EAAE,IAAI,KAAO,CAAC,CAAC,IAAE,EAAE,IAAI,IAAK,CAAC,CAAC,IAAE,EAAE;YAClE;YACA,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;gBACvB,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBAEN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBAEN,MAAM;gBACN,IAAI;gBACJ,IAAI;gBAEJ,IAAI,IAAI;gBAAQ,IAAI,MAAM;gBAC1B,IAAI,IAAI;gBAAQ,IAAI,MAAM;gBAE1B,SAAS;gBACT,IAAI,CAAC,AAAC,QAAQ,KAAO,OAAQ,KAAG,EAAI,IAAI,CAAC,AAAC,QAAQ,KAAO,OAAQ,KAAG,EAAI,IAAI,CAAC,AAAC,QAAS,KAAG,KAAQ,OAAQ,KAAG,CAAC,KAAG,EAAE,CAAG;gBACtH,IAAI,CAAC,AAAC,QAAQ,KAAO,OAAQ,KAAG,EAAI,IAAI,CAAC,AAAC,QAAQ,KAAO,OAAQ,KAAG,EAAI,IAAI,CAAC,AAAC,QAAS,KAAG,KAAQ,OAAQ,KAAG,CAAC,KAAG,EAAE,CAAG;gBAEtH,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAC5B,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAE5B,KAAK;gBACL,IAAI,AAAC,MAAM,MAAQ,CAAC,MAAM;gBAC1B,IAAI,AAAC,MAAM,MAAQ,CAAC,MAAM;gBAE1B,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAC5B,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAE5B,IAAI;gBACJ,IAAI,CAAC,CAAC,IAAE,EAAE;gBACV,IAAI,CAAC,CAAC,IAAE,IAAE,EAAE;gBAEZ,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAC5B,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAE5B,IAAI;gBACJ,IAAI,EAAE,CAAC,IAAE,GAAG;gBACZ,IAAI,EAAE,CAAC,IAAE,GAAG;gBAEZ,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAC5B,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAE5B,KAAK,MAAM;gBACX,KAAK,MAAM;gBACX,KAAK,MAAM;gBAEX,KAAK,IAAI,SAAS,KAAK;gBACvB,KAAK,IAAI,SAAS,KAAK;gBAEvB,MAAM;gBACN,IAAI;gBACJ,IAAI;gBAEJ,IAAI,IAAI;gBAAQ,IAAI,MAAM;gBAC1B,IAAI,IAAI;gBAAQ,IAAI,MAAM;gBAE1B,SAAS;gBACT,IAAI,CAAC,AAAC,QAAQ,KAAO,OAAQ,KAAG,EAAI,IAAI,CAAC,AAAC,QAAS,KAAG,KAAQ,OAAQ,KAAG,CAAC,KAAG,EAAE,CAAG,IAAI,CAAC,AAAC,QAAS,KAAG,KAAQ,OAAQ,KAAG,CAAC,KAAG,EAAE,CAAG;gBAChI,IAAI,CAAC,AAAC,QAAQ,KAAO,OAAQ,KAAG,EAAI,IAAI,CAAC,AAAC,QAAS,KAAG,KAAQ,OAAQ,KAAG,CAAC,KAAG,EAAE,CAAG,IAAI,CAAC,AAAC,QAAS,KAAG,KAAQ,OAAQ,KAAG,CAAC,KAAG,EAAE,CAAG;gBAEhI,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAC5B,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAE5B,MAAM;gBACN,IAAI,AAAC,MAAM,MAAQ,MAAM,MAAQ,MAAM;gBACvC,IAAI,AAAC,MAAM,MAAQ,MAAM,MAAQ,MAAM;gBAEvC,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAC5B,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAE5B,KAAK,MAAM;gBACX,KAAK,MAAM;gBACX,KAAK,MAAM;gBAEX,MAAM,AAAC,IAAI,SAAW,KAAK;gBAC3B,MAAM,AAAC,IAAI,SAAW,KAAK;gBAE3B,MAAM;gBACN,IAAI;gBACJ,IAAI;gBAEJ,IAAI,IAAI;gBAAQ,IAAI,MAAM;gBAC1B,IAAI,IAAI;gBAAQ,IAAI,MAAM;gBAE1B,IAAI;gBACJ,IAAI;gBAEJ,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAC5B,KAAK,IAAI;gBAAQ,KAAK,MAAM;gBAE5B,KAAK,MAAM;gBACX,KAAK,MAAM;gBACX,KAAK,MAAM;gBAEX,MAAM,AAAC,IAAI,SAAW,KAAK;gBAC3B,MAAM,AAAC,IAAI,SAAW,KAAK;gBAE3B,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBAEN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBAEN,IAAI,IAAE,OAAO,IAAI;oBACf,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;wBACvB,MAAM;wBACN,IAAI,EAAE,CAAC,EAAE;wBACT,IAAI,EAAE,CAAC,EAAE;wBAET,IAAI,IAAI;wBAAQ,IAAI,MAAM;wBAC1B,IAAI,IAAI;wBAAQ,IAAI,MAAM;wBAE1B,IAAI,EAAE,CAAC,CAAC,IAAE,CAAC,IAAE,GAAG;wBAChB,IAAI,EAAE,CAAC,CAAC,IAAE,CAAC,IAAE,GAAG;wBAEhB,KAAK,IAAI;wBAAQ,KAAK,MAAM;wBAC5B,KAAK,IAAI;wBAAQ,KAAK,MAAM;wBAE5B,SAAS;wBACT,KAAK,EAAE,CAAC,CAAC,IAAE,CAAC,IAAE,GAAG;wBACjB,KAAK,EAAE,CAAC,CAAC,IAAE,CAAC,IAAE,GAAG;wBACjB,IAAI,CAAC,AAAC,OAAO,IAAM,MAAO,KAAG,CAAG,IAAI,CAAC,AAAC,OAAO,IAAM,MAAO,KAAG,CAAG,IAAK,OAAO;wBAC5E,IAAI,CAAC,AAAC,OAAO,IAAM,MAAO,KAAG,CAAG,IAAI,CAAC,AAAC,OAAO,IAAM,MAAO,KAAG,CAAG,IAAI,CAAC,AAAC,OAAO,IAAM,MAAO,KAAG,CAAG;wBAEhG,KAAK,IAAI;wBAAQ,KAAK,MAAM;wBAC5B,KAAK,IAAI;wBAAQ,KAAK,MAAM;wBAE5B,SAAS;wBACT,KAAK,EAAE,CAAC,CAAC,IAAE,EAAE,IAAE,GAAG;wBAClB,KAAK,EAAE,CAAC,CAAC,IAAE,EAAE,IAAE,GAAG;wBAClB,IAAI,CAAC,AAAC,OAAO,KAAO,MAAO,KAAG,EAAI,IAAI,CAAC,AAAC,OAAQ,KAAG,KAAQ,MAAO,KAAG,CAAC,KAAG,EAAE,CAAG,IAAK,OAAO;wBAC1F,IAAI,CAAC,AAAC,OAAO,KAAO,MAAO,KAAG,EAAI,IAAI,CAAC,AAAC,OAAQ,KAAG,KAAQ,MAAO,KAAG,CAAC,KAAG,EAAE,CAAG,IAAI,CAAC,AAAC,OAAO,IAAM,MAAO,KAAG,CAAG;wBAE9G,KAAK,IAAI;wBAAQ,KAAK,MAAM;wBAC5B,KAAK,IAAI;wBAAQ,KAAK,MAAM;wBAE5B,KAAK,MAAM;wBACX,KAAK,MAAM;wBACX,KAAK,MAAM;wBAEX,EAAE,CAAC,EAAE,GAAG,AAAC,IAAI,SAAW,KAAK;wBAC7B,EAAE,CAAC,EAAE,GAAG,AAAC,IAAI,SAAW,KAAK;oBAC/B;gBACF;YACF;YAEA,MAAM;YACN,IAAI;YACJ,IAAI;YAEJ,IAAI,IAAI;YAAQ,IAAI,MAAM;YAC1B,IAAI,IAAI;YAAQ,IAAI,MAAM;YAE1B,IAAI,EAAE,CAAC,EAAE;YACT,IAAI,EAAE,CAAC,EAAE;YAET,KAAK,IAAI;YAAQ,KAAK,MAAM;YAC5B,KAAK,IAAI;YAAQ,KAAK,MAAM;YAE5B,KAAK,MAAM;YACX,KAAK,MAAM;YACX,KAAK,MAAM;YAEX,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YACnC,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YAEnC,IAAI;YACJ,IAAI;YAEJ,IAAI,IAAI;YAAQ,IAAI,MAAM;YAC1B,IAAI,IAAI;YAAQ,IAAI,MAAM;YAE1B,IAAI,EAAE,CAAC,EAAE;YACT,IAAI,EAAE,CAAC,EAAE;YAET,KAAK,IAAI;YAAQ,KAAK,MAAM;YAC5B,KAAK,IAAI;YAAQ,KAAK,MAAM;YAE5B,KAAK,MAAM;YACX,KAAK,MAAM;YACX,KAAK,MAAM;YAEX,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YACnC,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YAEnC,IAAI;YACJ,IAAI;YAEJ,IAAI,IAAI;YAAQ,IAAI,MAAM;YAC1B,IAAI,IAAI;YAAQ,IAAI,MAAM;YAE1B,IAAI,EAAE,CAAC,EAAE;YACT,IAAI,EAAE,CAAC,EAAE;YAET,KAAK,IAAI;YAAQ,KAAK,MAAM;YAC5B,KAAK,IAAI;YAAQ,KAAK,MAAM;YAE5B,KAAK,MAAM;YACX,KAAK,MAAM;YACX,KAAK,MAAM;YAEX,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YACnC,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YAEnC,IAAI;YACJ,IAAI;YAEJ,IAAI,IAAI;YAAQ,IAAI,MAAM;YAC1B,IAAI,IAAI;YAAQ,IAAI,MAAM;YAE1B,IAAI,EAAE,CAAC,EAAE;YACT,IAAI,EAAE,CAAC,EAAE;YAET,KAAK,IAAI;YAAQ,KAAK,MAAM;YAC5B,KAAK,IAAI;YAAQ,KAAK,MAAM;YAE5B,KAAK,MAAM;YACX,KAAK,MAAM;YACX,KAAK,MAAM;YAEX,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YACnC,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YAEnC,IAAI;YACJ,IAAI;YAEJ,IAAI,IAAI;YAAQ,IAAI,MAAM;YAC1B,IAAI,IAAI;YAAQ,IAAI,MAAM;YAE1B,IAAI,EAAE,CAAC,EAAE;YACT,IAAI,EAAE,CAAC,EAAE;YAET,KAAK,IAAI;YAAQ,KAAK,MAAM;YAC5B,KAAK,IAAI;YAAQ,KAAK,MAAM;YAE5B,KAAK,MAAM;YACX,KAAK,MAAM;YACX,KAAK,MAAM;YAEX,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YACnC,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YAEnC,IAAI;YACJ,IAAI;YAEJ,IAAI,IAAI;YAAQ,IAAI,MAAM;YAC1B,IAAI,IAAI;YAAQ,IAAI,MAAM;YAE1B,IAAI,EAAE,CAAC,EAAE;YACT,IAAI,EAAE,CAAC,EAAE;YAET,KAAK,IAAI;YAAQ,KAAK,MAAM;YAC5B,KAAK,IAAI;YAAQ,KAAK,MAAM;YAE5B,KAAK,MAAM;YACX,KAAK,MAAM;YACX,KAAK,MAAM;YAEX,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YACnC,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YAEnC,IAAI;YACJ,IAAI;YAEJ,IAAI,IAAI;YAAQ,IAAI,MAAM;YAC1B,IAAI,IAAI;YAAQ,IAAI,MAAM;YAE1B,IAAI,EAAE,CAAC,EAAE;YACT,IAAI,EAAE,CAAC,EAAE;YAET,KAAK,IAAI;YAAQ,KAAK,MAAM;YAC5B,KAAK,IAAI;YAAQ,KAAK,MAAM;YAE5B,KAAK,MAAM;YACX,KAAK,MAAM;YACX,KAAK,MAAM;YAEX,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YACnC,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YAEnC,IAAI;YACJ,IAAI;YAEJ,IAAI,IAAI;YAAQ,IAAI,MAAM;YAC1B,IAAI,IAAI;YAAQ,IAAI,MAAM;YAE1B,IAAI,EAAE,CAAC,EAAE;YACT,IAAI,EAAE,CAAC,EAAE;YAET,KAAK,IAAI;YAAQ,KAAK,MAAM;YAC5B,KAAK,IAAI;YAAQ,KAAK,MAAM;YAE5B,KAAK,MAAM;YACX,KAAK,MAAM;YACX,KAAK,MAAM;YAEX,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YACnC,EAAE,CAAC,EAAE,GAAG,MAAM,AAAC,IAAI,SAAW,KAAK;YAEnC,OAAO;YACP,KAAK;QACP;QAEA,OAAO;IACT;IAEA,SAAS,YAAY,GAAG,EAAE,CAAC,EAAE,CAAC;QAC5B,IAAI,KAAK,IAAI,WAAW,IACpB,KAAK,IAAI,WAAW,IACpB,IAAI,IAAI,WAAW,MACnB,GAAG,IAAI;QAEX,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QAER,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QAER,qBAAqB,IAAI,IAAI,GAAG;QAChC,KAAK;QAEL,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAE,IAAE,EAAE;QACvC,CAAC,CAAC,EAAE,GAAG;QAEP,IAAI,MAAI,MAAI,CAAC,IAAE,MAAI,IAAE,CAAC;QACtB,CAAC,CAAC,IAAE,EAAE,GAAG;QACT,KAAK,GAAG,IAAE,GAAI,AAAC,IAAI,aAAc,GAAG,KAAK;QACzC,qBAAqB,IAAI,IAAI,GAAG;QAEhC,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,KAAK,KAAK,IAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;QAEnD,OAAO;IACT;IAEA,SAAS,IAAI,CAAC,EAAE,CAAC;QACf,IAAI,IAAI,MAAM,IAAI,MAAM,IAAI,MACxB,IAAI,MAAM,IAAI,MAAM,IAAI,MACxB,IAAI,MAAM,IAAI,MAAM,IAAI;QAE5B,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QACf,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QACf,EAAE,GAAG,GAAG;QACR,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QACf,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QACf,EAAE,GAAG,GAAG;QACR,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QACf,EAAE,GAAG,GAAG;QACR,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QACf,EAAE,GAAG,GAAG;QACR,EAAE,GAAG,GAAG;QACR,EAAE,GAAG,GAAG;QACR,EAAE,GAAG,GAAG;QACR,EAAE,GAAG,GAAG;QAER,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG;QACX,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG;QACX,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG;QACX,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG;IACb;IAEA,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;QACpB,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK;YACtB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE;QACvB;IACF;IAEA,SAAS,KAAK,CAAC,EAAE,CAAC;QAChB,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK;QAC/B,SAAS,IAAI,CAAC,CAAC,EAAE;QACjB,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE;QACZ,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE;QACZ,UAAU,GAAG;QACb,CAAC,CAAC,GAAG,IAAI,SAAS,OAAO;IAC3B;IAEA,SAAS,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;QACzB,IAAI,GAAG;QACP,SAAS,CAAC,CAAC,EAAE,EAAE;QACf,SAAS,CAAC,CAAC,EAAE,EAAE;QACf,SAAS,CAAC,CAAC,EAAE,EAAE;QACf,SAAS,CAAC,CAAC,EAAE,EAAE;QACf,IAAK,IAAI,KAAK,KAAK,GAAG,EAAE,EAAG;YACzB,IAAI,AAAC,CAAC,CAAC,AAAC,IAAE,IAAG,EAAE,IAAI,CAAC,IAAE,CAAC,IAAK;YAC5B,MAAM,GAAG,GAAG;YACZ,IAAI,GAAG;YACP,IAAI,GAAG;YACP,MAAM,GAAG,GAAG;QACd;IACF;IAEA,SAAS,WAAW,CAAC,EAAE,CAAC;QACtB,IAAI,IAAI;YAAC;YAAM;YAAM;YAAM;SAAK;QAChC,SAAS,CAAC,CAAC,EAAE,EAAE;QACf,SAAS,CAAC,CAAC,EAAE,EAAE;QACf,SAAS,CAAC,CAAC,EAAE,EAAE;QACf,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG;QACX,WAAW,GAAG,GAAG;IACnB;IAEA,SAAS,oBAAoB,EAAE,EAAE,EAAE,EAAE,MAAM;QACzC,IAAI,IAAI,IAAI,WAAW;QACvB,IAAI,IAAI;YAAC;YAAM;YAAM;YAAM;SAAK;QAChC,IAAI;QAEJ,IAAI,CAAC,QAAQ,YAAY,IAAI;QAC7B,YAAY,GAAG,IAAI;QACnB,CAAC,CAAC,EAAE,IAAI;QACR,CAAC,CAAC,GAAG,IAAI;QACT,CAAC,CAAC,GAAG,IAAI;QAET,WAAW,GAAG;QACd,KAAK,IAAI;QAET,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,EAAE,CAAC,IAAE,GAAG,GAAG,EAAE,CAAC,EAAE;QACzC,OAAO;IACT;IAEA,IAAI,IAAI,IAAI,aAAa;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;KAAK;IAE5K,SAAS,KAAK,CAAC,EAAE,CAAC;QAChB,IAAI,OAAO,GAAG,GAAG;QACjB,IAAK,IAAI,IAAI,KAAK,IAAI,EAAE,EAAG;YACzB,QAAQ;YACR,IAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,EAAG;gBACvC,CAAC,CAAC,EAAE,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;gBAC3C,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI;gBAClC,CAAC,CAAC,EAAE,IAAI,QAAQ;YAClB;YACA,CAAC,CAAC,EAAE,IAAI;YACR,CAAC,CAAC,EAAE,GAAG;QACT;QACA,QAAQ;QACR,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;YACvB,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;YACnC,QAAQ,CAAC,CAAC,EAAE,IAAI;YAChB,CAAC,CAAC,EAAE,IAAI;QACV;QACA,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,CAAC,EAAE;QAC7C,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;YACvB,CAAC,CAAC,IAAE,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI;YAClB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG;QAChB;IACF;IAEA,SAAS,OAAO,CAAC;QACf,IAAI,IAAI,IAAI,aAAa,KAAK;QAC9B,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACpC,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG;QAChC,KAAK,GAAG;IACV;IAEA,oEAAoE;IACpE,SAAS,YAAY,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QAC/B,IAAI,IAAI,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW;QACvE,IAAI,GAAG,GAAG,IAAI,IAAI,aAAa;QAC/B,IAAI,IAAI;YAAC;YAAM;YAAM;YAAM;SAAK;QAEhC,YAAY,GAAG,IAAI;QACnB,CAAC,CAAC,EAAE,IAAI;QACR,CAAC,CAAC,GAAG,IAAI;QACT,CAAC,CAAC,GAAG,IAAI;QAET,IAAI,QAAQ,IAAI;QAChB,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QACzC,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE;QAE/C,YAAY,GAAG,GAAG,QAAQ,CAAC,KAAK,IAAE;QAClC,OAAO;QACP,WAAW,GAAG;QACd,KAAK,IAAI;QAET,IAAK,IAAI,IAAI,IAAI,IAAI,IAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;QACvC,YAAY,GAAG,IAAI,IAAI;QACvB,OAAO;QAEP,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG;QAChC,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACpC,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;YACvB,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;gBACvB,CAAC,CAAC,IAAE,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;YACvB;QACF;QAEA,KAAK,GAAG,QAAQ,CAAC,KAAK;QACtB,OAAO;IACT;IAEA,SAAS,UAAU,CAAC,EAAE,CAAC;QACrB,IAAI,IAAI,MAAM,MAAM,MAAM,MAAM,MAC5B,MAAM,MAAM,OAAO,MAAM,OAAO,MAChC,OAAO;QAEX,SAAS,CAAC,CAAC,EAAE,EAAE;QACf,YAAY,CAAC,CAAC,EAAE,EAAE;QAClB,EAAE,KAAK,CAAC,CAAC,EAAE;QACX,EAAE,KAAK,KAAK;QACZ,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE;QAEb,EAAE,MAAM;QACR,EAAE,MAAM;QACR,EAAE,MAAM,MAAM;QACd,EAAE,GAAG,MAAM;QACX,EAAE,GAAG,GAAG;QAER,QAAQ,GAAG;QACX,EAAE,GAAG,GAAG;QACR,EAAE,GAAG,GAAG;QACR,EAAE,GAAG,GAAG;QACR,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG;QAEX,EAAE,KAAK,CAAC,CAAC,EAAE;QACX,EAAE,KAAK,KAAK;QACZ,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE;QAEtC,EAAE,KAAK,CAAC,CAAC,EAAE;QACX,EAAE,KAAK,KAAK;QACZ,IAAI,SAAS,KAAK,MAAM,OAAO,CAAC;QAEhC,IAAI,SAAS,CAAC,CAAC,EAAE,MAAO,CAAC,CAAC,GAAG,IAAE,GAAI,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE;QAEpD,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QAClB,OAAO;IACT;IAEA,SAAS,iBAAiB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI;QACJ,IAAI,IAAI,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW;QAC/C,IAAI,IAAI;YAAC;YAAM;YAAM;YAAM;SAAK,EAC5B,IAAI;YAAC;YAAM;YAAM;YAAM;SAAK;QAEhC,IAAI,IAAI,IAAI,OAAO,CAAC;QAEpB,IAAI,UAAU,GAAG,KAAK,OAAO,CAAC;QAE9B,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;QACpC,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK,CAAC,CAAC,IAAE,GAAG,GAAG,EAAE,CAAC,EAAE;QACxC,YAAY,GAAG,GAAG;QAClB,OAAO;QACP,WAAW,GAAG,GAAG;QAEjB,WAAW,GAAG,GAAG,QAAQ,CAAC;QAC1B,IAAI,GAAG;QACP,KAAK,GAAG;QAER,KAAK;QACL,IAAI,iBAAiB,IAAI,GAAG,GAAG,IAAI;YACjC,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG;YAC/B,OAAO,CAAC;QACV;QAEA,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG;QACzC,OAAO;IACT;IAEA,IAAI,4BAA4B,IAC5B,8BAA8B,IAC9B,6BAA6B,IAC7B,gCAAgC,IAChC,0BAA0B,IAC1B,gCAAgC,IAChC,4BAA4B,IAC5B,4BAA4B,IAC5B,2BAA2B,IAC3B,wBAAwB,6BACxB,uBAAuB,4BACvB,0BAA0B,+BAC1B,oBAAoB,IACpB,6BAA6B,IAC7B,6BAA6B,IAC7B,wBAAwB,IACxB,oBAAoB;IAExB,KAAK,QAAQ,GAAG;QACd,sBAAsB;QACtB,mBAAmB;QACnB,eAAe;QACf,2BAA2B;QAC3B,uBAAuB;QACvB,oBAAoB;QACpB,2BAA2B;QAC3B,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,uBAAuB;QACvB,mBAAmB;QACnB,wBAAwB;QACxB,qBAAqB;QACrB,oBAAoB;QACpB,YAAY;QACZ,iBAAiB;QACjB,oBAAoB;QACpB,aAAa;QACb,aAAa;QACb,qBAAqB;QACrB,kBAAkB;QAElB,2BAA2B;QAC3B,6BAA6B;QAC7B,4BAA4B;QAC5B,+BAA+B;QAC/B,yBAAyB;QACzB,+BAA+B;QAC/B,2BAA2B;QAC3B,2BAA2B;QAC3B,0BAA0B;QAC1B,uBAAuB;QACvB,sBAAsB;QACtB,yBAAyB;QACzB,mBAAmB;QACnB,4BAA4B;QAC5B,4BAA4B;QAC5B,uBAAuB;QACvB,mBAAmB;QAEnB,IAAI;QACJ,GAAG;QACH,GAAG;QACH,WAAW;QACX,aAAa;QACb,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,SAAS;QACT,KAAK;QACL,UAAU;QACV,MAAM;QACN,YAAY;QACZ,YAAY;IACd;IAEA,kBAAkB,GAElB,SAAS,aAAa,CAAC,EAAE,CAAC;QACxB,IAAI,EAAE,MAAM,KAAK,2BAA2B,MAAM,IAAI,MAAM;QAC5D,IAAI,EAAE,MAAM,KAAK,6BAA6B,MAAM,IAAI,MAAM;IAChE;IAEA,SAAS,gBAAgB,EAAE,EAAE,EAAE;QAC7B,IAAI,GAAG,MAAM,KAAK,2BAA2B,MAAM,IAAI,MAAM;QAC7D,IAAI,GAAG,MAAM,KAAK,2BAA2B,MAAM,IAAI,MAAM;IAC/D;IAEA,SAAS;QACP,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACzC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE,YAAY,UAAU,GACtC,MAAM,IAAI,UAAU;QACxB;IACF;IAEA,SAAS,QAAQ,GAAG;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK,GAAG,CAAC,EAAE,GAAG;IAChD;IAEA,KAAK,WAAW,GAAG,SAAS,CAAC;QAC3B,IAAI,IAAI,IAAI,WAAW;QACvB,YAAY,GAAG;QACf,OAAO;IACT;IAEA,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,KAAK,EAAE,GAAG;QACvC,gBAAgB,KAAK,OAAO;QAC5B,aAAa,KAAK;QAClB,IAAI,IAAI,IAAI,WAAW,6BAA6B,IAAI,MAAM;QAC9D,IAAI,IAAI,IAAI,WAAW,EAAE,MAAM;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK,CAAC,CAAC,IAAE,2BAA2B,GAAG,GAAG,CAAC,EAAE;QAC7E,iBAAiB,GAAG,GAAG,EAAE,MAAM,EAAE,OAAO;QACxC,OAAO,EAAE,QAAQ,CAAC;IACpB;IAEA,KAAK,SAAS,CAAC,IAAI,GAAG,SAAS,GAAG,EAAE,KAAK,EAAE,GAAG;QAC5C,gBAAgB,KAAK,OAAO;QAC5B,aAAa,KAAK;QAClB,IAAI,IAAI,IAAI,WAAW,gCAAgC,IAAI,MAAM;QACjE,IAAI,IAAI,IAAI,WAAW,EAAE,MAAM;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK,CAAC,CAAC,IAAE,8BAA8B,GAAG,GAAG,CAAC,EAAE;QAChF,IAAI,EAAE,MAAM,GAAG,IAAI,OAAO;QAC1B,IAAI,sBAAsB,GAAG,GAAG,EAAE,MAAM,EAAE,OAAO,SAAS,GAAG,OAAO;QACpE,OAAO,EAAE,QAAQ,CAAC;IACpB;IAEA,KAAK,SAAS,CAAC,SAAS,GAAG;IAC3B,KAAK,SAAS,CAAC,WAAW,GAAG;IAC7B,KAAK,SAAS,CAAC,cAAc,GAAG;IAEhC,KAAK,UAAU,GAAG,SAAS,CAAC,EAAE,CAAC;QAC7B,gBAAgB,GAAG;QACnB,IAAI,EAAE,MAAM,KAAK,+BAA+B,MAAM,IAAI,MAAM;QAChE,IAAI,EAAE,MAAM,KAAK,yBAAyB,MAAM,IAAI,MAAM;QAC1D,IAAI,IAAI,IAAI,WAAW;QACvB,kBAAkB,GAAG,GAAG;QACxB,OAAO;IACT;IAEA,KAAK,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC;QAC/B,gBAAgB;QAChB,IAAI,EAAE,MAAM,KAAK,+BAA+B,MAAM,IAAI,MAAM;QAChE,IAAI,IAAI,IAAI,WAAW;QACvB,uBAAuB,GAAG;QAC1B,OAAO;IACT;IAEA,KAAK,UAAU,CAAC,YAAY,GAAG;IAC/B,KAAK,UAAU,CAAC,kBAAkB,GAAG;IAErC,KAAK,GAAG,GAAG,SAAS,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS;QAClD,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,WAAW;QACnC,OAAO,KAAK,SAAS,CAAC,KAAK,OAAO;IACpC;IAEA,KAAK,GAAG,CAAC,MAAM,GAAG,SAAS,SAAS,EAAE,SAAS;QAC7C,gBAAgB,WAAW;QAC3B,gBAAgB,WAAW;QAC3B,IAAI,IAAI,IAAI,WAAW;QACvB,oBAAoB,GAAG,WAAW;QAClC,OAAO;IACT;IAEA,KAAK,GAAG,CAAC,KAAK,GAAG,KAAK,SAAS;IAE/B,KAAK,GAAG,CAAC,IAAI,GAAG,SAAS,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS;QACvD,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,WAAW;QACnC,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,KAAK,OAAO;IACzC;IAEA,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,SAAS,CAAC,IAAI;IAEzC,KAAK,GAAG,CAAC,OAAO,GAAG;QACjB,IAAI,KAAK,IAAI,WAAW;QACxB,IAAI,KAAK,IAAI,WAAW;QACxB,mBAAmB,IAAI;QACvB,OAAO;YAAC,WAAW;YAAI,WAAW;QAAE;IACtC;IAEA,KAAK,GAAG,CAAC,OAAO,CAAC,aAAa,GAAG,SAAS,SAAS;QACjD,gBAAgB;QAChB,IAAI,UAAU,MAAM,KAAK,2BACvB,MAAM,IAAI,MAAM;QAClB,IAAI,KAAK,IAAI,WAAW;QACxB,uBAAuB,IAAI;QAC3B,OAAO;YAAC,WAAW;YAAI,WAAW,IAAI,WAAW;QAAU;IAC7D;IAEA,KAAK,GAAG,CAAC,eAAe,GAAG;IAC3B,KAAK,GAAG,CAAC,eAAe,GAAG;IAC3B,KAAK,GAAG,CAAC,eAAe,GAAG;IAC3B,KAAK,GAAG,CAAC,WAAW,GAAG;IACvB,KAAK,GAAG,CAAC,cAAc,GAAG,KAAK,SAAS,CAAC,cAAc;IAEvD,KAAK,IAAI,GAAG,SAAS,GAAG,EAAE,SAAS;QACjC,gBAAgB,KAAK;QACrB,IAAI,UAAU,MAAM,KAAK,4BACvB,MAAM,IAAI,MAAM;QAClB,IAAI,YAAY,IAAI,WAAW,oBAAkB,IAAI,MAAM;QAC3D,YAAY,WAAW,KAAK,IAAI,MAAM,EAAE;QACxC,OAAO;IACT;IAEA,KAAK,IAAI,CAAC,IAAI,GAAG,SAAS,SAAS,EAAE,SAAS;QAC5C,gBAAgB,WAAW;QAC3B,IAAI,UAAU,MAAM,KAAK,4BACvB,MAAM,IAAI,MAAM;QAClB,IAAI,MAAM,IAAI,WAAW,UAAU,MAAM;QACzC,IAAI,OAAO,iBAAiB,KAAK,WAAW,UAAU,MAAM,EAAE;QAC9D,IAAI,OAAO,GAAG,OAAO;QACrB,IAAI,IAAI,IAAI,WAAW;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QAChD,OAAO;IACT;IAEA,KAAK,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,EAAE,SAAS;QAC1C,IAAI,YAAY,KAAK,IAAI,CAAC,KAAK;QAC/B,IAAI,MAAM,IAAI,WAAW;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;QAC1D,OAAO;IACT;IAEA,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,SAAS;QACtD,gBAAgB,KAAK,KAAK;QAC1B,IAAI,IAAI,MAAM,KAAK,mBACjB,MAAM,IAAI,MAAM;QAClB,IAAI,UAAU,MAAM,KAAK,4BACvB,MAAM,IAAI,MAAM;QAClB,IAAI,KAAK,IAAI,WAAW,oBAAoB,IAAI,MAAM;QACtD,IAAI,IAAI,IAAI,WAAW,oBAAoB,IAAI,MAAM;QACrD,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,mBAAmB,IAAK,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QACtD,IAAK,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK,EAAE,CAAC,IAAE,kBAAkB,GAAG,GAAG,CAAC,EAAE;QACjE,OAAQ,iBAAiB,GAAG,IAAI,GAAG,MAAM,EAAE,cAAc;IAC3D;IAEA,KAAK,IAAI,CAAC,OAAO,GAAG;QAClB,IAAI,KAAK,IAAI,WAAW;QACxB,IAAI,KAAK,IAAI,WAAW;QACxB,oBAAoB,IAAI;QACxB,OAAO;YAAC,WAAW;YAAI,WAAW;QAAE;IACtC;IAEA,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,SAAS,SAAS;QAClD,gBAAgB;QAChB,IAAI,UAAU,MAAM,KAAK,4BACvB,MAAM,IAAI,MAAM;QAClB,IAAI,KAAK,IAAI,WAAW;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,KAAG,EAAE;QAC3D,OAAO;YAAC,WAAW;YAAI,WAAW,IAAI,WAAW;QAAU;IAC7D;IAEA,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,SAAS,IAAI;QACxC,gBAAgB;QAChB,IAAI,KAAK,MAAM,KAAK,uBAClB,MAAM,IAAI,MAAM;QAClB,IAAI,KAAK,IAAI,WAAW;QACxB,IAAI,KAAK,IAAI,WAAW;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5C,oBAAoB,IAAI,IAAI;QAC5B,OAAO;YAAC,WAAW;YAAI,WAAW;QAAE;IACtC;IAEA,KAAK,IAAI,CAAC,eAAe,GAAG;IAC5B,KAAK,IAAI,CAAC,eAAe,GAAG;IAC5B,KAAK,IAAI,CAAC,UAAU,GAAG;IACvB,KAAK,IAAI,CAAC,eAAe,GAAG;IAE5B,KAAK,IAAI,GAAG,SAAS,GAAG;QACtB,gBAAgB;QAChB,IAAI,IAAI,IAAI,WAAW;QACvB,YAAY,GAAG,KAAK,IAAI,MAAM;QAC9B,OAAO;IACT;IAEA,KAAK,IAAI,CAAC,UAAU,GAAG;IAEvB,KAAK,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC;QACzB,gBAAgB,GAAG;QACnB,kDAAkD;QAClD,IAAI,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,KAAK,GAAG,OAAO;QAC7C,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,OAAO;QAClC,OAAO,AAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,MAAM,MAAM,IAAK,OAAO;IACnD;IAEA,KAAK,OAAO,GAAG,SAAS,EAAE;QACxB,cAAc;IAChB;IAEA,CAAC;QACC,kDAAkD;QAClD,kDAAkD;QAClD,IAAI,SAAS,OAAO,SAAS,cAAe,KAAK,MAAM,IAAI,KAAK,QAAQ,GAAI;QAC5E,IAAI,UAAU,OAAO,eAAe,EAAE;YACpC,YAAY;YACZ,IAAI,QAAQ;YACZ,KAAK,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACxB,IAAI,GAAG,IAAI,IAAI,WAAW;gBAC1B,IAAK,IAAI,GAAG,IAAI,GAAG,KAAK,MAAO;oBAC7B,OAAO,eAAe,CAAC,EAAE,QAAQ,CAAC,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;gBAC3D;gBACA,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;gBACnC,QAAQ;YACV;QACF,OAAO,wCAAoC;YACzC,WAAW;YACX;YACA,IAAI,UAAU,OAAO,WAAW,EAAE;gBAChC,KAAK,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;oBACxB,IAAI,GAAG,IAAI,OAAO,WAAW,CAAC;oBAC9B,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;oBACnC,QAAQ;gBACV;YACF;QACF;IACF,CAAC;AAED,CAAC,EAAE,+CAAkB,eAAe,OAAO,OAAO,GAAG,OAAO,OAAO,GAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2545, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/protocol/src/models/wallet-message/wallet-event/connect-event.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/protocol/src/models/wallet-message/wallet-response/send-transaction-rpc-response.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/protocol/src/models/wallet-message/wallet-response/sign-data-rpc-response.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/protocol/src/models/wallet-message/wallet-response/disconnect-rpc-response.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/protocol/src/models/CHAIN.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/protocol/src/utils/base64.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/protocol/src/utils/binary.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/protocol/src/utils/web-api.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/protocol/src/crypto/session-crypto.ts"], "sourcesContent": ["unable to read source [project]/node_modules/@tonconnect/protocol/src/models/wallet-message/wallet-event/connect-event.ts", "unable to read source [project]/node_modules/@tonconnect/protocol/src/models/wallet-message/wallet-response/send-transaction-rpc-response.ts", "unable to read source [project]/node_modules/@tonconnect/protocol/src/models/wallet-message/wallet-response/sign-data-rpc-response.ts", "unable to read source [project]/node_modules/@tonconnect/protocol/src/models/wallet-message/wallet-response/disconnect-rpc-response.ts", "unable to read source [project]/node_modules/@tonconnect/protocol/src/models/CHAIN.ts", "unable to read source [project]/node_modules/@tonconnect/protocol/src/utils/base64.ts", "unable to read source [project]/node_modules/@tonconnect/protocol/src/utils/binary.ts", "unable to read source [project]/node_modules/@tonconnect/protocol/src/utils/web-api.ts", "unable to read source [project]/node_modules/@tonconnect/protocol/src/crypto/session-crypto.ts"], "names": ["nacl"], "mappings": ";;;;;;;;;;;;;;;;;;;IAuBY,0BAQX;AARD,CAAA,SAAY,yBAAyB,EAAA;IACjC,yBAAA,CAAA,yBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;IACjB,yBAAA,CAAA,yBAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;IACrB,yBAAA,CAAA,yBAAA,CAAA,0BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,0BAA4B,CAAA;IAC5B,yBAAA,CAAA,yBAAA,CAAA,wBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,wBAA0B,CAAA;IAC1B,yBAAA,CAAA,yBAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;IACvB,yBAAA,CAAA,yBAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;IACxB,yBAAA,CAAA,yBAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC9B,CAAC,EARW,yBAAyB,IAAA,CAAzB,yBAAyB,GAQpC,CAAA,CAAA,CAAA,CAAA,CAAA;IA6BW,yBAGX;AAHD,CAAA,SAAY,wBAAwB,EAAA;IAChC,wBAAA,CAAA,wBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;IACjB,wBAAA,CAAA,wBAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC9B,CAAC,EAHW,wBAAwB,IAAA,CAAxB,wBAAwB,GAGnC,CAAA,CAAA,CAAA,CAAA;IC/CW,6BAMX;AAND,CAAA,SAAY,4BAA4B,EAAA;IACpC,4BAAA,CAAA,4BAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;IACjB,4BAAA,CAAA,4BAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;IACrB,4BAAA,CAAA,4BAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;IACvB,4BAAA,CAAA,4BAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;IACxB,4BAAA,CAAA,4BAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC9B,CAAC,EANW,4BAA4B,IAAA,CAA5B,4BAA4B,GAMvC,CAAA,CAAA,CAAA,CAAA;ICLW,sBAMX;AAND,CAAA,SAAY,qBAAqB,EAAA;IAC7B,qBAAA,CAAA,qBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;IACjB,qBAAA,CAAA,qBAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;IACrB,qBAAA,CAAA,qBAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;IACvB,qBAAA,CAAA,qBAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;IACxB,qBAAA,CAAA,qBAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC9B,CAAC,EANW,qBAAqB,IAAA,CAArB,qBAAqB,GAMhC,CAAA,CAAA,CAAA,CAAA;ICTW,uBAKX;AALD,CAAA,SAAY,sBAAsB,EAAA;IAC9B,sBAAA,CAAA,sBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;IACjB,sBAAA,CAAA,sBAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;IACrB,sBAAA,CAAA,sBAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;IACvB,sBAAA,CAAA,sBAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC9B,CAAC,EALW,sBAAsB,IAAA,CAAtB,sBAAsB,GAKjC,CAAA,CAAA,CAAA,CAAA;ICnBW,MAGX;AAHD,CAAA,SAAY,KAAK,EAAA;IACb,KAAA,CAAA,SAAA,CAAA,GAAA,MAAgB,CAAA;IAChB,KAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AAClB,CAAC,EAHW,KAAK,IAAA,CAAL,KAAK,GAGhB,CAAA,CAAA,CAAA,CAAA;ACDD,SAAS,gBAAgB,CAAC,KAAiB,EAAE,OAAgB,EAAA;IACzD,MAAM,OAAO,qJAAG,UAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACzC,IAAI,CAAC,OAAO,EAAE;QACV,OAAO,OAAO,CAAC;IAClB,CAAA;IAED,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAa,EAAE,OAAgB,EAAA;IACvD,IAAI,OAAO,EAAE;QACT,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACrC,CAAA;IAED,yJAAO,UAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,MAAM,CAAC,KAAmC,EAAE,OAAO,GAAG,KAAK,EAAA;IAChE,IAAI,UAAsB,CAAC;IAE3B,IAAI,KAAK,YAAY,UAAU,EAAE;QAC7B,UAAU,GAAG,KAAK,CAAC;IACtB,CAAA,MAAM;QACH,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3B,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACjC,CAAA;QAED,UAAU,qJAAG,UAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACvC,CAAA;IAED,OAAO,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,MAAM,CACX,KAAa,EACb,OAAO,GAAG,KAAK,EAAA;IAMf,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAE7D,OAAO;QACH,QAAQ,GAAA;YACJ,yJAAO,UAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;SAC7C;QACD,QAAQ,GAAA;YACJ,IAAI;gBACA,OAAO,IAAI,CAAC,KAAK,mJAAC,UAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAM,CAAC;YAC9D,CAAA,CAAC,OAAO,CAAC,EAAE;gBACR,OAAO,IAAI,CAAC;YACf,CAAA;SACJ;QACD,YAAY,GAAA;YACR,OAAO,iBAAiB,CAAC;SAC5B;KACJ,CAAC;AACN,CAAC;AAEY,MAAA,MAAM,GAAG;IAClB,MAAM;IACN,MAAM;;AChEM,SAAA,iBAAiB,CAAC,OAAmB,EAAE,OAAmB,EAAA;IACtE,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IACpE,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACzB,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IACzC,OAAO,WAAW,CAAC;AACvB,CAAC;AAEe,SAAA,kBAAkB,CAAC,KAAiB,EAAE,KAAa,EAAA;IAC/D,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;QACvB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC7C,CAAA;IAED,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACxC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACrC,OAAO;QAAC,SAAS;QAAE,SAAS;KAAC,CAAC;AAClC,CAAC;AAEK,SAAU,WAAW,CAAC,SAAqB,EAAA;IAC7C,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,SAAS,CAAC,OAAO,EAAC,IAAI,IAAG;QACrB,SAAS,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IACH,OAAO,SAAS,CAAC;AACrB,CAAC;AACK,SAAU,cAAc,CAAC,SAAiB,EAAA;IAC5C,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,CAAA,eAAA,EAAkB,SAAS,CAAA,cAAA,CAAgB,CAAC,CAAC;IAChE,CAAA;IACD,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACpD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE;QAC1C,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC3D,CAAA;IACD,OAAO,MAAM,CAAC;AAClB;SCjCgB,MAAM,GAAA;IAClB,OACI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,EAC7F;AACN;MCAa,aAAa,CAAA;IAOtB,WAAA,CAAY,OAAiB,CAAA;QANZ,IAAW,CAAA,WAAA,GAAG,EAAE,CAAC;QAO9B,IAAI,CAAC,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACtF,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;KACxD;IAEO,aAAa,GAAA;QACjB,iJAAOA,UAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;KAC7B;IAEO,uBAAuB,CAAC,OAAgB,EAAA;QAC5C,OAAO;YACH,SAAS,EAAE,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC;YAC5C,SAAS,EAAE,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC;SAC/C,CAAC;KACL;IAEO,WAAW,GAAA;QACf,iJAAOA,UAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAC7C;IAEM,OAAO,CAAC,OAAe,EAAE,iBAA6B,EAAA;QACzD,MAAM,cAAc,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACjC,MAAM,SAAS,6IAAGA,UAAI,CAAC,GAAG,CACtB,cAAc,EACd,KAAK,EACL,iBAAiB,EACjB,IAAI,CAAC,OAAO,CAAC,SAAS,CACzB,CAAC;QACF,OAAO,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;KAC9C;IAEM,OAAO,CAAC,OAAmB,EAAE,eAA2B,EAAA;QAC3D,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAE/E,MAAM,SAAS,6IAAGA,UAAI,CAAC,GAAG,CAAC,IAAI,CAC3B,eAAe,EACf,KAAK,EACL,eAAe,EACf,IAAI,CAAC,OAAO,CAAC,SAAS,CACzB,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE;YACZ,MAAM,IAAI,KAAK,CACX,CAAA,8BAAA,EAAiC,OAAO,CAAC,QAAQ,EAAE,CAAA,mBAAA,EAAsB,eAAe,CAAC,QAAQ,EAAE,CAAA,oBAAA,EAAuB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAA,uBAAA,EAA0B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAA,CAAE,CAC3N,CAAC;QACL,CAAA;QAED,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;KAC9C;IAEM,gBAAgB,GAAA;QACnB,OAAO;YACH,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YAC9C,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;SACjD,CAAC;KACL;AACJ", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8], "debugId": null}}, {"offset": {"line": 2737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/eventsource/lib/eventsource.js"], "sourcesContent": ["var parse = require('url').parse\nvar events = require('events')\nvar https = require('https')\nvar http = require('http')\nvar util = require('util')\n\nvar httpsOptions = [\n  'pfx', 'key', 'passphrase', 'cert', 'ca', 'ciphers',\n  'rejectUnauthorized', 'secureProtocol', 'servername', 'checkServerIdentity'\n]\n\nvar bom = [239, 187, 191]\nvar colon = 58\nvar space = 32\nvar lineFeed = 10\nvar carriageReturn = 13\n// Beyond 256KB we could not observe any gain in performance\nvar maxBufferAheadAllocation = 1024 * 256\n// Headers matching the pattern should be removed when redirecting to different origin\nvar reUnsafeHeader = /^(cookie|authorization)$/i\n\nfunction hasBom (buf) {\n  return bom.every(function (charCode, index) {\n    return buf[index] === charCode\n  })\n}\n\n/**\n * Creates a new EventSource object\n *\n * @param {String} url the URL to which to connect\n * @param {Object} [eventSourceInitDict] extra init params. See README for details.\n * @api public\n **/\nfunction EventSource (url, eventSourceInitDict) {\n  var readyState = EventSource.CONNECTING\n  var headers = eventSourceInitDict && eventSourceInitDict.headers\n  var hasNewOrigin = false\n  Object.defineProperty(this, 'readyState', {\n    get: function () {\n      return readyState\n    }\n  })\n\n  Object.defineProperty(this, 'url', {\n    get: function () {\n      return url\n    }\n  })\n\n  var self = this\n  self.reconnectInterval = 1000\n  self.connectionInProgress = false\n\n  function onConnectionClosed (message) {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CONNECTING\n    _emit('error', new Event('error', {message: message}))\n\n    // The url may have been changed by a temporary redirect. If that's the case,\n    // revert it now, and flag that we are no longer pointing to a new origin\n    if (reconnectUrl) {\n      url = reconnectUrl\n      reconnectUrl = null\n      hasNewOrigin = false\n    }\n    setTimeout(function () {\n      if (readyState !== EventSource.CONNECTING || self.connectionInProgress) {\n        return\n      }\n      self.connectionInProgress = true\n      connect()\n    }, self.reconnectInterval)\n  }\n\n  var req\n  var lastEventId = ''\n  if (headers && headers['Last-Event-ID']) {\n    lastEventId = headers['Last-Event-ID']\n    delete headers['Last-Event-ID']\n  }\n\n  var discardTrailingNewline = false\n  var data = ''\n  var eventName = ''\n\n  var reconnectUrl = null\n\n  function connect () {\n    var options = parse(url)\n    var isSecure = options.protocol === 'https:'\n    options.headers = { 'Cache-Control': 'no-cache', 'Accept': 'text/event-stream' }\n    if (lastEventId) options.headers['Last-Event-ID'] = lastEventId\n    if (headers) {\n      var reqHeaders = hasNewOrigin ? removeUnsafeHeaders(headers) : headers\n      for (var i in reqHeaders) {\n        var header = reqHeaders[i]\n        if (header) {\n          options.headers[i] = header\n        }\n      }\n    }\n\n    // Legacy: this should be specified as `eventSourceInitDict.https.rejectUnauthorized`,\n    // but for now exists as a backwards-compatibility layer\n    options.rejectUnauthorized = !(eventSourceInitDict && !eventSourceInitDict.rejectUnauthorized)\n\n    if (eventSourceInitDict && eventSourceInitDict.createConnection !== undefined) {\n      options.createConnection = eventSourceInitDict.createConnection\n    }\n\n    // If specify http proxy, make the request to sent to the proxy server,\n    // and include the original url in path and Host headers\n    var useProxy = eventSourceInitDict && eventSourceInitDict.proxy\n    if (useProxy) {\n      var proxy = parse(eventSourceInitDict.proxy)\n      isSecure = proxy.protocol === 'https:'\n\n      options.protocol = isSecure ? 'https:' : 'http:'\n      options.path = url\n      options.headers.Host = options.host\n      options.hostname = proxy.hostname\n      options.host = proxy.host\n      options.port = proxy.port\n    }\n\n    // If https options are specified, merge them into the request options\n    if (eventSourceInitDict && eventSourceInitDict.https) {\n      for (var optName in eventSourceInitDict.https) {\n        if (httpsOptions.indexOf(optName) === -1) {\n          continue\n        }\n\n        var option = eventSourceInitDict.https[optName]\n        if (option !== undefined) {\n          options[optName] = option\n        }\n      }\n    }\n\n    // Pass this on to the XHR\n    if (eventSourceInitDict && eventSourceInitDict.withCredentials !== undefined) {\n      options.withCredentials = eventSourceInitDict.withCredentials\n    }\n\n    req = (isSecure ? https : http).request(options, function (res) {\n      self.connectionInProgress = false\n      // Handle HTTP errors\n      if (res.statusCode === 500 || res.statusCode === 502 || res.statusCode === 503 || res.statusCode === 504) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        onConnectionClosed()\n        return\n      }\n\n      // Handle HTTP redirects\n      if (res.statusCode === 301 || res.statusCode === 302 || res.statusCode === 307) {\n        var location = res.headers.location\n        if (!location) {\n          // Server sent redirect response without Location header.\n          _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n          return\n        }\n        var prevOrigin = new URL(url).origin\n        var nextOrigin = new URL(location).origin\n        hasNewOrigin = prevOrigin !== nextOrigin\n        if (res.statusCode === 307) reconnectUrl = url\n        url = location\n        process.nextTick(connect)\n        return\n      }\n\n      if (res.statusCode !== 200) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        return self.close()\n      }\n\n      readyState = EventSource.OPEN\n      res.on('close', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n\n      res.on('end', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n      _emit('open', new Event('open'))\n\n      // text/event-stream parser adapted from webkit's\n      // Source/WebCore/page/EventSource.cpp\n      var buf\n      var newBuffer\n      var startingPos = 0\n      var startingFieldLength = -1\n      var newBufferSize = 0\n      var bytesUsed = 0\n\n      res.on('data', function (chunk) {\n        if (!buf) {\n          buf = chunk\n          if (hasBom(buf)) {\n            buf = buf.slice(bom.length)\n          }\n          bytesUsed = buf.length\n        } else {\n          if (chunk.length > buf.length - bytesUsed) {\n            newBufferSize = (buf.length * 2) + chunk.length\n            if (newBufferSize > maxBufferAheadAllocation) {\n              newBufferSize = buf.length + chunk.length + maxBufferAheadAllocation\n            }\n            newBuffer = Buffer.alloc(newBufferSize)\n            buf.copy(newBuffer, 0, 0, bytesUsed)\n            buf = newBuffer\n          }\n          chunk.copy(buf, bytesUsed)\n          bytesUsed += chunk.length\n        }\n\n        var pos = 0\n        var length = bytesUsed\n\n        while (pos < length) {\n          if (discardTrailingNewline) {\n            if (buf[pos] === lineFeed) {\n              ++pos\n            }\n            discardTrailingNewline = false\n          }\n\n          var lineLength = -1\n          var fieldLength = startingFieldLength\n          var c\n\n          for (var i = startingPos; lineLength < 0 && i < length; ++i) {\n            c = buf[i]\n            if (c === colon) {\n              if (fieldLength < 0) {\n                fieldLength = i - pos\n              }\n            } else if (c === carriageReturn) {\n              discardTrailingNewline = true\n              lineLength = i - pos\n            } else if (c === lineFeed) {\n              lineLength = i - pos\n            }\n          }\n\n          if (lineLength < 0) {\n            startingPos = length - pos\n            startingFieldLength = fieldLength\n            break\n          } else {\n            startingPos = 0\n            startingFieldLength = -1\n          }\n\n          parseEventStreamLine(buf, pos, fieldLength, lineLength)\n\n          pos += lineLength + 1\n        }\n\n        if (pos === length) {\n          buf = void 0\n          bytesUsed = 0\n        } else if (pos > 0) {\n          buf = buf.slice(pos, bytesUsed)\n          bytesUsed = buf.length\n        }\n      })\n    })\n\n    req.on('error', function (err) {\n      self.connectionInProgress = false\n      onConnectionClosed(err.message)\n    })\n\n    if (req.setNoDelay) req.setNoDelay(true)\n    req.end()\n  }\n\n  connect()\n\n  function _emit () {\n    if (self.listeners(arguments[0]).length > 0) {\n      self.emit.apply(self, arguments)\n    }\n  }\n\n  this._close = function () {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CLOSED\n    if (req.abort) req.abort()\n    if (req.xhr && req.xhr.abort) req.xhr.abort()\n  }\n\n  function parseEventStreamLine (buf, pos, fieldLength, lineLength) {\n    if (lineLength === 0) {\n      if (data.length > 0) {\n        var type = eventName || 'message'\n        _emit(type, new MessageEvent(type, {\n          data: data.slice(0, -1), // remove trailing newline\n          lastEventId: lastEventId,\n          origin: new URL(url).origin\n        }))\n        data = ''\n      }\n      eventName = void 0\n    } else if (fieldLength > 0) {\n      var noValue = fieldLength < 0\n      var step = 0\n      var field = buf.slice(pos, pos + (noValue ? lineLength : fieldLength)).toString()\n\n      if (noValue) {\n        step = lineLength\n      } else if (buf[pos + fieldLength + 1] !== space) {\n        step = fieldLength + 1\n      } else {\n        step = fieldLength + 2\n      }\n      pos += step\n\n      var valueLength = lineLength - step\n      var value = buf.slice(pos, pos + valueLength).toString()\n\n      if (field === 'data') {\n        data += value + '\\n'\n      } else if (field === 'event') {\n        eventName = value\n      } else if (field === 'id') {\n        lastEventId = value\n      } else if (field === 'retry') {\n        var retry = parseInt(value, 10)\n        if (!Number.isNaN(retry)) {\n          self.reconnectInterval = retry\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = EventSource\n\nutil.inherits(EventSource, events.EventEmitter)\nEventSource.prototype.constructor = EventSource; // make stacktraces readable\n\n['open', 'error', 'message'].forEach(function (method) {\n  Object.defineProperty(EventSource.prototype, 'on' + method, {\n    /**\n     * Returns the current listener\n     *\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    get: function get () {\n      var listener = this.listeners(method)[0]\n      return listener ? (listener._listener ? listener._listener : listener) : undefined\n    },\n\n    /**\n     * Start listening for events\n     *\n     * @param {Function} listener the listener\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    set: function set (listener) {\n      this.removeAllListeners(method)\n      this.addEventListener(method, listener)\n    }\n  })\n})\n\n/**\n * Ready states\n */\nObject.defineProperty(EventSource, 'CONNECTING', {enumerable: true, value: 0})\nObject.defineProperty(EventSource, 'OPEN', {enumerable: true, value: 1})\nObject.defineProperty(EventSource, 'CLOSED', {enumerable: true, value: 2})\n\nEventSource.prototype.CONNECTING = 0\nEventSource.prototype.OPEN = 1\nEventSource.prototype.CLOSED = 2\n\n/**\n * Closes the connection, if one is made, and sets the readyState attribute to 2 (closed)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventSource/close\n * @api public\n */\nEventSource.prototype.close = function () {\n  this._close()\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using addEventListener.\n *\n * @param {String} type A string representing the event type to listen out for\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.addEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.addEventListener = function addEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    // store a reference so we can return the original function again\n    listener._listener = listener\n    this.on(type, listener)\n  }\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using dispatchEvent.\n *\n * @param {Event} event An event to be dispatched\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/dispatchEvent\n * @api public\n */\nEventSource.prototype.dispatchEvent = function dispatchEvent (event) {\n  if (!event.type) {\n    throw new Error('UNSPECIFIED_EVENT_TYPE_ERR')\n  }\n  // if event is instance of an CustomEvent (or has 'details' property),\n  // send the detail object as the payload for the event\n  this.emit(event.type, event.detail)\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using removeEventListener.\n *\n * @param {String} type A string representing the event type to remove\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.removeEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.removeEventListener = function removeEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    listener._listener = undefined\n    this.removeListener(type, listener)\n  }\n}\n\n/**\n * W3C Event\n *\n * @see http://www.w3.org/TR/DOM-Level-3-Events/#interface-Event\n * @api private\n */\nfunction Event (type, optionalProperties) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  if (optionalProperties) {\n    for (var f in optionalProperties) {\n      if (optionalProperties.hasOwnProperty(f)) {\n        Object.defineProperty(this, f, { writable: false, value: optionalProperties[f], enumerable: true })\n      }\n    }\n  }\n}\n\n/**\n * W3C MessageEvent\n *\n * @see http://www.w3.org/TR/webmessaging/#event-definitions\n * @api private\n */\nfunction MessageEvent (type, eventInitDict) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  for (var f in eventInitDict) {\n    if (eventInitDict.hasOwnProperty(f)) {\n      Object.defineProperty(this, f, { writable: false, value: eventInitDict[f], enumerable: true })\n    }\n  }\n}\n\n/**\n * Returns a new object of headers that does not include any authorization and cookie headers\n *\n * @param {Object} headers An object of headers ({[headerName]: headerValue})\n * @return {Object} a new object of headers\n * @api private\n */\nfunction removeUnsafeHeaders (headers) {\n  var safe = {}\n  for (var key in headers) {\n    if (reUnsafeHeader.test(key)) {\n      continue\n    }\n\n    safe[key] = headers[key]\n  }\n\n  return safe\n}\n"], "names": [], "mappings": "AAAA,IAAI,QAAQ,iEAAe,KAAK;AAChC,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,eAAe;IACjB;IAAO;IAAO;IAAc;IAAQ;IAAM;IAC1C;IAAsB;IAAkB;IAAc;CACvD;AAED,IAAI,MAAM;IAAC;IAAK;IAAK;CAAI;AACzB,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,4DAA4D;AAC5D,IAAI,2BAA2B,OAAO;AACtC,sFAAsF;AACtF,IAAI,iBAAiB;AAErB,SAAS,OAAQ,GAAG;IAClB,OAAO,IAAI,KAAK,CAAC,SAAU,QAAQ,EAAE,KAAK;QACxC,OAAO,GAAG,CAAC,MAAM,KAAK;IACxB;AACF;AAEA;;;;;;EAME,GACF,SAAS,YAAa,GAAG,EAAE,mBAAmB;IAC5C,IAAI,aAAa,YAAY,UAAU;IACvC,IAAI,UAAU,uBAAuB,oBAAoB,OAAO;IAChE,IAAI,eAAe;IACnB,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;QACxC,KAAK;YACH,OAAO;QACT;IACF;IAEA,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO;QACjC,KAAK;YACH,OAAO;QACT;IACF;IAEA,IAAI,OAAO,IAAI;IACf,KAAK,iBAAiB,GAAG;IACzB,KAAK,oBAAoB,GAAG;IAE5B,SAAS,mBAAoB,OAAO;QAClC,IAAI,eAAe,YAAY,MAAM,EAAE;QACvC,aAAa,YAAY,UAAU;QACnC,MAAM,SAAS,IAAI,MAAM,SAAS;YAAC,SAAS;QAAO;QAEnD,6EAA6E;QAC7E,yEAAyE;QACzE,IAAI,cAAc;YAChB,MAAM;YACN,eAAe;YACf,eAAe;QACjB;QACA,WAAW;YACT,IAAI,eAAe,YAAY,UAAU,IAAI,KAAK,oBAAoB,EAAE;gBACtE;YACF;YACA,KAAK,oBAAoB,GAAG;YAC5B;QACF,GAAG,KAAK,iBAAiB;IAC3B;IAEA,IAAI;IACJ,IAAI,cAAc;IAClB,IAAI,WAAW,OAAO,CAAC,gBAAgB,EAAE;QACvC,cAAc,OAAO,CAAC,gBAAgB;QACtC,OAAO,OAAO,CAAC,gBAAgB;IACjC;IAEA,IAAI,yBAAyB;IAC7B,IAAI,OAAO;IACX,IAAI,YAAY;IAEhB,IAAI,eAAe;IAEnB,SAAS;QACP,IAAI,UAAU,MAAM;QACpB,IAAI,WAAW,QAAQ,QAAQ,KAAK;QACpC,QAAQ,OAAO,GAAG;YAAE,iBAAiB;YAAY,UAAU;QAAoB;QAC/E,IAAI,aAAa,QAAQ,OAAO,CAAC,gBAAgB,GAAG;QACpD,IAAI,SAAS;YACX,IAAI,aAAa,eAAe,oBAAoB,WAAW;YAC/D,IAAK,IAAI,KAAK,WAAY;gBACxB,IAAI,SAAS,UAAU,CAAC,EAAE;gBAC1B,IAAI,QAAQ;oBACV,QAAQ,OAAO,CAAC,EAAE,GAAG;gBACvB;YACF;QACF;QAEA,sFAAsF;QACtF,wDAAwD;QACxD,QAAQ,kBAAkB,GAAG,CAAC,CAAC,uBAAuB,CAAC,oBAAoB,kBAAkB;QAE7F,IAAI,uBAAuB,oBAAoB,gBAAgB,KAAK,WAAW;YAC7E,QAAQ,gBAAgB,GAAG,oBAAoB,gBAAgB;QACjE;QAEA,uEAAuE;QACvE,wDAAwD;QACxD,IAAI,WAAW,uBAAuB,oBAAoB,KAAK;QAC/D,IAAI,UAAU;YACZ,IAAI,QAAQ,MAAM,oBAAoB,KAAK;YAC3C,WAAW,MAAM,QAAQ,KAAK;YAE9B,QAAQ,QAAQ,GAAG,WAAW,WAAW;YACzC,QAAQ,IAAI,GAAG;YACf,QAAQ,OAAO,CAAC,IAAI,GAAG,QAAQ,IAAI;YACnC,QAAQ,QAAQ,GAAG,MAAM,QAAQ;YACjC,QAAQ,IAAI,GAAG,MAAM,IAAI;YACzB,QAAQ,IAAI,GAAG,MAAM,IAAI;QAC3B;QAEA,sEAAsE;QACtE,IAAI,uBAAuB,oBAAoB,KAAK,EAAE;YACpD,IAAK,IAAI,WAAW,oBAAoB,KAAK,CAAE;gBAC7C,IAAI,aAAa,OAAO,CAAC,aAAa,CAAC,GAAG;oBACxC;gBACF;gBAEA,IAAI,SAAS,oBAAoB,KAAK,CAAC,QAAQ;gBAC/C,IAAI,WAAW,WAAW;oBACxB,OAAO,CAAC,QAAQ,GAAG;gBACrB;YACF;QACF;QAEA,0BAA0B;QAC1B,IAAI,uBAAuB,oBAAoB,eAAe,KAAK,WAAW;YAC5E,QAAQ,eAAe,GAAG,oBAAoB,eAAe;QAC/D;QAEA,MAAM,CAAC,WAAW,QAAQ,IAAI,EAAE,OAAO,CAAC,SAAS,SAAU,GAAG;YAC5D,KAAK,oBAAoB,GAAG;YAC5B,qBAAqB;YACrB,IAAI,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,KAAK;gBACxG,MAAM,SAAS,IAAI,MAAM,SAAS;oBAAC,QAAQ,IAAI,UAAU;oBAAE,SAAS,IAAI,aAAa;gBAAA;gBACrF;gBACA;YACF;YAEA,wBAAwB;YACxB,IAAI,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,KAAK;gBAC9E,IAAI,WAAW,IAAI,OAAO,CAAC,QAAQ;gBACnC,IAAI,CAAC,UAAU;oBACb,yDAAyD;oBACzD,MAAM,SAAS,IAAI,MAAM,SAAS;wBAAC,QAAQ,IAAI,UAAU;wBAAE,SAAS,IAAI,aAAa;oBAAA;oBACrF;gBACF;gBACA,IAAI,aAAa,IAAI,IAAI,KAAK,MAAM;gBACpC,IAAI,aAAa,IAAI,IAAI,UAAU,MAAM;gBACzC,eAAe,eAAe;gBAC9B,IAAI,IAAI,UAAU,KAAK,KAAK,eAAe;gBAC3C,MAAM;gBACN,QAAQ,QAAQ,CAAC;gBACjB;YACF;YAEA,IAAI,IAAI,UAAU,KAAK,KAAK;gBAC1B,MAAM,SAAS,IAAI,MAAM,SAAS;oBAAC,QAAQ,IAAI,UAAU;oBAAE,SAAS,IAAI,aAAa;gBAAA;gBACrF,OAAO,KAAK,KAAK;YACnB;YAEA,aAAa,YAAY,IAAI;YAC7B,IAAI,EAAE,CAAC,SAAS;gBACd,IAAI,kBAAkB,CAAC;gBACvB,IAAI,kBAAkB,CAAC;gBACvB;YACF;YAEA,IAAI,EAAE,CAAC,OAAO;gBACZ,IAAI,kBAAkB,CAAC;gBACvB,IAAI,kBAAkB,CAAC;gBACvB;YACF;YACA,MAAM,QAAQ,IAAI,MAAM;YAExB,iDAAiD;YACjD,sCAAsC;YACtC,IAAI;YACJ,IAAI;YACJ,IAAI,cAAc;YAClB,IAAI,sBAAsB,CAAC;YAC3B,IAAI,gBAAgB;YACpB,IAAI,YAAY;YAEhB,IAAI,EAAE,CAAC,QAAQ,SAAU,KAAK;gBAC5B,IAAI,CAAC,KAAK;oBACR,MAAM;oBACN,IAAI,OAAO,MAAM;wBACf,MAAM,IAAI,KAAK,CAAC,IAAI,MAAM;oBAC5B;oBACA,YAAY,IAAI,MAAM;gBACxB,OAAO;oBACL,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,GAAG,WAAW;wBACzC,gBAAgB,AAAC,IAAI,MAAM,GAAG,IAAK,MAAM,MAAM;wBAC/C,IAAI,gBAAgB,0BAA0B;4BAC5C,gBAAgB,IAAI,MAAM,GAAG,MAAM,MAAM,GAAG;wBAC9C;wBACA,YAAY,OAAO,KAAK,CAAC;wBACzB,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG;wBAC1B,MAAM;oBACR;oBACA,MAAM,IAAI,CAAC,KAAK;oBAChB,aAAa,MAAM,MAAM;gBAC3B;gBAEA,IAAI,MAAM;gBACV,IAAI,SAAS;gBAEb,MAAO,MAAM,OAAQ;oBACnB,IAAI,wBAAwB;wBAC1B,IAAI,GAAG,CAAC,IAAI,KAAK,UAAU;4BACzB,EAAE;wBACJ;wBACA,yBAAyB;oBAC3B;oBAEA,IAAI,aAAa,CAAC;oBAClB,IAAI,cAAc;oBAClB,IAAI;oBAEJ,IAAK,IAAI,IAAI,aAAa,aAAa,KAAK,IAAI,QAAQ,EAAE,EAAG;wBAC3D,IAAI,GAAG,CAAC,EAAE;wBACV,IAAI,MAAM,OAAO;4BACf,IAAI,cAAc,GAAG;gCACnB,cAAc,IAAI;4BACpB;wBACF,OAAO,IAAI,MAAM,gBAAgB;4BAC/B,yBAAyB;4BACzB,aAAa,IAAI;wBACnB,OAAO,IAAI,MAAM,UAAU;4BACzB,aAAa,IAAI;wBACnB;oBACF;oBAEA,IAAI,aAAa,GAAG;wBAClB,cAAc,SAAS;wBACvB,sBAAsB;wBACtB;oBACF,OAAO;wBACL,cAAc;wBACd,sBAAsB,CAAC;oBACzB;oBAEA,qBAAqB,KAAK,KAAK,aAAa;oBAE5C,OAAO,aAAa;gBACtB;gBAEA,IAAI,QAAQ,QAAQ;oBAClB,MAAM,KAAK;oBACX,YAAY;gBACd,OAAO,IAAI,MAAM,GAAG;oBAClB,MAAM,IAAI,KAAK,CAAC,KAAK;oBACrB,YAAY,IAAI,MAAM;gBACxB;YACF;QACF;QAEA,IAAI,EAAE,CAAC,SAAS,SAAU,GAAG;YAC3B,KAAK,oBAAoB,GAAG;YAC5B,mBAAmB,IAAI,OAAO;QAChC;QAEA,IAAI,IAAI,UAAU,EAAE,IAAI,UAAU,CAAC;QACnC,IAAI,GAAG;IACT;IAEA;IAEA,SAAS;QACP,IAAI,KAAK,SAAS,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,GAAG,GAAG;YAC3C,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;QACxB;IACF;IAEA,IAAI,CAAC,MAAM,GAAG;QACZ,IAAI,eAAe,YAAY,MAAM,EAAE;QACvC,aAAa,YAAY,MAAM;QAC/B,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK;QACxB,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,KAAK;IAC7C;IAEA,SAAS,qBAAsB,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,UAAU;QAC9D,IAAI,eAAe,GAAG;YACpB,IAAI,KAAK,MAAM,GAAG,GAAG;gBACnB,IAAI,OAAO,aAAa;gBACxB,MAAM,MAAM,IAAI,aAAa,MAAM;oBACjC,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC;oBACrB,aAAa;oBACb,QAAQ,IAAI,IAAI,KAAK,MAAM;gBAC7B;gBACA,OAAO;YACT;YACA,YAAY,KAAK;QACnB,OAAO,IAAI,cAAc,GAAG;YAC1B,IAAI,UAAU,cAAc;YAC5B,IAAI,OAAO;YACX,IAAI,QAAQ,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,UAAU,aAAa,WAAW,GAAG,QAAQ;YAE/E,IAAI,SAAS;gBACX,OAAO;YACT,OAAO,IAAI,GAAG,CAAC,MAAM,cAAc,EAAE,KAAK,OAAO;gBAC/C,OAAO,cAAc;YACvB,OAAO;gBACL,OAAO,cAAc;YACvB;YACA,OAAO;YAEP,IAAI,cAAc,aAAa;YAC/B,IAAI,QAAQ,IAAI,KAAK,CAAC,KAAK,MAAM,aAAa,QAAQ;YAEtD,IAAI,UAAU,QAAQ;gBACpB,QAAQ,QAAQ;YAClB,OAAO,IAAI,UAAU,SAAS;gBAC5B,YAAY;YACd,OAAO,IAAI,UAAU,MAAM;gBACzB,cAAc;YAChB,OAAO,IAAI,UAAU,SAAS;gBAC5B,IAAI,QAAQ,SAAS,OAAO;gBAC5B,IAAI,CAAC,OAAO,KAAK,CAAC,QAAQ;oBACxB,KAAK,iBAAiB,GAAG;gBAC3B;YACF;QACF;IACF;AACF;AAEA,OAAO,OAAO,GAAG;AAEjB,KAAK,QAAQ,CAAC,aAAa,OAAO,YAAY;AAC9C,YAAY,SAAS,CAAC,WAAW,GAAG,aAAa,4BAA4B;AAE7E;IAAC;IAAQ;IAAS;CAAU,CAAC,OAAO,CAAC,SAAU,MAAM;IACnD,OAAO,cAAc,CAAC,YAAY,SAAS,EAAE,OAAO,QAAQ;QAC1D;;;;;KAKC,GACD,KAAK,SAAS;YACZ,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YACxC,OAAO,WAAY,SAAS,SAAS,GAAG,SAAS,SAAS,GAAG,WAAY;QAC3E;QAEA;;;;;;KAMC,GACD,KAAK,SAAS,IAAK,QAAQ;YACzB,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QAChC;IACF;AACF;AAEA;;CAEC,GACD,OAAO,cAAc,CAAC,aAAa,cAAc;IAAC,YAAY;IAAM,OAAO;AAAC;AAC5E,OAAO,cAAc,CAAC,aAAa,QAAQ;IAAC,YAAY;IAAM,OAAO;AAAC;AACtE,OAAO,cAAc,CAAC,aAAa,UAAU;IAAC,YAAY;IAAM,OAAO;AAAC;AAExE,YAAY,SAAS,CAAC,UAAU,GAAG;AACnC,YAAY,SAAS,CAAC,IAAI,GAAG;AAC7B,YAAY,SAAS,CAAC,MAAM,GAAG;AAE/B;;;;;CAKC,GACD,YAAY,SAAS,CAAC,KAAK,GAAG;IAC5B,IAAI,CAAC,MAAM;AACb;AAEA;;;;;;;;CAQC,GACD,YAAY,SAAS,CAAC,gBAAgB,GAAG,SAAS,iBAAkB,IAAI,EAAE,QAAQ;IAChF,IAAI,OAAO,aAAa,YAAY;QAClC,iEAAiE;QACjE,SAAS,SAAS,GAAG;QACrB,IAAI,CAAC,EAAE,CAAC,MAAM;IAChB;AACF;AAEA;;;;;;CAMC,GACD,YAAY,SAAS,CAAC,aAAa,GAAG,SAAS,cAAe,KAAK;IACjE,IAAI,CAAC,MAAM,IAAI,EAAE;QACf,MAAM,IAAI,MAAM;IAClB;IACA,sEAAsE;IACtE,sDAAsD;IACtD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,MAAM,MAAM;AACpC;AAEA;;;;;;;;CAQC,GACD,YAAY,SAAS,CAAC,mBAAmB,GAAG,SAAS,oBAAqB,IAAI,EAAE,QAAQ;IACtF,IAAI,OAAO,aAAa,YAAY;QAClC,SAAS,SAAS,GAAG;QACrB,IAAI,CAAC,cAAc,CAAC,MAAM;IAC5B;AACF;AAEA;;;;;CAKC,GACD,SAAS,MAAO,IAAI,EAAE,kBAAkB;IACtC,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;QAAE,UAAU;QAAO,OAAO;QAAM,YAAY;IAAK;IACrF,IAAI,oBAAoB;QACtB,IAAK,IAAI,KAAK,mBAAoB;YAChC,IAAI,mBAAmB,cAAc,CAAC,IAAI;gBACxC,OAAO,cAAc,CAAC,IAAI,EAAE,GAAG;oBAAE,UAAU;oBAAO,OAAO,kBAAkB,CAAC,EAAE;oBAAE,YAAY;gBAAK;YACnG;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,aAAc,IAAI,EAAE,aAAa;IACxC,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;QAAE,UAAU;QAAO,OAAO;QAAM,YAAY;IAAK;IACrF,IAAK,IAAI,KAAK,cAAe;QAC3B,IAAI,cAAc,cAAc,CAAC,IAAI;YACnC,OAAO,cAAc,CAAC,IAAI,EAAE,GAAG;gBAAE,UAAU;gBAAO,OAAO,aAAa,CAAC,EAAE;gBAAE,YAAY;YAAK;QAC9F;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS,oBAAqB,OAAO;IACnC,IAAI,OAAO,CAAC;IACZ,IAAK,IAAI,OAAO,QAAS;QACvB,IAAI,eAAe,IAAI,CAAC,MAAM;YAC5B;QACF;QAEA,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;IAC1B;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/isomorphic-eventsource/index.mjs"], "sourcesContent": ["import EventSource from 'eventsource';\n\nif (!global.EventSource) {\n    global.EventSource = EventSource;\n}\n"], "names": [], "mappings": ";AAAA;;AAEA,IAAI,CAAC,OAAO,WAAW,EAAE;IACrB,OAAO,WAAW,GAAG,iJAAA,CAAA,UAAW;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/webidl-conversions/lib/index.js"], "sourcesContent": ["\"use strict\";\n\nvar conversions = {};\nmodule.exports = conversions;\n\nfunction sign(x) {\n    return x < 0 ? -1 : 1;\n}\n\nfunction evenRound(x) {\n    // Round x to the nearest integer, choosing the even integer if it lies halfway between two.\n    if ((x % 1) === 0.5 && (x & 1) === 0) { // [even number].5; round down (i.e. floor)\n        return Math.floor(x);\n    } else {\n        return Math.round(x);\n    }\n}\n\nfunction createNumberConversion(bitLength, typeOpts) {\n    if (!typeOpts.unsigned) {\n        --bitLength;\n    }\n    const lowerBound = typeOpts.unsigned ? 0 : -Math.pow(2, bitLength);\n    const upperBound = Math.pow(2, bitLength) - 1;\n\n    const moduloVal = typeOpts.moduloBitLength ? Math.pow(2, typeOpts.moduloBitLength) : Math.pow(2, bitLength);\n    const moduloBound = typeOpts.moduloBitLength ? Math.pow(2, typeOpts.moduloBitLength - 1) : Math.pow(2, bitLength - 1);\n\n    return function(V, opts) {\n        if (!opts) opts = {};\n\n        let x = +V;\n\n        if (opts.enforceRange) {\n            if (!Number.isFinite(x)) {\n                throw new TypeError(\"Argument is not a finite number\");\n            }\n\n            x = sign(x) * Math.floor(Math.abs(x));\n            if (x < lowerBound || x > upperBound) {\n                throw new TypeError(\"Argument is not in byte range\");\n            }\n\n            return x;\n        }\n\n        if (!isNaN(x) && opts.clamp) {\n            x = evenRound(x);\n\n            if (x < lowerBound) x = lowerBound;\n            if (x > upperBound) x = upperBound;\n            return x;\n        }\n\n        if (!Number.isFinite(x) || x === 0) {\n            return 0;\n        }\n\n        x = sign(x) * Math.floor(Math.abs(x));\n        x = x % moduloVal;\n\n        if (!typeOpts.unsigned && x >= moduloBound) {\n            return x - moduloVal;\n        } else if (typeOpts.unsigned) {\n            if (x < 0) {\n              x += moduloVal;\n            } else if (x === -0) { // don't return negative zero\n              return 0;\n            }\n        }\n\n        return x;\n    }\n}\n\nconversions[\"void\"] = function () {\n    return undefined;\n};\n\nconversions[\"boolean\"] = function (val) {\n    return !!val;\n};\n\nconversions[\"byte\"] = createNumberConversion(8, { unsigned: false });\nconversions[\"octet\"] = createNumberConversion(8, { unsigned: true });\n\nconversions[\"short\"] = createNumberConversion(16, { unsigned: false });\nconversions[\"unsigned short\"] = createNumberConversion(16, { unsigned: true });\n\nconversions[\"long\"] = createNumberConversion(32, { unsigned: false });\nconversions[\"unsigned long\"] = createNumberConversion(32, { unsigned: true });\n\nconversions[\"long long\"] = createNumberConversion(32, { unsigned: false, moduloBitLength: 64 });\nconversions[\"unsigned long long\"] = createNumberConversion(32, { unsigned: true, moduloBitLength: 64 });\n\nconversions[\"double\"] = function (V) {\n    const x = +V;\n\n    if (!Number.isFinite(x)) {\n        throw new TypeError(\"Argument is not a finite floating-point value\");\n    }\n\n    return x;\n};\n\nconversions[\"unrestricted double\"] = function (V) {\n    const x = +V;\n\n    if (isNaN(x)) {\n        throw new TypeError(\"Argument is NaN\");\n    }\n\n    return x;\n};\n\n// not quite valid, but good enough for JS\nconversions[\"float\"] = conversions[\"double\"];\nconversions[\"unrestricted float\"] = conversions[\"unrestricted double\"];\n\nconversions[\"DOMString\"] = function (V, opts) {\n    if (!opts) opts = {};\n\n    if (opts.treatNullAsEmptyString && V === null) {\n        return \"\";\n    }\n\n    return String(V);\n};\n\nconversions[\"ByteString\"] = function (V, opts) {\n    const x = String(V);\n    let c = undefined;\n    for (let i = 0; (c = x.codePointAt(i)) !== undefined; ++i) {\n        if (c > 255) {\n            throw new TypeError(\"Argument is not a valid bytestring\");\n        }\n    }\n\n    return x;\n};\n\nconversions[\"USVString\"] = function (V) {\n    const S = String(V);\n    const n = S.length;\n    const U = [];\n    for (let i = 0; i < n; ++i) {\n        const c = S.charCodeAt(i);\n        if (c < 0xD800 || c > 0xDFFF) {\n            U.push(String.fromCodePoint(c));\n        } else if (0xDC00 <= c && c <= 0xDFFF) {\n            U.push(String.fromCodePoint(0xFFFD));\n        } else {\n            if (i === n - 1) {\n                U.push(String.fromCodePoint(0xFFFD));\n            } else {\n                const d = S.charCodeAt(i + 1);\n                if (0xDC00 <= d && d <= 0xDFFF) {\n                    const a = c & 0x3FF;\n                    const b = d & 0x3FF;\n                    U.push(String.fromCodePoint((2 << 15) + (2 << 9) * a + b));\n                    ++i;\n                } else {\n                    U.push(String.fromCodePoint(0xFFFD));\n                }\n            }\n        }\n    }\n\n    return U.join('');\n};\n\nconversions[\"Date\"] = function (V, opts) {\n    if (!(V instanceof Date)) {\n        throw new TypeError(\"Argument is not a Date object\");\n    }\n    if (isNaN(V)) {\n        return undefined;\n    }\n\n    return V;\n};\n\nconversions[\"RegExp\"] = function (V, opts) {\n    if (!(V instanceof RegExp)) {\n        V = new RegExp(V);\n    }\n\n    return V;\n};\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,cAAc,CAAC;AACnB,OAAO,OAAO,GAAG;AAEjB,SAAS,KAAK,CAAC;IACX,OAAO,IAAI,IAAI,CAAC,IAAI;AACxB;AAEA,SAAS,UAAU,CAAC;IAChB,4FAA4F;IAC5F,IAAI,AAAC,IAAI,MAAO,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG;QAClC,OAAO,KAAK,KAAK,CAAC;IACtB,OAAO;QACH,OAAO,KAAK,KAAK,CAAC;IACtB;AACJ;AAEA,SAAS,uBAAuB,SAAS,EAAE,QAAQ;IAC/C,IAAI,CAAC,SAAS,QAAQ,EAAE;QACpB,EAAE;IACN;IACA,MAAM,aAAa,SAAS,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG;IACxD,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,aAAa;IAE5C,MAAM,YAAY,SAAS,eAAe,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,eAAe,IAAI,KAAK,GAAG,CAAC,GAAG;IACjG,MAAM,cAAc,SAAS,eAAe,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,eAAe,GAAG,KAAK,KAAK,GAAG,CAAC,GAAG,YAAY;IAEnH,OAAO,SAAS,CAAC,EAAE,IAAI;QACnB,IAAI,CAAC,MAAM,OAAO,CAAC;QAEnB,IAAI,IAAI,CAAC;QAET,IAAI,KAAK,YAAY,EAAE;YACnB,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI;gBACrB,MAAM,IAAI,UAAU;YACxB;YAEA,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;YAClC,IAAI,IAAI,cAAc,IAAI,YAAY;gBAClC,MAAM,IAAI,UAAU;YACxB;YAEA,OAAO;QACX;QAEA,IAAI,CAAC,MAAM,MAAM,KAAK,KAAK,EAAE;YACzB,IAAI,UAAU;YAEd,IAAI,IAAI,YAAY,IAAI;YACxB,IAAI,IAAI,YAAY,IAAI;YACxB,OAAO;QACX;QAEA,IAAI,CAAC,OAAO,QAAQ,CAAC,MAAM,MAAM,GAAG;YAChC,OAAO;QACX;QAEA,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;QAClC,IAAI,IAAI;QAER,IAAI,CAAC,SAAS,QAAQ,IAAI,KAAK,aAAa;YACxC,OAAO,IAAI;QACf,OAAO,IAAI,SAAS,QAAQ,EAAE;YAC1B,IAAI,IAAI,GAAG;gBACT,KAAK;YACP,OAAO,IAAI,MAAM,CAAC,GAAG;gBACnB,OAAO;YACT;QACJ;QAEA,OAAO;IACX;AACJ;AAEA,WAAW,CAAC,OAAO,GAAG;IAClB,OAAO;AACX;AAEA,WAAW,CAAC,UAAU,GAAG,SAAU,GAAG;IAClC,OAAO,CAAC,CAAC;AACb;AAEA,WAAW,CAAC,OAAO,GAAG,uBAAuB,GAAG;IAAE,UAAU;AAAM;AAClE,WAAW,CAAC,QAAQ,GAAG,uBAAuB,GAAG;IAAE,UAAU;AAAK;AAElE,WAAW,CAAC,QAAQ,GAAG,uBAAuB,IAAI;IAAE,UAAU;AAAM;AACpE,WAAW,CAAC,iBAAiB,GAAG,uBAAuB,IAAI;IAAE,UAAU;AAAK;AAE5E,WAAW,CAAC,OAAO,GAAG,uBAAuB,IAAI;IAAE,UAAU;AAAM;AACnE,WAAW,CAAC,gBAAgB,GAAG,uBAAuB,IAAI;IAAE,UAAU;AAAK;AAE3E,WAAW,CAAC,YAAY,GAAG,uBAAuB,IAAI;IAAE,UAAU;IAAO,iBAAiB;AAAG;AAC7F,WAAW,CAAC,qBAAqB,GAAG,uBAAuB,IAAI;IAAE,UAAU;IAAM,iBAAiB;AAAG;AAErG,WAAW,CAAC,SAAS,GAAG,SAAU,CAAC;IAC/B,MAAM,IAAI,CAAC;IAEX,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI;QACrB,MAAM,IAAI,UAAU;IACxB;IAEA,OAAO;AACX;AAEA,WAAW,CAAC,sBAAsB,GAAG,SAAU,CAAC;IAC5C,MAAM,IAAI,CAAC;IAEX,IAAI,MAAM,IAAI;QACV,MAAM,IAAI,UAAU;IACxB;IAEA,OAAO;AACX;AAEA,0CAA0C;AAC1C,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,SAAS;AAC5C,WAAW,CAAC,qBAAqB,GAAG,WAAW,CAAC,sBAAsB;AAEtE,WAAW,CAAC,YAAY,GAAG,SAAU,CAAC,EAAE,IAAI;IACxC,IAAI,CAAC,MAAM,OAAO,CAAC;IAEnB,IAAI,KAAK,sBAAsB,IAAI,MAAM,MAAM;QAC3C,OAAO;IACX;IAEA,OAAO,OAAO;AAClB;AAEA,WAAW,CAAC,aAAa,GAAG,SAAU,CAAC,EAAE,IAAI;IACzC,MAAM,IAAI,OAAO;IACjB,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,MAAM,WAAW,EAAE,EAAG;QACvD,IAAI,IAAI,KAAK;YACT,MAAM,IAAI,UAAU;QACxB;IACJ;IAEA,OAAO;AACX;AAEA,WAAW,CAAC,YAAY,GAAG,SAAU,CAAC;IAClC,MAAM,IAAI,OAAO;IACjB,MAAM,IAAI,EAAE,MAAM;IAClB,MAAM,IAAI,EAAE;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACxB,MAAM,IAAI,EAAE,UAAU,CAAC;QACvB,IAAI,IAAI,UAAU,IAAI,QAAQ;YAC1B,EAAE,IAAI,CAAC,OAAO,aAAa,CAAC;QAChC,OAAO,IAAI,UAAU,KAAK,KAAK,QAAQ;YACnC,EAAE,IAAI,CAAC,OAAO,aAAa,CAAC;QAChC,OAAO;YACH,IAAI,MAAM,IAAI,GAAG;gBACb,EAAE,IAAI,CAAC,OAAO,aAAa,CAAC;YAChC,OAAO;gBACH,MAAM,IAAI,EAAE,UAAU,CAAC,IAAI;gBAC3B,IAAI,UAAU,KAAK,KAAK,QAAQ;oBAC5B,MAAM,IAAI,IAAI;oBACd,MAAM,IAAI,IAAI;oBACd,EAAE,IAAI,CAAC,OAAO,aAAa,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI;oBACvD,EAAE;gBACN,OAAO;oBACH,EAAE,IAAI,CAAC,OAAO,aAAa,CAAC;gBAChC;YACJ;QACJ;IACJ;IAEA,OAAO,EAAE,IAAI,CAAC;AAClB;AAEA,WAAW,CAAC,OAAO,GAAG,SAAU,CAAC,EAAE,IAAI;IACnC,IAAI,CAAC,CAAC,aAAa,IAAI,GAAG;QACtB,MAAM,IAAI,UAAU;IACxB;IACA,IAAI,MAAM,IAAI;QACV,OAAO;IACX;IAEA,OAAO;AACX;AAEA,WAAW,CAAC,SAAS,GAAG,SAAU,CAAC,EAAE,IAAI;IACrC,IAAI,CAAC,CAAC,aAAa,MAAM,GAAG;QACxB,IAAI,IAAI,OAAO;IACnB;IAEA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/whatwg-url/lib/utils.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports.mixin = function mixin(target, source) {\n  const keys = Object.getOwnPropertyNames(source);\n  for (let i = 0; i < keys.length; ++i) {\n    Object.defineProperty(target, keys[i], Object.getOwnPropertyDescriptor(source, keys[i]));\n  }\n};\n\nmodule.exports.wrapperSymbol = Symbol(\"wrapper\");\nmodule.exports.implSymbol = Symbol(\"impl\");\n\nmodule.exports.wrapperForImpl = function (impl) {\n  return impl[module.exports.wrapperSymbol];\n};\n\nmodule.exports.implForWrapper = function (wrapper) {\n  return wrapper[module.exports.implSymbol];\n};\n\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,CAAC,KAAK,GAAG,SAAS,MAAM,MAAM,EAAE,MAAM;IAClD,MAAM,OAAO,OAAO,mBAAmB,CAAC;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;QACpC,OAAO,cAAc,CAAC,QAAQ,IAAI,CAAC,EAAE,EAAE,OAAO,wBAAwB,CAAC,QAAQ,IAAI,CAAC,EAAE;IACxF;AACF;AAEA,OAAO,OAAO,CAAC,aAAa,GAAG,OAAO;AACtC,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO;AAEnC,OAAO,OAAO,CAAC,cAAc,GAAG,SAAU,IAAI;IAC5C,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,aAAa,CAAC;AAC3C;AAEA,OAAO,OAAO,CAAC,cAAc,GAAG,SAAU,OAAO;IAC/C,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,UAAU,CAAC;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/whatwg-url/lib/url-state-machine.js"], "sourcesContent": ["\"use strict\";\r\nconst punycode = require(\"punycode\");\r\nconst tr46 = require(\"tr46\");\r\n\r\nconst specialSchemes = {\r\n  ftp: 21,\r\n  file: null,\r\n  gopher: 70,\r\n  http: 80,\r\n  https: 443,\r\n  ws: 80,\r\n  wss: 443\r\n};\r\n\r\nconst failure = Symbol(\"failure\");\r\n\r\nfunction countSymbols(str) {\r\n  return punycode.ucs2.decode(str).length;\r\n}\r\n\r\nfunction at(input, idx) {\r\n  const c = input[idx];\r\n  return isNaN(c) ? undefined : String.fromCodePoint(c);\r\n}\r\n\r\nfunction isASCIIDigit(c) {\r\n  return c >= 0x30 && c <= 0x39;\r\n}\r\n\r\nfunction isASCIIAlpha(c) {\r\n  return (c >= 0x41 && c <= 0x5A) || (c >= 0x61 && c <= 0x7A);\r\n}\r\n\r\nfunction isASCIIAlphanumeric(c) {\r\n  return isASCIIAlpha(c) || isASCIIDigit(c);\r\n}\r\n\r\nfunction isASCIIHex(c) {\r\n  return isASCIIDigit(c) || (c >= 0x41 && c <= 0x46) || (c >= 0x61 && c <= 0x66);\r\n}\r\n\r\nfunction isSingleDot(buffer) {\r\n  return buffer === \".\" || buffer.toLowerCase() === \"%2e\";\r\n}\r\n\r\nfunction isDoubleDot(buffer) {\r\n  buffer = buffer.toLowerCase();\r\n  return buffer === \"..\" || buffer === \"%2e.\" || buffer === \".%2e\" || buffer === \"%2e%2e\";\r\n}\r\n\r\nfunction isWindowsDriveLetterCodePoints(cp1, cp2) {\r\n  return isASCIIAlpha(cp1) && (cp2 === 58 || cp2 === 124);\r\n}\r\n\r\nfunction isWindowsDriveLetterString(string) {\r\n  return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && (string[1] === \":\" || string[1] === \"|\");\r\n}\r\n\r\nfunction isNormalizedWindowsDriveLetterString(string) {\r\n  return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && string[1] === \":\";\r\n}\r\n\r\nfunction containsForbiddenHostCodePoint(string) {\r\n  return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|%|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\r\n}\r\n\r\nfunction containsForbiddenHostCodePointExcludingPercent(string) {\r\n  return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\r\n}\r\n\r\nfunction isSpecialScheme(scheme) {\r\n  return specialSchemes[scheme] !== undefined;\r\n}\r\n\r\nfunction isSpecial(url) {\r\n  return isSpecialScheme(url.scheme);\r\n}\r\n\r\nfunction defaultPort(scheme) {\r\n  return specialSchemes[scheme];\r\n}\r\n\r\nfunction percentEncode(c) {\r\n  let hex = c.toString(16).toUpperCase();\r\n  if (hex.length === 1) {\r\n    hex = \"0\" + hex;\r\n  }\r\n\r\n  return \"%\" + hex;\r\n}\r\n\r\nfunction utf8PercentEncode(c) {\r\n  const buf = new Buffer(c);\r\n\r\n  let str = \"\";\r\n\r\n  for (let i = 0; i < buf.length; ++i) {\r\n    str += percentEncode(buf[i]);\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\nfunction utf8PercentDecode(str) {\r\n  const input = new Buffer(str);\r\n  const output = [];\r\n  for (let i = 0; i < input.length; ++i) {\r\n    if (input[i] !== 37) {\r\n      output.push(input[i]);\r\n    } else if (input[i] === 37 && isASCIIHex(input[i + 1]) && isASCIIHex(input[i + 2])) {\r\n      output.push(parseInt(input.slice(i + 1, i + 3).toString(), 16));\r\n      i += 2;\r\n    } else {\r\n      output.push(input[i]);\r\n    }\r\n  }\r\n  return new Buffer(output).toString();\r\n}\r\n\r\nfunction isC0ControlPercentEncode(c) {\r\n  return c <= 0x1F || c > 0x7E;\r\n}\r\n\r\nconst extraPathPercentEncodeSet = new Set([32, 34, 35, 60, 62, 63, 96, 123, 125]);\r\nfunction isPathPercentEncode(c) {\r\n  return isC0ControlPercentEncode(c) || extraPathPercentEncodeSet.has(c);\r\n}\r\n\r\nconst extraUserinfoPercentEncodeSet =\r\n  new Set([47, 58, 59, 61, 64, 91, 92, 93, 94, 124]);\r\nfunction isUserinfoPercentEncode(c) {\r\n  return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);\r\n}\r\n\r\nfunction percentEncodeChar(c, encodeSetPredicate) {\r\n  const cStr = String.fromCodePoint(c);\r\n\r\n  if (encodeSetPredicate(c)) {\r\n    return utf8PercentEncode(cStr);\r\n  }\r\n\r\n  return cStr;\r\n}\r\n\r\nfunction parseIPv4Number(input) {\r\n  let R = 10;\r\n\r\n  if (input.length >= 2 && input.charAt(0) === \"0\" && input.charAt(1).toLowerCase() === \"x\") {\r\n    input = input.substring(2);\r\n    R = 16;\r\n  } else if (input.length >= 2 && input.charAt(0) === \"0\") {\r\n    input = input.substring(1);\r\n    R = 8;\r\n  }\r\n\r\n  if (input === \"\") {\r\n    return 0;\r\n  }\r\n\r\n  const regex = R === 10 ? /[^0-9]/ : (R === 16 ? /[^0-9A-Fa-f]/ : /[^0-7]/);\r\n  if (regex.test(input)) {\r\n    return failure;\r\n  }\r\n\r\n  return parseInt(input, R);\r\n}\r\n\r\nfunction parseIPv4(input) {\r\n  const parts = input.split(\".\");\r\n  if (parts[parts.length - 1] === \"\") {\r\n    if (parts.length > 1) {\r\n      parts.pop();\r\n    }\r\n  }\r\n\r\n  if (parts.length > 4) {\r\n    return input;\r\n  }\r\n\r\n  const numbers = [];\r\n  for (const part of parts) {\r\n    if (part === \"\") {\r\n      return input;\r\n    }\r\n    const n = parseIPv4Number(part);\r\n    if (n === failure) {\r\n      return input;\r\n    }\r\n\r\n    numbers.push(n);\r\n  }\r\n\r\n  for (let i = 0; i < numbers.length - 1; ++i) {\r\n    if (numbers[i] > 255) {\r\n      return failure;\r\n    }\r\n  }\r\n  if (numbers[numbers.length - 1] >= Math.pow(256, 5 - numbers.length)) {\r\n    return failure;\r\n  }\r\n\r\n  let ipv4 = numbers.pop();\r\n  let counter = 0;\r\n\r\n  for (const n of numbers) {\r\n    ipv4 += n * Math.pow(256, 3 - counter);\r\n    ++counter;\r\n  }\r\n\r\n  return ipv4;\r\n}\r\n\r\nfunction serializeIPv4(address) {\r\n  let output = \"\";\r\n  let n = address;\r\n\r\n  for (let i = 1; i <= 4; ++i) {\r\n    output = String(n % 256) + output;\r\n    if (i !== 4) {\r\n      output = \".\" + output;\r\n    }\r\n    n = Math.floor(n / 256);\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction parseIPv6(input) {\r\n  const address = [0, 0, 0, 0, 0, 0, 0, 0];\r\n  let pieceIndex = 0;\r\n  let compress = null;\r\n  let pointer = 0;\r\n\r\n  input = punycode.ucs2.decode(input);\r\n\r\n  if (input[pointer] === 58) {\r\n    if (input[pointer + 1] !== 58) {\r\n      return failure;\r\n    }\r\n\r\n    pointer += 2;\r\n    ++pieceIndex;\r\n    compress = pieceIndex;\r\n  }\r\n\r\n  while (pointer < input.length) {\r\n    if (pieceIndex === 8) {\r\n      return failure;\r\n    }\r\n\r\n    if (input[pointer] === 58) {\r\n      if (compress !== null) {\r\n        return failure;\r\n      }\r\n      ++pointer;\r\n      ++pieceIndex;\r\n      compress = pieceIndex;\r\n      continue;\r\n    }\r\n\r\n    let value = 0;\r\n    let length = 0;\r\n\r\n    while (length < 4 && isASCIIHex(input[pointer])) {\r\n      value = value * 0x10 + parseInt(at(input, pointer), 16);\r\n      ++pointer;\r\n      ++length;\r\n    }\r\n\r\n    if (input[pointer] === 46) {\r\n      if (length === 0) {\r\n        return failure;\r\n      }\r\n\r\n      pointer -= length;\r\n\r\n      if (pieceIndex > 6) {\r\n        return failure;\r\n      }\r\n\r\n      let numbersSeen = 0;\r\n\r\n      while (input[pointer] !== undefined) {\r\n        let ipv4Piece = null;\r\n\r\n        if (numbersSeen > 0) {\r\n          if (input[pointer] === 46 && numbersSeen < 4) {\r\n            ++pointer;\r\n          } else {\r\n            return failure;\r\n          }\r\n        }\r\n\r\n        if (!isASCIIDigit(input[pointer])) {\r\n          return failure;\r\n        }\r\n\r\n        while (isASCIIDigit(input[pointer])) {\r\n          const number = parseInt(at(input, pointer));\r\n          if (ipv4Piece === null) {\r\n            ipv4Piece = number;\r\n          } else if (ipv4Piece === 0) {\r\n            return failure;\r\n          } else {\r\n            ipv4Piece = ipv4Piece * 10 + number;\r\n          }\r\n          if (ipv4Piece > 255) {\r\n            return failure;\r\n          }\r\n          ++pointer;\r\n        }\r\n\r\n        address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;\r\n\r\n        ++numbersSeen;\r\n\r\n        if (numbersSeen === 2 || numbersSeen === 4) {\r\n          ++pieceIndex;\r\n        }\r\n      }\r\n\r\n      if (numbersSeen !== 4) {\r\n        return failure;\r\n      }\r\n\r\n      break;\r\n    } else if (input[pointer] === 58) {\r\n      ++pointer;\r\n      if (input[pointer] === undefined) {\r\n        return failure;\r\n      }\r\n    } else if (input[pointer] !== undefined) {\r\n      return failure;\r\n    }\r\n\r\n    address[pieceIndex] = value;\r\n    ++pieceIndex;\r\n  }\r\n\r\n  if (compress !== null) {\r\n    let swaps = pieceIndex - compress;\r\n    pieceIndex = 7;\r\n    while (pieceIndex !== 0 && swaps > 0) {\r\n      const temp = address[compress + swaps - 1];\r\n      address[compress + swaps - 1] = address[pieceIndex];\r\n      address[pieceIndex] = temp;\r\n      --pieceIndex;\r\n      --swaps;\r\n    }\r\n  } else if (compress === null && pieceIndex !== 8) {\r\n    return failure;\r\n  }\r\n\r\n  return address;\r\n}\r\n\r\nfunction serializeIPv6(address) {\r\n  let output = \"\";\r\n  const seqResult = findLongestZeroSequence(address);\r\n  const compress = seqResult.idx;\r\n  let ignore0 = false;\r\n\r\n  for (let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex) {\r\n    if (ignore0 && address[pieceIndex] === 0) {\r\n      continue;\r\n    } else if (ignore0) {\r\n      ignore0 = false;\r\n    }\r\n\r\n    if (compress === pieceIndex) {\r\n      const separator = pieceIndex === 0 ? \"::\" : \":\";\r\n      output += separator;\r\n      ignore0 = true;\r\n      continue;\r\n    }\r\n\r\n    output += address[pieceIndex].toString(16);\r\n\r\n    if (pieceIndex !== 7) {\r\n      output += \":\";\r\n    }\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction parseHost(input, isSpecialArg) {\r\n  if (input[0] === \"[\") {\r\n    if (input[input.length - 1] !== \"]\") {\r\n      return failure;\r\n    }\r\n\r\n    return parseIPv6(input.substring(1, input.length - 1));\r\n  }\r\n\r\n  if (!isSpecialArg) {\r\n    return parseOpaqueHost(input);\r\n  }\r\n\r\n  const domain = utf8PercentDecode(input);\r\n  const asciiDomain = tr46.toASCII(domain, false, tr46.PROCESSING_OPTIONS.NONTRANSITIONAL, false);\r\n  if (asciiDomain === null) {\r\n    return failure;\r\n  }\r\n\r\n  if (containsForbiddenHostCodePoint(asciiDomain)) {\r\n    return failure;\r\n  }\r\n\r\n  const ipv4Host = parseIPv4(asciiDomain);\r\n  if (typeof ipv4Host === \"number\" || ipv4Host === failure) {\r\n    return ipv4Host;\r\n  }\r\n\r\n  return asciiDomain;\r\n}\r\n\r\nfunction parseOpaqueHost(input) {\r\n  if (containsForbiddenHostCodePointExcludingPercent(input)) {\r\n    return failure;\r\n  }\r\n\r\n  let output = \"\";\r\n  const decoded = punycode.ucs2.decode(input);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    output += percentEncodeChar(decoded[i], isC0ControlPercentEncode);\r\n  }\r\n  return output;\r\n}\r\n\r\nfunction findLongestZeroSequence(arr) {\r\n  let maxIdx = null;\r\n  let maxLen = 1; // only find elements > 1\r\n  let currStart = null;\r\n  let currLen = 0;\r\n\r\n  for (let i = 0; i < arr.length; ++i) {\r\n    if (arr[i] !== 0) {\r\n      if (currLen > maxLen) {\r\n        maxIdx = currStart;\r\n        maxLen = currLen;\r\n      }\r\n\r\n      currStart = null;\r\n      currLen = 0;\r\n    } else {\r\n      if (currStart === null) {\r\n        currStart = i;\r\n      }\r\n      ++currLen;\r\n    }\r\n  }\r\n\r\n  // if trailing zeros\r\n  if (currLen > maxLen) {\r\n    maxIdx = currStart;\r\n    maxLen = currLen;\r\n  }\r\n\r\n  return {\r\n    idx: maxIdx,\r\n    len: maxLen\r\n  };\r\n}\r\n\r\nfunction serializeHost(host) {\r\n  if (typeof host === \"number\") {\r\n    return serializeIPv4(host);\r\n  }\r\n\r\n  // IPv6 serializer\r\n  if (host instanceof Array) {\r\n    return \"[\" + serializeIPv6(host) + \"]\";\r\n  }\r\n\r\n  return host;\r\n}\r\n\r\nfunction trimControlChars(url) {\r\n  return url.replace(/^[\\u0000-\\u001F\\u0020]+|[\\u0000-\\u001F\\u0020]+$/g, \"\");\r\n}\r\n\r\nfunction trimTabAndNewline(url) {\r\n  return url.replace(/\\u0009|\\u000A|\\u000D/g, \"\");\r\n}\r\n\r\nfunction shortenPath(url) {\r\n  const path = url.path;\r\n  if (path.length === 0) {\r\n    return;\r\n  }\r\n  if (url.scheme === \"file\" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {\r\n    return;\r\n  }\r\n\r\n  path.pop();\r\n}\r\n\r\nfunction includesCredentials(url) {\r\n  return url.username !== \"\" || url.password !== \"\";\r\n}\r\n\r\nfunction cannotHaveAUsernamePasswordPort(url) {\r\n  return url.host === null || url.host === \"\" || url.cannotBeABaseURL || url.scheme === \"file\";\r\n}\r\n\r\nfunction isNormalizedWindowsDriveLetter(string) {\r\n  return /^[A-Za-z]:$/.test(string);\r\n}\r\n\r\nfunction URLStateMachine(input, base, encodingOverride, url, stateOverride) {\r\n  this.pointer = 0;\r\n  this.input = input;\r\n  this.base = base || null;\r\n  this.encodingOverride = encodingOverride || \"utf-8\";\r\n  this.stateOverride = stateOverride;\r\n  this.url = url;\r\n  this.failure = false;\r\n  this.parseError = false;\r\n\r\n  if (!this.url) {\r\n    this.url = {\r\n      scheme: \"\",\r\n      username: \"\",\r\n      password: \"\",\r\n      host: null,\r\n      port: null,\r\n      path: [],\r\n      query: null,\r\n      fragment: null,\r\n\r\n      cannotBeABaseURL: false\r\n    };\r\n\r\n    const res = trimControlChars(this.input);\r\n    if (res !== this.input) {\r\n      this.parseError = true;\r\n    }\r\n    this.input = res;\r\n  }\r\n\r\n  const res = trimTabAndNewline(this.input);\r\n  if (res !== this.input) {\r\n    this.parseError = true;\r\n  }\r\n  this.input = res;\r\n\r\n  this.state = stateOverride || \"scheme start\";\r\n\r\n  this.buffer = \"\";\r\n  this.atFlag = false;\r\n  this.arrFlag = false;\r\n  this.passwordTokenSeenFlag = false;\r\n\r\n  this.input = punycode.ucs2.decode(this.input);\r\n\r\n  for (; this.pointer <= this.input.length; ++this.pointer) {\r\n    const c = this.input[this.pointer];\r\n    const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);\r\n\r\n    // exec state machine\r\n    const ret = this[\"parse \" + this.state](c, cStr);\r\n    if (!ret) {\r\n      break; // terminate algorithm\r\n    } else if (ret === failure) {\r\n      this.failure = true;\r\n      break;\r\n    }\r\n  }\r\n}\r\n\r\nURLStateMachine.prototype[\"parse scheme start\"] = function parseSchemeStart(c, cStr) {\r\n  if (isASCIIAlpha(c)) {\r\n    this.buffer += cStr.toLowerCase();\r\n    this.state = \"scheme\";\r\n  } else if (!this.stateOverride) {\r\n    this.state = \"no scheme\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse scheme\"] = function parseScheme(c, cStr) {\r\n  if (isASCIIAlphanumeric(c) || c === 43 || c === 45 || c === 46) {\r\n    this.buffer += cStr.toLowerCase();\r\n  } else if (c === 58) {\r\n    if (this.stateOverride) {\r\n      if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {\r\n        return false;\r\n      }\r\n\r\n      if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {\r\n        return false;\r\n      }\r\n\r\n      if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === \"file\") {\r\n        return false;\r\n      }\r\n\r\n      if (this.url.scheme === \"file\" && (this.url.host === \"\" || this.url.host === null)) {\r\n        return false;\r\n      }\r\n    }\r\n    this.url.scheme = this.buffer;\r\n    this.buffer = \"\";\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n    if (this.url.scheme === \"file\") {\r\n      if (this.input[this.pointer + 1] !== 47 || this.input[this.pointer + 2] !== 47) {\r\n        this.parseError = true;\r\n      }\r\n      this.state = \"file\";\r\n    } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {\r\n      this.state = \"special relative or authority\";\r\n    } else if (isSpecial(this.url)) {\r\n      this.state = \"special authority slashes\";\r\n    } else if (this.input[this.pointer + 1] === 47) {\r\n      this.state = \"path or authority\";\r\n      ++this.pointer;\r\n    } else {\r\n      this.url.cannotBeABaseURL = true;\r\n      this.url.path.push(\"\");\r\n      this.state = \"cannot-be-a-base-URL path\";\r\n    }\r\n  } else if (!this.stateOverride) {\r\n    this.buffer = \"\";\r\n    this.state = \"no scheme\";\r\n    this.pointer = -1;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse no scheme\"] = function parseNoScheme(c) {\r\n  if (this.base === null || (this.base.cannotBeABaseURL && c !== 35)) {\r\n    return failure;\r\n  } else if (this.base.cannotBeABaseURL && c === 35) {\r\n    this.url.scheme = this.base.scheme;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n    this.url.fragment = \"\";\r\n    this.url.cannotBeABaseURL = true;\r\n    this.state = \"fragment\";\r\n  } else if (this.base.scheme === \"file\") {\r\n    this.state = \"file\";\r\n    --this.pointer;\r\n  } else {\r\n    this.state = \"relative\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special relative or authority\"] = function parseSpecialRelativeOrAuthority(c) {\r\n  if (c === 47 && this.input[this.pointer + 1] === 47) {\r\n    this.state = \"special authority ignore slashes\";\r\n    ++this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    this.state = \"relative\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path or authority\"] = function parsePathOrAuthority(c) {\r\n  if (c === 47) {\r\n    this.state = \"authority\";\r\n  } else {\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse relative\"] = function parseRelative(c) {\r\n  this.url.scheme = this.base.scheme;\r\n  if (isNaN(c)) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n  } else if (c === 47) {\r\n    this.state = \"relative slash\";\r\n  } else if (c === 63) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (c === 35) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else if (isSpecial(this.url) && c === 92) {\r\n    this.parseError = true;\r\n    this.state = \"relative slash\";\r\n  } else {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice(0, this.base.path.length - 1);\r\n\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse relative slash\"] = function parseRelativeSlash(c) {\r\n  if (isSpecial(this.url) && (c === 47 || c === 92)) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"special authority ignore slashes\";\r\n  } else if (c === 47) {\r\n    this.state = \"authority\";\r\n  } else {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special authority slashes\"] = function parseSpecialAuthoritySlashes(c) {\r\n  if (c === 47 && this.input[this.pointer + 1] === 47) {\r\n    this.state = \"special authority ignore slashes\";\r\n    ++this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    this.state = \"special authority ignore slashes\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special authority ignore slashes\"] = function parseSpecialAuthorityIgnoreSlashes(c) {\r\n  if (c !== 47 && c !== 92) {\r\n    this.state = \"authority\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse authority\"] = function parseAuthority(c, cStr) {\r\n  if (c === 64) {\r\n    this.parseError = true;\r\n    if (this.atFlag) {\r\n      this.buffer = \"%40\" + this.buffer;\r\n    }\r\n    this.atFlag = true;\r\n\r\n    // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars\r\n    const len = countSymbols(this.buffer);\r\n    for (let pointer = 0; pointer < len; ++pointer) {\r\n      const codePoint = this.buffer.codePointAt(pointer);\r\n\r\n      if (codePoint === 58 && !this.passwordTokenSeenFlag) {\r\n        this.passwordTokenSeenFlag = true;\r\n        continue;\r\n      }\r\n      const encodedCodePoints = percentEncodeChar(codePoint, isUserinfoPercentEncode);\r\n      if (this.passwordTokenSeenFlag) {\r\n        this.url.password += encodedCodePoints;\r\n      } else {\r\n        this.url.username += encodedCodePoints;\r\n      }\r\n    }\r\n    this.buffer = \"\";\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92)) {\r\n    if (this.atFlag && this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    }\r\n    this.pointer -= countSymbols(this.buffer) + 1;\r\n    this.buffer = \"\";\r\n    this.state = \"host\";\r\n  } else {\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse hostname\"] =\r\nURLStateMachine.prototype[\"parse host\"] = function parseHostName(c, cStr) {\r\n  if (this.stateOverride && this.url.scheme === \"file\") {\r\n    --this.pointer;\r\n    this.state = \"file host\";\r\n  } else if (c === 58 && !this.arrFlag) {\r\n    if (this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    }\r\n\r\n    const host = parseHost(this.buffer, isSpecial(this.url));\r\n    if (host === failure) {\r\n      return failure;\r\n    }\r\n\r\n    this.url.host = host;\r\n    this.buffer = \"\";\r\n    this.state = \"port\";\r\n    if (this.stateOverride === \"hostname\") {\r\n      return false;\r\n    }\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92)) {\r\n    --this.pointer;\r\n    if (isSpecial(this.url) && this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    } else if (this.stateOverride && this.buffer === \"\" &&\r\n               (includesCredentials(this.url) || this.url.port !== null)) {\r\n      this.parseError = true;\r\n      return false;\r\n    }\r\n\r\n    const host = parseHost(this.buffer, isSpecial(this.url));\r\n    if (host === failure) {\r\n      return failure;\r\n    }\r\n\r\n    this.url.host = host;\r\n    this.buffer = \"\";\r\n    this.state = \"path start\";\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n  } else {\r\n    if (c === 91) {\r\n      this.arrFlag = true;\r\n    } else if (c === 93) {\r\n      this.arrFlag = false;\r\n    }\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse port\"] = function parsePort(c, cStr) {\r\n  if (isASCIIDigit(c)) {\r\n    this.buffer += cStr;\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92) ||\r\n             this.stateOverride) {\r\n    if (this.buffer !== \"\") {\r\n      const port = parseInt(this.buffer);\r\n      if (port > Math.pow(2, 16) - 1) {\r\n        this.parseError = true;\r\n        return failure;\r\n      }\r\n      this.url.port = port === defaultPort(this.url.scheme) ? null : port;\r\n      this.buffer = \"\";\r\n    }\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n    this.state = \"path start\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nconst fileOtherwiseCodePoints = new Set([47, 92, 63, 35]);\r\n\r\nURLStateMachine.prototype[\"parse file\"] = function parseFile(c) {\r\n  this.url.scheme = \"file\";\r\n\r\n  if (c === 47 || c === 92) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"file slash\";\r\n  } else if (this.base !== null && this.base.scheme === \"file\") {\r\n    if (isNaN(c)) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = this.base.query;\r\n    } else if (c === 63) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = \"\";\r\n      this.state = \"query\";\r\n    } else if (c === 35) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = this.base.query;\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    } else {\r\n      if (this.input.length - this.pointer - 1 === 0 || // remaining consists of 0 code points\r\n          !isWindowsDriveLetterCodePoints(c, this.input[this.pointer + 1]) ||\r\n          (this.input.length - this.pointer - 1 >= 2 && // remaining has at least 2 code points\r\n           !fileOtherwiseCodePoints.has(this.input[this.pointer + 2]))) {\r\n        this.url.host = this.base.host;\r\n        this.url.path = this.base.path.slice();\r\n        shortenPath(this.url);\r\n      } else {\r\n        this.parseError = true;\r\n      }\r\n\r\n      this.state = \"path\";\r\n      --this.pointer;\r\n    }\r\n  } else {\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse file slash\"] = function parseFileSlash(c) {\r\n  if (c === 47 || c === 92) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"file host\";\r\n  } else {\r\n    if (this.base !== null && this.base.scheme === \"file\") {\r\n      if (isNormalizedWindowsDriveLetterString(this.base.path[0])) {\r\n        this.url.path.push(this.base.path[0]);\r\n      } else {\r\n        this.url.host = this.base.host;\r\n      }\r\n    }\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse file host\"] = function parseFileHost(c, cStr) {\r\n  if (isNaN(c) || c === 47 || c === 92 || c === 63 || c === 35) {\r\n    --this.pointer;\r\n    if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {\r\n      this.parseError = true;\r\n      this.state = \"path\";\r\n    } else if (this.buffer === \"\") {\r\n      this.url.host = \"\";\r\n      if (this.stateOverride) {\r\n        return false;\r\n      }\r\n      this.state = \"path start\";\r\n    } else {\r\n      let host = parseHost(this.buffer, isSpecial(this.url));\r\n      if (host === failure) {\r\n        return failure;\r\n      }\r\n      if (host === \"localhost\") {\r\n        host = \"\";\r\n      }\r\n      this.url.host = host;\r\n\r\n      if (this.stateOverride) {\r\n        return false;\r\n      }\r\n\r\n      this.buffer = \"\";\r\n      this.state = \"path start\";\r\n    }\r\n  } else {\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path start\"] = function parsePathStart(c) {\r\n  if (isSpecial(this.url)) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"path\";\r\n\r\n    if (c !== 47 && c !== 92) {\r\n      --this.pointer;\r\n    }\r\n  } else if (!this.stateOverride && c === 63) {\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (!this.stateOverride && c === 35) {\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else if (c !== undefined) {\r\n    this.state = \"path\";\r\n    if (c !== 47) {\r\n      --this.pointer;\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path\"] = function parsePath(c) {\r\n  if (isNaN(c) || c === 47 || (isSpecial(this.url) && c === 92) ||\r\n      (!this.stateOverride && (c === 63 || c === 35))) {\r\n    if (isSpecial(this.url) && c === 92) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (isDoubleDot(this.buffer)) {\r\n      shortenPath(this.url);\r\n      if (c !== 47 && !(isSpecial(this.url) && c === 92)) {\r\n        this.url.path.push(\"\");\r\n      }\r\n    } else if (isSingleDot(this.buffer) && c !== 47 &&\r\n               !(isSpecial(this.url) && c === 92)) {\r\n      this.url.path.push(\"\");\r\n    } else if (!isSingleDot(this.buffer)) {\r\n      if (this.url.scheme === \"file\" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {\r\n        if (this.url.host !== \"\" && this.url.host !== null) {\r\n          this.parseError = true;\r\n          this.url.host = \"\";\r\n        }\r\n        this.buffer = this.buffer[0] + \":\";\r\n      }\r\n      this.url.path.push(this.buffer);\r\n    }\r\n    this.buffer = \"\";\r\n    if (this.url.scheme === \"file\" && (c === undefined || c === 63 || c === 35)) {\r\n      while (this.url.path.length > 1 && this.url.path[0] === \"\") {\r\n        this.parseError = true;\r\n        this.url.path.shift();\r\n      }\r\n    }\r\n    if (c === 63) {\r\n      this.url.query = \"\";\r\n      this.state = \"query\";\r\n    }\r\n    if (c === 35) {\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    }\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.buffer += percentEncodeChar(c, isPathPercentEncode);\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse cannot-be-a-base-URL path\"] = function parseCannotBeABaseURLPath(c) {\r\n  if (c === 63) {\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (c === 35) {\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else {\r\n    // TODO: Add: not a URL code point\r\n    if (!isNaN(c) && c !== 37) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (c === 37 &&\r\n        (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n         !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (!isNaN(c)) {\r\n      this.url.path[0] = this.url.path[0] + percentEncodeChar(c, isC0ControlPercentEncode);\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse query\"] = function parseQuery(c, cStr) {\r\n  if (isNaN(c) || (!this.stateOverride && c === 35)) {\r\n    if (!isSpecial(this.url) || this.url.scheme === \"ws\" || this.url.scheme === \"wss\") {\r\n      this.encodingOverride = \"utf-8\";\r\n    }\r\n\r\n    const buffer = new Buffer(this.buffer); // TODO: Use encoding override instead\r\n    for (let i = 0; i < buffer.length; ++i) {\r\n      if (buffer[i] < 0x21 || buffer[i] > 0x7E || buffer[i] === 0x22 || buffer[i] === 0x23 ||\r\n          buffer[i] === 0x3C || buffer[i] === 0x3E) {\r\n        this.url.query += percentEncode(buffer[i]);\r\n      } else {\r\n        this.url.query += String.fromCodePoint(buffer[i]);\r\n      }\r\n    }\r\n\r\n    this.buffer = \"\";\r\n    if (c === 35) {\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    }\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse fragment\"] = function parseFragment(c) {\r\n  if (isNaN(c)) { // do nothing\r\n  } else if (c === 0x0) {\r\n    this.parseError = true;\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.url.fragment += percentEncodeChar(c, isC0ControlPercentEncode);\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nfunction serializeURL(url, excludeFragment) {\r\n  let output = url.scheme + \":\";\r\n  if (url.host !== null) {\r\n    output += \"//\";\r\n\r\n    if (url.username !== \"\" || url.password !== \"\") {\r\n      output += url.username;\r\n      if (url.password !== \"\") {\r\n        output += \":\" + url.password;\r\n      }\r\n      output += \"@\";\r\n    }\r\n\r\n    output += serializeHost(url.host);\r\n\r\n    if (url.port !== null) {\r\n      output += \":\" + url.port;\r\n    }\r\n  } else if (url.host === null && url.scheme === \"file\") {\r\n    output += \"//\";\r\n  }\r\n\r\n  if (url.cannotBeABaseURL) {\r\n    output += url.path[0];\r\n  } else {\r\n    for (const string of url.path) {\r\n      output += \"/\" + string;\r\n    }\r\n  }\r\n\r\n  if (url.query !== null) {\r\n    output += \"?\" + url.query;\r\n  }\r\n\r\n  if (!excludeFragment && url.fragment !== null) {\r\n    output += \"#\" + url.fragment;\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction serializeOrigin(tuple) {\r\n  let result = tuple.scheme + \"://\";\r\n  result += serializeHost(tuple.host);\r\n\r\n  if (tuple.port !== null) {\r\n    result += \":\" + tuple.port;\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\nmodule.exports.serializeURL = serializeURL;\r\n\r\nmodule.exports.serializeURLOrigin = function (url) {\r\n  // https://url.spec.whatwg.org/#concept-url-origin\r\n  switch (url.scheme) {\r\n    case \"blob\":\r\n      try {\r\n        return module.exports.serializeURLOrigin(module.exports.parseURL(url.path[0]));\r\n      } catch (e) {\r\n        // serializing an opaque origin returns \"null\"\r\n        return \"null\";\r\n      }\r\n    case \"ftp\":\r\n    case \"gopher\":\r\n    case \"http\":\r\n    case \"https\":\r\n    case \"ws\":\r\n    case \"wss\":\r\n      return serializeOrigin({\r\n        scheme: url.scheme,\r\n        host: url.host,\r\n        port: url.port\r\n      });\r\n    case \"file\":\r\n      // spec says \"exercise to the reader\", chrome says \"file://\"\r\n      return \"file://\";\r\n    default:\r\n      // serializing an opaque origin returns \"null\"\r\n      return \"null\";\r\n  }\r\n};\r\n\r\nmodule.exports.basicURLParse = function (input, options) {\r\n  if (options === undefined) {\r\n    options = {};\r\n  }\r\n\r\n  const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);\r\n  if (usm.failure) {\r\n    return \"failure\";\r\n  }\r\n\r\n  return usm.url;\r\n};\r\n\r\nmodule.exports.setTheUsername = function (url, username) {\r\n  url.username = \"\";\r\n  const decoded = punycode.ucs2.decode(username);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    url.username += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\r\n  }\r\n};\r\n\r\nmodule.exports.setThePassword = function (url, password) {\r\n  url.password = \"\";\r\n  const decoded = punycode.ucs2.decode(password);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    url.password += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\r\n  }\r\n};\r\n\r\nmodule.exports.serializeHost = serializeHost;\r\n\r\nmodule.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;\r\n\r\nmodule.exports.serializeInteger = function (integer) {\r\n  return String(integer);\r\n};\r\n\r\nmodule.exports.parseURL = function (input, options) {\r\n  if (options === undefined) {\r\n    options = {};\r\n  }\r\n\r\n  // We don't handle blobs, so this just delegates:\r\n  return module.exports.basicURLParse(input, { baseURL: options.baseURL, encodingOverride: options.encodingOverride });\r\n};\r\n"], "names": [], "mappings": "AAAA;AACA,MAAM;AACN,MAAM;AAEN,MAAM,iBAAiB;IACrB,KAAK;IACL,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO;IACP,IAAI;IACJ,KAAK;AACP;AAEA,MAAM,UAAU,OAAO;AAEvB,SAAS,aAAa,GAAG;IACvB,OAAO,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM;AACzC;AAEA,SAAS,GAAG,KAAK,EAAE,GAAG;IACpB,MAAM,IAAI,KAAK,CAAC,IAAI;IACpB,OAAO,MAAM,KAAK,YAAY,OAAO,aAAa,CAAC;AACrD;AAEA,SAAS,aAAa,CAAC;IACrB,OAAO,KAAK,QAAQ,KAAK;AAC3B;AAEA,SAAS,aAAa,CAAC;IACrB,OAAO,AAAC,KAAK,QAAQ,KAAK,QAAU,KAAK,QAAQ,KAAK;AACxD;AAEA,SAAS,oBAAoB,CAAC;IAC5B,OAAO,aAAa,MAAM,aAAa;AACzC;AAEA,SAAS,WAAW,CAAC;IACnB,OAAO,aAAa,MAAO,KAAK,QAAQ,KAAK,QAAU,KAAK,QAAQ,KAAK;AAC3E;AAEA,SAAS,YAAY,MAAM;IACzB,OAAO,WAAW,OAAO,OAAO,WAAW,OAAO;AACpD;AAEA,SAAS,YAAY,MAAM;IACzB,SAAS,OAAO,WAAW;IAC3B,OAAO,WAAW,QAAQ,WAAW,UAAU,WAAW,UAAU,WAAW;AACjF;AAEA,SAAS,+BAA+B,GAAG,EAAE,GAAG;IAC9C,OAAO,aAAa,QAAQ,CAAC,QAAQ,MAAM,QAAQ,GAAG;AACxD;AAEA,SAAS,2BAA2B,MAAM;IACxC,OAAO,OAAO,MAAM,KAAK,KAAK,aAAa,OAAO,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK,GAAG;AAC9G;AAEA,SAAS,qCAAqC,MAAM;IAClD,OAAO,OAAO,MAAM,KAAK,KAAK,aAAa,OAAO,WAAW,CAAC,OAAO,MAAM,CAAC,EAAE,KAAK;AACrF;AAEA,SAAS,+BAA+B,MAAM;IAC5C,OAAO,OAAO,MAAM,CAAC,iEAAiE,CAAC;AACzF;AAEA,SAAS,+CAA+C,MAAM;IAC5D,OAAO,OAAO,MAAM,CAAC,+DAA+D,CAAC;AACvF;AAEA,SAAS,gBAAgB,MAAM;IAC7B,OAAO,cAAc,CAAC,OAAO,KAAK;AACpC;AAEA,SAAS,UAAU,GAAG;IACpB,OAAO,gBAAgB,IAAI,MAAM;AACnC;AAEA,SAAS,YAAY,MAAM;IACzB,OAAO,cAAc,CAAC,OAAO;AAC/B;AAEA,SAAS,cAAc,CAAC;IACtB,IAAI,MAAM,EAAE,QAAQ,CAAC,IAAI,WAAW;IACpC,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,MAAM,MAAM;IACd;IAEA,OAAO,MAAM;AACf;AAEA,SAAS,kBAAkB,CAAC;IAC1B,MAAM,MAAM,IAAI,OAAO;IAEvB,IAAI,MAAM;IAEV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,EAAE,EAAG;QACnC,OAAO,cAAc,GAAG,CAAC,EAAE;IAC7B;IAEA,OAAO;AACT;AAEA,SAAS,kBAAkB,GAAG;IAC5B,MAAM,QAAQ,IAAI,OAAO;IACzB,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACrC,IAAI,KAAK,CAAC,EAAE,KAAK,IAAI;YACnB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI,KAAK,CAAC,EAAE,KAAK,MAAM,WAAW,KAAK,CAAC,IAAI,EAAE,KAAK,WAAW,KAAK,CAAC,IAAI,EAAE,GAAG;YAClF,OAAO,IAAI,CAAC,SAAS,MAAM,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI;YAC3D,KAAK;QACP,OAAO;YACL,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;QACtB;IACF;IACA,OAAO,IAAI,OAAO,QAAQ,QAAQ;AACpC;AAEA,SAAS,yBAAyB,CAAC;IACjC,OAAO,KAAK,QAAQ,IAAI;AAC1B;AAEA,MAAM,4BAA4B,IAAI,IAAI;IAAC;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAK;CAAI;AAChF,SAAS,oBAAoB,CAAC;IAC5B,OAAO,yBAAyB,MAAM,0BAA0B,GAAG,CAAC;AACtE;AAEA,MAAM,gCACJ,IAAI,IAAI;IAAC;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;CAAI;AACnD,SAAS,wBAAwB,CAAC;IAChC,OAAO,oBAAoB,MAAM,8BAA8B,GAAG,CAAC;AACrE;AAEA,SAAS,kBAAkB,CAAC,EAAE,kBAAkB;IAC9C,MAAM,OAAO,OAAO,aAAa,CAAC;IAElC,IAAI,mBAAmB,IAAI;QACzB,OAAO,kBAAkB;IAC3B;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,KAAK;IAC5B,IAAI,IAAI;IAER,IAAI,MAAM,MAAM,IAAI,KAAK,MAAM,MAAM,CAAC,OAAO,OAAO,MAAM,MAAM,CAAC,GAAG,WAAW,OAAO,KAAK;QACzF,QAAQ,MAAM,SAAS,CAAC;QACxB,IAAI;IACN,OAAO,IAAI,MAAM,MAAM,IAAI,KAAK,MAAM,MAAM,CAAC,OAAO,KAAK;QACvD,QAAQ,MAAM,SAAS,CAAC;QACxB,IAAI;IACN;IAEA,IAAI,UAAU,IAAI;QAChB,OAAO;IACT;IAEA,MAAM,QAAQ,MAAM,KAAK,WAAY,MAAM,KAAK,iBAAiB;IACjE,IAAI,MAAM,IAAI,CAAC,QAAQ;QACrB,OAAO;IACT;IAEA,OAAO,SAAS,OAAO;AACzB;AAEA,SAAS,UAAU,KAAK;IACtB,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,KAAK,IAAI;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,MAAM,GAAG;QACX;IACF;IAEA,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,OAAO;IACT;IAEA,MAAM,UAAU,EAAE;IAClB,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,SAAS,IAAI;YACf,OAAO;QACT;QACA,MAAM,IAAI,gBAAgB;QAC1B,IAAI,MAAM,SAAS;YACjB,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC;IACf;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,GAAG,EAAE,EAAG;QAC3C,IAAI,OAAO,CAAC,EAAE,GAAG,KAAK;YACpB,OAAO;QACT;IACF;IACA,IAAI,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,IAAI,KAAK,GAAG,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;QACpE,OAAO;IACT;IAEA,IAAI,OAAO,QAAQ,GAAG;IACtB,IAAI,UAAU;IAEd,KAAK,MAAM,KAAK,QAAS;QACvB,QAAQ,IAAI,KAAK,GAAG,CAAC,KAAK,IAAI;QAC9B,EAAE;IACJ;IAEA,OAAO;AACT;AAEA,SAAS,cAAc,OAAO;IAC5B,IAAI,SAAS;IACb,IAAI,IAAI;IAER,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QAC3B,SAAS,OAAO,IAAI,OAAO;QAC3B,IAAI,MAAM,GAAG;YACX,SAAS,MAAM;QACjB;QACA,IAAI,KAAK,KAAK,CAAC,IAAI;IACrB;IAEA,OAAO;AACT;AAEA,SAAS,UAAU,KAAK;IACtB,MAAM,UAAU;QAAC;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;IACxC,IAAI,aAAa;IACjB,IAAI,WAAW;IACf,IAAI,UAAU;IAEd,QAAQ,SAAS,IAAI,CAAC,MAAM,CAAC;IAE7B,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI;QACzB,IAAI,KAAK,CAAC,UAAU,EAAE,KAAK,IAAI;YAC7B,OAAO;QACT;QAEA,WAAW;QACX,EAAE;QACF,WAAW;IACb;IAEA,MAAO,UAAU,MAAM,MAAM,CAAE;QAC7B,IAAI,eAAe,GAAG;YACpB,OAAO;QACT;QAEA,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI;YACzB,IAAI,aAAa,MAAM;gBACrB,OAAO;YACT;YACA,EAAE;YACF,EAAE;YACF,WAAW;YACX;QACF;QAEA,IAAI,QAAQ;QACZ,IAAI,SAAS;QAEb,MAAO,SAAS,KAAK,WAAW,KAAK,CAAC,QAAQ,EAAG;YAC/C,QAAQ,QAAQ,OAAO,SAAS,GAAG,OAAO,UAAU;YACpD,EAAE;YACF,EAAE;QACJ;QAEA,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI;YACzB,IAAI,WAAW,GAAG;gBAChB,OAAO;YACT;YAEA,WAAW;YAEX,IAAI,aAAa,GAAG;gBAClB,OAAO;YACT;YAEA,IAAI,cAAc;YAElB,MAAO,KAAK,CAAC,QAAQ,KAAK,UAAW;gBACnC,IAAI,YAAY;gBAEhB,IAAI,cAAc,GAAG;oBACnB,IAAI,KAAK,CAAC,QAAQ,KAAK,MAAM,cAAc,GAAG;wBAC5C,EAAE;oBACJ,OAAO;wBACL,OAAO;oBACT;gBACF;gBAEA,IAAI,CAAC,aAAa,KAAK,CAAC,QAAQ,GAAG;oBACjC,OAAO;gBACT;gBAEA,MAAO,aAAa,KAAK,CAAC,QAAQ,EAAG;oBACnC,MAAM,SAAS,SAAS,GAAG,OAAO;oBAClC,IAAI,cAAc,MAAM;wBACtB,YAAY;oBACd,OAAO,IAAI,cAAc,GAAG;wBAC1B,OAAO;oBACT,OAAO;wBACL,YAAY,YAAY,KAAK;oBAC/B;oBACA,IAAI,YAAY,KAAK;wBACnB,OAAO;oBACT;oBACA,EAAE;gBACJ;gBAEA,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,GAAG,QAAQ;gBAEpD,EAAE;gBAEF,IAAI,gBAAgB,KAAK,gBAAgB,GAAG;oBAC1C,EAAE;gBACJ;YACF;YAEA,IAAI,gBAAgB,GAAG;gBACrB,OAAO;YACT;YAEA;QACF,OAAO,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI;YAChC,EAAE;YACF,IAAI,KAAK,CAAC,QAAQ,KAAK,WAAW;gBAChC,OAAO;YACT;QACF,OAAO,IAAI,KAAK,CAAC,QAAQ,KAAK,WAAW;YACvC,OAAO;QACT;QAEA,OAAO,CAAC,WAAW,GAAG;QACtB,EAAE;IACJ;IAEA,IAAI,aAAa,MAAM;QACrB,IAAI,QAAQ,aAAa;QACzB,aAAa;QACb,MAAO,eAAe,KAAK,QAAQ,EAAG;YACpC,MAAM,OAAO,OAAO,CAAC,WAAW,QAAQ,EAAE;YAC1C,OAAO,CAAC,WAAW,QAAQ,EAAE,GAAG,OAAO,CAAC,WAAW;YACnD,OAAO,CAAC,WAAW,GAAG;YACtB,EAAE;YACF,EAAE;QACJ;IACF,OAAO,IAAI,aAAa,QAAQ,eAAe,GAAG;QAChD,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,cAAc,OAAO;IAC5B,IAAI,SAAS;IACb,MAAM,YAAY,wBAAwB;IAC1C,MAAM,WAAW,UAAU,GAAG;IAC9B,IAAI,UAAU;IAEd,IAAK,IAAI,aAAa,GAAG,cAAc,GAAG,EAAE,WAAY;QACtD,IAAI,WAAW,OAAO,CAAC,WAAW,KAAK,GAAG;YACxC;QACF,OAAO,IAAI,SAAS;YAClB,UAAU;QACZ;QAEA,IAAI,aAAa,YAAY;YAC3B,MAAM,YAAY,eAAe,IAAI,OAAO;YAC5C,UAAU;YACV,UAAU;YACV;QACF;QAEA,UAAU,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC;QAEvC,IAAI,eAAe,GAAG;YACpB,UAAU;QACZ;IACF;IAEA,OAAO;AACT;AAEA,SAAS,UAAU,KAAK,EAAE,YAAY;IACpC,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;QACpB,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,KAAK,KAAK;YACnC,OAAO;QACT;QAEA,OAAO,UAAU,MAAM,SAAS,CAAC,GAAG,MAAM,MAAM,GAAG;IACrD;IAEA,IAAI,CAAC,cAAc;QACjB,OAAO,gBAAgB;IACzB;IAEA,MAAM,SAAS,kBAAkB;IACjC,MAAM,cAAc,KAAK,OAAO,CAAC,QAAQ,OAAO,KAAK,kBAAkB,CAAC,eAAe,EAAE;IACzF,IAAI,gBAAgB,MAAM;QACxB,OAAO;IACT;IAEA,IAAI,+BAA+B,cAAc;QAC/C,OAAO;IACT;IAEA,MAAM,WAAW,UAAU;IAC3B,IAAI,OAAO,aAAa,YAAY,aAAa,SAAS;QACxD,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,KAAK;IAC5B,IAAI,+CAA+C,QAAQ;QACzD,OAAO;IACT;IAEA,IAAI,SAAS;IACb,MAAM,UAAU,SAAS,IAAI,CAAC,MAAM,CAAC;IACrC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,EAAE,EAAG;QACvC,UAAU,kBAAkB,OAAO,CAAC,EAAE,EAAE;IAC1C;IACA,OAAO;AACT;AAEA,SAAS,wBAAwB,GAAG;IAClC,IAAI,SAAS;IACb,IAAI,SAAS,GAAG,yBAAyB;IACzC,IAAI,YAAY;IAChB,IAAI,UAAU;IAEd,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,EAAE,EAAG;QACnC,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG;YAChB,IAAI,UAAU,QAAQ;gBACpB,SAAS;gBACT,SAAS;YACX;YAEA,YAAY;YACZ,UAAU;QACZ,OAAO;YACL,IAAI,cAAc,MAAM;gBACtB,YAAY;YACd;YACA,EAAE;QACJ;IACF;IAEA,oBAAoB;IACpB,IAAI,UAAU,QAAQ;QACpB,SAAS;QACT,SAAS;IACX;IAEA,OAAO;QACL,KAAK;QACL,KAAK;IACP;AACF;AAEA,SAAS,cAAc,IAAI;IACzB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,cAAc;IACvB;IAEA,kBAAkB;IAClB,IAAI,gBAAgB,OAAO;QACzB,OAAO,MAAM,cAAc,QAAQ;IACrC;IAEA,OAAO;AACT;AAEA,SAAS,iBAAiB,GAAG;IAC3B,OAAO,IAAI,OAAO,CAAC,oDAAoD;AACzE;AAEA,SAAS,kBAAkB,GAAG;IAC5B,OAAO,IAAI,OAAO,CAAC,yBAAyB;AAC9C;AAEA,SAAS,YAAY,GAAG;IACtB,MAAM,OAAO,IAAI,IAAI;IACrB,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB;IACF;IACA,IAAI,IAAI,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,KAAK,+BAA+B,IAAI,CAAC,EAAE,GAAG;QACzF;IACF;IAEA,KAAK,GAAG;AACV;AAEA,SAAS,oBAAoB,GAAG;IAC9B,OAAO,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK;AACjD;AAEA,SAAS,gCAAgC,GAAG;IAC1C,OAAO,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,MAAM,IAAI,gBAAgB,IAAI,IAAI,MAAM,KAAK;AACxF;AAEA,SAAS,+BAA+B,MAAM;IAC5C,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEA,SAAS,gBAAgB,KAAK,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,EAAE,aAAa;IACxE,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAAC,gBAAgB,GAAG,oBAAoB;IAC5C,IAAI,CAAC,aAAa,GAAG;IACrB,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,UAAU,GAAG;IAElB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,GAAG,GAAG;YACT,QAAQ;YACR,UAAU;YACV,UAAU;YACV,MAAM;YACN,MAAM;YACN,MAAM,EAAE;YACR,OAAO;YACP,UAAU;YAEV,kBAAkB;QACpB;QAEA,MAAM,MAAM,iBAAiB,IAAI,CAAC,KAAK;QACvC,IAAI,QAAQ,IAAI,CAAC,KAAK,EAAE;YACtB,IAAI,CAAC,UAAU,GAAG;QACpB;QACA,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,MAAM,MAAM,kBAAkB,IAAI,CAAC,KAAK;IACxC,IAAI,QAAQ,IAAI,CAAC,KAAK,EAAE;QACtB,IAAI,CAAC,UAAU,GAAG;IACpB;IACA,IAAI,CAAC,KAAK,GAAG;IAEb,IAAI,CAAC,KAAK,GAAG,iBAAiB;IAE9B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,qBAAqB,GAAG;IAE7B,IAAI,CAAC,KAAK,GAAG,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK;IAE5C,MAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,OAAO,CAAE;QACxD,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;QAClC,MAAM,OAAO,MAAM,KAAK,YAAY,OAAO,aAAa,CAAC;QAEzD,qBAAqB;QACrB,MAAM,MAAM,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG;QAC3C,IAAI,CAAC,KAAK;YACR,OAAO,sBAAsB;QAC/B,OAAO,IAAI,QAAQ,SAAS;YAC1B,IAAI,CAAC,OAAO,GAAG;YACf;QACF;IACF;AACF;AAEA,gBAAgB,SAAS,CAAC,qBAAqB,GAAG,SAAS,iBAAiB,CAAC,EAAE,IAAI;IACjF,IAAI,aAAa,IAAI;QACnB,IAAI,CAAC,MAAM,IAAI,KAAK,WAAW;QAC/B,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;QAC9B,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB,OAAO;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,eAAe,GAAG,SAAS,YAAY,CAAC,EAAE,IAAI;IACtE,IAAI,oBAAoB,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;QAC9D,IAAI,CAAC,MAAM,IAAI,KAAK,WAAW;IACjC,OAAO,IAAI,MAAM,IAAI;QACnB,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,UAAU,IAAI,CAAC,GAAG,KAAK,CAAC,gBAAgB,IAAI,CAAC,MAAM,GAAG;gBACxD,OAAO;YACT;YAEA,IAAI,CAAC,UAAU,IAAI,CAAC,GAAG,KAAK,gBAAgB,IAAI,CAAC,MAAM,GAAG;gBACxD,OAAO;YACT;YAEA,IAAI,CAAC,oBAAoB,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,QAAQ;gBACvF,OAAO;YACT;YAEA,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG;gBAClF,OAAO;YACT;QACF;QACA,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;QAC7B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO;QACT;QACA,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ;YAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,IAAI;gBAC9E,IAAI,CAAC,UAAU,GAAG;YACpB;YACA,IAAI,CAAC,KAAK,GAAG;QACf,OAAO,IAAI,UAAU,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YAC5F,IAAI,CAAC,KAAK,GAAG;QACf,OAAO,IAAI,UAAU,IAAI,CAAC,GAAG,GAAG;YAC9B,IAAI,CAAC,KAAK,GAAG;QACf,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,IAAI;YAC9C,IAAI,CAAC,KAAK,GAAG;YACb,EAAE,IAAI,CAAC,OAAO;QAChB,OAAO;YACL,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG;YAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;YACnB,IAAI,CAAC,KAAK,GAAG;QACf;IACF,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;QAC9B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG,CAAC;IAClB,OAAO;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,kBAAkB,GAAG,SAAS,cAAc,CAAC;IACrE,IAAI,IAAI,CAAC,IAAI,KAAK,QAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,IAAK;QAClE,OAAO;IACT,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,IAAI;QACjD,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;QAClC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;QACpC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;QAChC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG;QACpB,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG;QAC5B,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ;QACtC,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB,OAAO;QACL,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,sCAAsC,GAAG,SAAS,gCAAgC,CAAC;IAC3G,IAAI,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,IAAI;QACnD,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB,OAAO;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,0BAA0B,GAAG,SAAS,qBAAqB,CAAC;IACpF,IAAI,MAAM,IAAI;QACZ,IAAI,CAAC,KAAK,GAAG;IACf,OAAO;QACL,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,iBAAiB,GAAG,SAAS,cAAc,CAAC;IACpE,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;IAClC,IAAI,MAAM,IAAI;QACZ,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;QACpC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;IAClC,OAAO,IAAI,MAAM,IAAI;QACnB,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,MAAM,IAAI;QACnB,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;QACpC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,MAAM,IAAI;QACnB,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;QACpC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;QAChC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,UAAU,IAAI,CAAC,GAAG,KAAK,MAAM,IAAI;QAC1C,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,KAAK,GAAG;IACf,OAAO;QACL,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QAEhE,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,uBAAuB,GAAG,SAAS,mBAAmB,CAAC;IAC/E,IAAI,UAAU,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,MAAM,MAAM,EAAE,GAAG;QACjD,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,UAAU,GAAG;QACpB;QACA,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,MAAM,IAAI;QACnB,IAAI,CAAC,KAAK,GAAG;IACf,OAAO;QACL,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,kCAAkC,GAAG,SAAS,6BAA6B,CAAC;IACpG,IAAI,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,IAAI;QACnD,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB,OAAO;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,yCAAyC,GAAG,SAAS,mCAAmC,CAAC;IACjH,IAAI,MAAM,MAAM,MAAM,IAAI;QACxB,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB,OAAO;QACL,IAAI,CAAC,UAAU,GAAG;IACpB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,kBAAkB,GAAG,SAAS,eAAe,CAAC,EAAE,IAAI;IAC5E,IAAI,MAAM,IAAI;QACZ,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,CAAC,MAAM;QACnC;QACA,IAAI,CAAC,MAAM,GAAG;QAEd,qGAAqG;QACrG,MAAM,MAAM,aAAa,IAAI,CAAC,MAAM;QACpC,IAAK,IAAI,UAAU,GAAG,UAAU,KAAK,EAAE,QAAS;YAC9C,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAE1C,IAAI,cAAc,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBACnD,IAAI,CAAC,qBAAqB,GAAG;gBAC7B;YACF;YACA,MAAM,oBAAoB,kBAAkB,WAAW;YACvD,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC9B,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI;YACvB,OAAO;gBACL,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI;YACvB;QACF;QACA,IAAI,CAAC,MAAM,GAAG;IAChB,OAAO,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MACzC,UAAU,IAAI,CAAC,GAAG,KAAK,MAAM,IAAK;QAC5C,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI;YACrC,IAAI,CAAC,UAAU,GAAG;YAClB,OAAO;QACT;QACA,IAAI,CAAC,OAAO,IAAI,aAAa,IAAI,CAAC,MAAM,IAAI;QAC5C,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;IACf,OAAO;QACL,IAAI,CAAC,MAAM,IAAI;IACjB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,iBAAiB,GAC3C,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAS,cAAc,CAAC,EAAE,IAAI;IACtE,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ;QACpD,EAAE,IAAI,CAAC,OAAO;QACd,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;QACpC,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI;YACtB,IAAI,CAAC,UAAU,GAAG;YAClB,OAAO;QACT;QAEA,MAAM,OAAO,UAAU,IAAI,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC,GAAG;QACtD,IAAI,SAAS,SAAS;YACpB,OAAO;QACT;QAEA,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,IAAI,CAAC,aAAa,KAAK,YAAY;YACrC,OAAO;QACT;IACF,OAAO,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MACzC,UAAU,IAAI,CAAC,GAAG,KAAK,MAAM,IAAK;QAC5C,EAAE,IAAI,CAAC,OAAO;QACd,IAAI,UAAU,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,MAAM,KAAK,IAAI;YAC7C,IAAI,CAAC,UAAU,GAAG;YAClB,OAAO;QACT,OAAO,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,KAAK,MACtC,CAAC,oBAAoB,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG;YACpE,IAAI,CAAC,UAAU,GAAG;YAClB,OAAO;QACT;QAEA,MAAM,OAAO,UAAU,IAAI,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC,GAAG;QACtD,IAAI,SAAS,SAAS;YACpB,OAAO;QACT;QAEA,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO;QACT;IACF,OAAO;QACL,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,OAAO,GAAG;QACjB,OAAO,IAAI,MAAM,IAAI;YACnB,IAAI,CAAC,OAAO,GAAG;QACjB;QACA,IAAI,CAAC,MAAM,IAAI;IACjB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAS,UAAU,CAAC,EAAE,IAAI;IAClE,IAAI,aAAa,IAAI;QACnB,IAAI,CAAC,MAAM,IAAI;IACjB,OAAO,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MACzC,UAAU,IAAI,CAAC,GAAG,KAAK,MAAM,MAC9B,IAAI,CAAC,aAAa,EAAE;QAC7B,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI;YACtB,MAAM,OAAO,SAAS,IAAI,CAAC,MAAM;YACjC,IAAI,OAAO,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG;gBAC9B,IAAI,CAAC,UAAU,GAAG;gBAClB,OAAO;YACT;YACA,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,SAAS,YAAY,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,OAAO;YAC/D,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO;QACT;QACA,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB,OAAO;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,MAAM,0BAA0B,IAAI,IAAI;IAAC;IAAI;IAAI;IAAI;CAAG;AAExD,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAS,UAAU,CAAC;IAC5D,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG;IAElB,IAAI,MAAM,MAAM,MAAM,IAAI;QACxB,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,UAAU,GAAG;QACpB;QACA,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ;QAC5D,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;YACpC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;QAClC,OAAO,IAAI,MAAM,IAAI;YACnB,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;YACpC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG;YACjB,IAAI,CAAC,KAAK,GAAG;QACf,OAAO,IAAI,MAAM,IAAI;YACnB,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;YACpC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;YAChC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG;YACpB,IAAI,CAAC,KAAK,GAAG;QACf,OAAO;YACL,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,KAAK,sCAAsC;YACpF,CAAC,+BAA+B,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,KAC9D,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,uCAAuC;YACpF,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAI;gBAChE,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;gBAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;gBACpC,YAAY,IAAI,CAAC,GAAG;YACtB,OAAO;gBACL,IAAI,CAAC,UAAU,GAAG;YACpB;YAEA,IAAI,CAAC,KAAK,GAAG;YACb,EAAE,IAAI,CAAC,OAAO;QAChB;IACF,OAAO;QACL,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,mBAAmB,GAAG,SAAS,eAAe,CAAC;IACvE,IAAI,MAAM,MAAM,MAAM,IAAI;QACxB,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,UAAU,GAAG;QACpB;QACA,IAAI,CAAC,KAAK,GAAG;IACf,OAAO;QACL,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ;YACrD,IAAI,qCAAqC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;gBAC3D,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACtC,OAAO;gBACL,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;YAChC;QACF;QACA,IAAI,CAAC,KAAK,GAAG;QACb,EAAE,IAAI,CAAC,OAAO;IAChB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,kBAAkB,GAAG,SAAS,cAAc,CAAC,EAAE,IAAI;IAC3E,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;QAC5D,EAAE,IAAI,CAAC,OAAO;QACd,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,2BAA2B,IAAI,CAAC,MAAM,GAAG;YAClE,IAAI,CAAC,UAAU,GAAG;YAClB,IAAI,CAAC,KAAK,GAAG;QACf,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI;YAC7B,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG;YAChB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,OAAO;YACT;YACA,IAAI,CAAC,KAAK,GAAG;QACf,OAAO;YACL,IAAI,OAAO,UAAU,IAAI,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC,GAAG;YACpD,IAAI,SAAS,SAAS;gBACpB,OAAO;YACT;YACA,IAAI,SAAS,aAAa;gBACxB,OAAO;YACT;YACA,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG;YAEhB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,OAAO;YACT;YAEA,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,KAAK,GAAG;QACf;IACF,OAAO;QACL,IAAI,CAAC,MAAM,IAAI;IACjB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,mBAAmB,GAAG,SAAS,eAAe,CAAC;IACvE,IAAI,UAAU,IAAI,CAAC,GAAG,GAAG;QACvB,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,UAAU,GAAG;QACpB;QACA,IAAI,CAAC,KAAK,GAAG;QAEb,IAAI,MAAM,MAAM,MAAM,IAAI;YACxB,EAAE,IAAI,CAAC,OAAO;QAChB;IACF,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,MAAM,IAAI;QAC1C,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,MAAM,IAAI;QAC1C,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,MAAM,WAAW;QAC1B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,MAAM,IAAI;YACZ,EAAE,IAAI,CAAC,OAAO;QAChB;IACF;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAS,UAAU,CAAC;IAC5D,IAAI,MAAM,MAAM,MAAM,MAAO,UAAU,IAAI,CAAC,GAAG,KAAK,MAAM,MACrD,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,MAAM,MAAM,MAAM,EAAE,GAAI;QACnD,IAAI,UAAU,IAAI,CAAC,GAAG,KAAK,MAAM,IAAI;YACnC,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,IAAI,YAAY,IAAI,CAAC,MAAM,GAAG;YAC5B,YAAY,IAAI,CAAC,GAAG;YACpB,IAAI,MAAM,MAAM,CAAC,CAAC,UAAU,IAAI,CAAC,GAAG,KAAK,MAAM,EAAE,GAAG;gBAClD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;YACrB;QACF,OAAO,IAAI,YAAY,IAAI,CAAC,MAAM,KAAK,MAAM,MAClC,CAAC,CAAC,UAAU,IAAI,CAAC,GAAG,KAAK,MAAM,EAAE,GAAG;YAC7C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QACrB,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,MAAM,GAAG;YACpC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,2BAA2B,IAAI,CAAC,MAAM,GAAG;gBACvG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM;oBAClD,IAAI,CAAC,UAAU,GAAG;oBAClB,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG;gBAClB;gBACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;YACjC;YACA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;QAChC;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,aAAa,MAAM,MAAM,MAAM,EAAE,GAAG;YAC3E,MAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,GAAI;gBAC1D,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK;YACrB;QACF;QACA,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG;YACjB,IAAI,CAAC,KAAK,GAAG;QACf;QACA,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG;YACpB,IAAI,CAAC,KAAK,GAAG;QACf;IACF,OAAO;QACL,+DAA+D;QAE/D,IAAI,MAAM,MACR,CAAC,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,KACvC,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,GAAG;YAC9C,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,IAAI,CAAC,MAAM,IAAI,kBAAkB,GAAG;IACtC;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,kCAAkC,GAAG,SAAS,0BAA0B,CAAC;IACjG,IAAI,MAAM,IAAI;QACZ,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;IACf,OAAO,IAAI,MAAM,IAAI;QACnB,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG;IACf,OAAO;QACL,kCAAkC;QAClC,IAAI,CAAC,MAAM,MAAM,MAAM,IAAI;YACzB,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,IAAI,MAAM,MACN,CAAC,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,KACxC,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,GAAG;YAC/C,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,IAAI,CAAC,MAAM,IAAI;YACb,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,kBAAkB,GAAG;QAC7D;IACF;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,cAAc,GAAG,SAAS,WAAW,CAAC,EAAE,IAAI;IACpE,IAAI,MAAM,MAAO,CAAC,IAAI,CAAC,aAAa,IAAI,MAAM,IAAK;QACjD,IAAI,CAAC,UAAU,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,OAAO;YACjF,IAAI,CAAC,gBAAgB,GAAG;QAC1B;QAEA,MAAM,SAAS,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,sCAAsC;QAC9E,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;YACtC,IAAI,MAAM,CAAC,EAAE,GAAG,QAAQ,MAAM,CAAC,EAAE,GAAG,QAAQ,MAAM,CAAC,EAAE,KAAK,QAAQ,MAAM,CAAC,EAAE,KAAK,QAC5E,MAAM,CAAC,EAAE,KAAK,QAAQ,MAAM,CAAC,EAAE,KAAK,MAAM;gBAC5C,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,cAAc,MAAM,CAAC,EAAE;YAC3C,OAAO;gBACL,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,OAAO,aAAa,CAAC,MAAM,CAAC,EAAE;YAClD;QACF;QAEA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG;YACpB,IAAI,CAAC,KAAK,GAAG;QACf;IACF,OAAO;QACL,+DAA+D;QAC/D,IAAI,MAAM,MACR,CAAC,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,KACvC,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,GAAG;YAC9C,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,IAAI,CAAC,MAAM,IAAI;IACjB;IAEA,OAAO;AACT;AAEA,gBAAgB,SAAS,CAAC,iBAAiB,GAAG,SAAS,cAAc,CAAC;IACpE,IAAI,MAAM,IAAI,CACd,OAAO,IAAI,MAAM,KAAK;QACpB,IAAI,CAAC,UAAU,GAAG;IACpB,OAAO;QACL,+DAA+D;QAC/D,IAAI,MAAM,MACR,CAAC,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,KACvC,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,GAAG;YAC9C,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,kBAAkB,GAAG;IAC5C;IAEA,OAAO;AACT;AAEA,SAAS,aAAa,GAAG,EAAE,eAAe;IACxC,IAAI,SAAS,IAAI,MAAM,GAAG;IAC1B,IAAI,IAAI,IAAI,KAAK,MAAM;QACrB,UAAU;QAEV,IAAI,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,IAAI;YAC9C,UAAU,IAAI,QAAQ;YACtB,IAAI,IAAI,QAAQ,KAAK,IAAI;gBACvB,UAAU,MAAM,IAAI,QAAQ;YAC9B;YACA,UAAU;QACZ;QAEA,UAAU,cAAc,IAAI,IAAI;QAEhC,IAAI,IAAI,IAAI,KAAK,MAAM;YACrB,UAAU,MAAM,IAAI,IAAI;QAC1B;IACF,OAAO,IAAI,IAAI,IAAI,KAAK,QAAQ,IAAI,MAAM,KAAK,QAAQ;QACrD,UAAU;IACZ;IAEA,IAAI,IAAI,gBAAgB,EAAE;QACxB,UAAU,IAAI,IAAI,CAAC,EAAE;IACvB,OAAO;QACL,KAAK,MAAM,UAAU,IAAI,IAAI,CAAE;YAC7B,UAAU,MAAM;QAClB;IACF;IAEA,IAAI,IAAI,KAAK,KAAK,MAAM;QACtB,UAAU,MAAM,IAAI,KAAK;IAC3B;IAEA,IAAI,CAAC,mBAAmB,IAAI,QAAQ,KAAK,MAAM;QAC7C,UAAU,MAAM,IAAI,QAAQ;IAC9B;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,KAAK;IAC5B,IAAI,SAAS,MAAM,MAAM,GAAG;IAC5B,UAAU,cAAc,MAAM,IAAI;IAElC,IAAI,MAAM,IAAI,KAAK,MAAM;QACvB,UAAU,MAAM,MAAM,IAAI;IAC5B;IAEA,OAAO;AACT;AAEA,OAAO,OAAO,CAAC,YAAY,GAAG;AAE9B,OAAO,OAAO,CAAC,kBAAkB,GAAG,SAAU,GAAG;IAC/C,kDAAkD;IAClD,OAAQ,IAAI,MAAM;QAChB,KAAK;YACH,IAAI;gBACF,OAAO,OAAO,OAAO,CAAC,kBAAkB,CAAC,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,EAAE;YAC9E,EAAE,OAAO,GAAG;gBACV,8CAA8C;gBAC9C,OAAO;YACT;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,gBAAgB;gBACrB,QAAQ,IAAI,MAAM;gBAClB,MAAM,IAAI,IAAI;gBACd,MAAM,IAAI,IAAI;YAChB;QACF,KAAK;YACH,4DAA4D;YAC5D,OAAO;QACT;YACE,8CAA8C;YAC9C,OAAO;IACX;AACF;AAEA,OAAO,OAAO,CAAC,aAAa,GAAG,SAAU,KAAK,EAAE,OAAO;IACrD,IAAI,YAAY,WAAW;QACzB,UAAU,CAAC;IACb;IAEA,MAAM,MAAM,IAAI,gBAAgB,OAAO,QAAQ,OAAO,EAAE,QAAQ,gBAAgB,EAAE,QAAQ,GAAG,EAAE,QAAQ,aAAa;IACpH,IAAI,IAAI,OAAO,EAAE;QACf,OAAO;IACT;IAEA,OAAO,IAAI,GAAG;AAChB;AAEA,OAAO,OAAO,CAAC,cAAc,GAAG,SAAU,GAAG,EAAE,QAAQ;IACrD,IAAI,QAAQ,GAAG;IACf,MAAM,UAAU,SAAS,IAAI,CAAC,MAAM,CAAC;IACrC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,EAAE,EAAG;QACvC,IAAI,QAAQ,IAAI,kBAAkB,OAAO,CAAC,EAAE,EAAE;IAChD;AACF;AAEA,OAAO,OAAO,CAAC,cAAc,GAAG,SAAU,GAAG,EAAE,QAAQ;IACrD,IAAI,QAAQ,GAAG;IACf,MAAM,UAAU,SAAS,IAAI,CAAC,MAAM,CAAC;IACrC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,EAAE,EAAG;QACvC,IAAI,QAAQ,IAAI,kBAAkB,OAAO,CAAC,EAAE,EAAE;IAChD;AACF;AAEA,OAAO,OAAO,CAAC,aAAa,GAAG;AAE/B,OAAO,OAAO,CAAC,+BAA+B,GAAG;AAEjD,OAAO,OAAO,CAAC,gBAAgB,GAAG,SAAU,OAAO;IACjD,OAAO,OAAO;AAChB;AAEA,OAAO,OAAO,CAAC,QAAQ,GAAG,SAAU,KAAK,EAAE,OAAO;IAChD,IAAI,YAAY,WAAW;QACzB,UAAU,CAAC;IACb;IAEA,iDAAiD;IACjD,OAAO,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO;QAAE,SAAS,QAAQ,OAAO;QAAE,kBAAkB,QAAQ,gBAAgB;IAAC;AACpH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/whatwg-url/lib/URL-impl.js"], "sourcesContent": ["\"use strict\";\nconst usm = require(\"./url-state-machine\");\n\nexports.implementation = class URLImpl {\n  constructor(constructorArgs) {\n    const url = constructorArgs[0];\n    const base = constructorArgs[1];\n\n    let parsedBase = null;\n    if (base !== undefined) {\n      parsedBase = usm.basicURLParse(base);\n      if (parsedBase === \"failure\") {\n        throw new TypeError(\"Invalid base URL\");\n      }\n    }\n\n    const parsedURL = usm.basicURLParse(url, { baseURL: parsedBase });\n    if (parsedURL === \"failure\") {\n      throw new TypeError(\"Invalid URL\");\n    }\n\n    this._url = parsedURL;\n\n    // TODO: query stuff\n  }\n\n  get href() {\n    return usm.serializeURL(this._url);\n  }\n\n  set href(v) {\n    const parsedURL = usm.basicURLParse(v);\n    if (parsedURL === \"failure\") {\n      throw new TypeError(\"Invalid URL\");\n    }\n\n    this._url = parsedURL;\n  }\n\n  get origin() {\n    return usm.serializeURLOrigin(this._url);\n  }\n\n  get protocol() {\n    return this._url.scheme + \":\";\n  }\n\n  set protocol(v) {\n    usm.basicURLParse(v + \":\", { url: this._url, stateOverride: \"scheme start\" });\n  }\n\n  get username() {\n    return this._url.username;\n  }\n\n  set username(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    usm.setTheUsername(this._url, v);\n  }\n\n  get password() {\n    return this._url.password;\n  }\n\n  set password(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    usm.setThePassword(this._url, v);\n  }\n\n  get host() {\n    const url = this._url;\n\n    if (url.host === null) {\n      return \"\";\n    }\n\n    if (url.port === null) {\n      return usm.serializeHost(url.host);\n    }\n\n    return usm.serializeHost(url.host) + \":\" + usm.serializeInteger(url.port);\n  }\n\n  set host(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"host\" });\n  }\n\n  get hostname() {\n    if (this._url.host === null) {\n      return \"\";\n    }\n\n    return usm.serializeHost(this._url.host);\n  }\n\n  set hostname(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"hostname\" });\n  }\n\n  get port() {\n    if (this._url.port === null) {\n      return \"\";\n    }\n\n    return usm.serializeInteger(this._url.port);\n  }\n\n  set port(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    if (v === \"\") {\n      this._url.port = null;\n    } else {\n      usm.basicURLParse(v, { url: this._url, stateOverride: \"port\" });\n    }\n  }\n\n  get pathname() {\n    if (this._url.cannotBeABaseURL) {\n      return this._url.path[0];\n    }\n\n    if (this._url.path.length === 0) {\n      return \"\";\n    }\n\n    return \"/\" + this._url.path.join(\"/\");\n  }\n\n  set pathname(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    this._url.path = [];\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"path start\" });\n  }\n\n  get search() {\n    if (this._url.query === null || this._url.query === \"\") {\n      return \"\";\n    }\n\n    return \"?\" + this._url.query;\n  }\n\n  set search(v) {\n    // TODO: query stuff\n\n    const url = this._url;\n\n    if (v === \"\") {\n      url.query = null;\n      return;\n    }\n\n    const input = v[0] === \"?\" ? v.substring(1) : v;\n    url.query = \"\";\n    usm.basicURLParse(input, { url, stateOverride: \"query\" });\n  }\n\n  get hash() {\n    if (this._url.fragment === null || this._url.fragment === \"\") {\n      return \"\";\n    }\n\n    return \"#\" + this._url.fragment;\n  }\n\n  set hash(v) {\n    if (v === \"\") {\n      this._url.fragment = null;\n      return;\n    }\n\n    const input = v[0] === \"#\" ? v.substring(1) : v;\n    this._url.fragment = \"\";\n    usm.basicURLParse(input, { url: this._url, stateOverride: \"fragment\" });\n  }\n\n  toJSON() {\n    return this.href;\n  }\n};\n"], "names": [], "mappings": "AAAA;AACA,MAAM;AAEN,QAAQ,cAAc,GAAG,MAAM;IAC7B,YAAY,eAAe,CAAE;QAC3B,MAAM,MAAM,eAAe,CAAC,EAAE;QAC9B,MAAM,OAAO,eAAe,CAAC,EAAE;QAE/B,IAAI,aAAa;QACjB,IAAI,SAAS,WAAW;YACtB,aAAa,IAAI,aAAa,CAAC;YAC/B,IAAI,eAAe,WAAW;gBAC5B,MAAM,IAAI,UAAU;YACtB;QACF;QAEA,MAAM,YAAY,IAAI,aAAa,CAAC,KAAK;YAAE,SAAS;QAAW;QAC/D,IAAI,cAAc,WAAW;YAC3B,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI,CAAC,IAAI,GAAG;IAEZ,oBAAoB;IACtB;IAEA,IAAI,OAAO;QACT,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI;IACnC;IAEA,IAAI,KAAK,CAAC,EAAE;QACV,MAAM,YAAY,IAAI,aAAa,CAAC;QACpC,IAAI,cAAc,WAAW;YAC3B,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,IAAI,SAAS;QACX,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI;IACzC;IAEA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;IAC5B;IAEA,IAAI,SAAS,CAAC,EAAE;QACd,IAAI,aAAa,CAAC,IAAI,KAAK;YAAE,KAAK,IAAI,CAAC,IAAI;YAAE,eAAe;QAAe;IAC7E;IAEA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ;IAC3B;IAEA,IAAI,SAAS,CAAC,EAAE;QACd,IAAI,IAAI,+BAA+B,CAAC,IAAI,CAAC,IAAI,GAAG;YAClD;QACF;QAEA,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE;IAChC;IAEA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ;IAC3B;IAEA,IAAI,SAAS,CAAC,EAAE;QACd,IAAI,IAAI,+BAA+B,CAAC,IAAI,CAAC,IAAI,GAAG;YAClD;QACF;QAEA,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE;IAChC;IAEA,IAAI,OAAO;QACT,MAAM,MAAM,IAAI,CAAC,IAAI;QAErB,IAAI,IAAI,IAAI,KAAK,MAAM;YACrB,OAAO;QACT;QAEA,IAAI,IAAI,IAAI,KAAK,MAAM;YACrB,OAAO,IAAI,aAAa,CAAC,IAAI,IAAI;QACnC;QAEA,OAAO,IAAI,aAAa,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI,gBAAgB,CAAC,IAAI,IAAI;IAC1E;IAEA,IAAI,KAAK,CAAC,EAAE;QACV,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC9B;QACF;QAEA,IAAI,aAAa,CAAC,GAAG;YAAE,KAAK,IAAI,CAAC,IAAI;YAAE,eAAe;QAAO;IAC/D;IAEA,IAAI,WAAW;QACb,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM;YAC3B,OAAO;QACT;QAEA,OAAO,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;IACzC;IAEA,IAAI,SAAS,CAAC,EAAE;QACd,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC9B;QACF;QAEA,IAAI,aAAa,CAAC,GAAG;YAAE,KAAK,IAAI,CAAC,IAAI;YAAE,eAAe;QAAW;IACnE;IAEA,IAAI,OAAO;QACT,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM;YAC3B,OAAO;QACT;QAEA,OAAO,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;IAC5C;IAEA,IAAI,KAAK,CAAC,EAAE;QACV,IAAI,IAAI,+BAA+B,CAAC,IAAI,CAAC,IAAI,GAAG;YAClD;QACF;QAEA,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;QACnB,OAAO;YACL,IAAI,aAAa,CAAC,GAAG;gBAAE,KAAK,IAAI,CAAC,IAAI;gBAAE,eAAe;YAAO;QAC/D;IACF;IAEA,IAAI,WAAW;QACb,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC1B;QAEA,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG;YAC/B,OAAO;QACT;QAEA,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IACnC;IAEA,IAAI,SAAS,CAAC,EAAE;QACd,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC9B;QACF;QAEA,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE;QACnB,IAAI,aAAa,CAAC,GAAG;YAAE,KAAK,IAAI,CAAC,IAAI;YAAE,eAAe;QAAa;IACrE;IAEA,IAAI,SAAS;QACX,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI;YACtD,OAAO;QACT;QAEA,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK;IAC9B;IAEA,IAAI,OAAO,CAAC,EAAE;QACZ,oBAAoB;QAEpB,MAAM,MAAM,IAAI,CAAC,IAAI;QAErB,IAAI,MAAM,IAAI;YACZ,IAAI,KAAK,GAAG;YACZ;QACF;QAEA,MAAM,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE,SAAS,CAAC,KAAK;QAC9C,IAAI,KAAK,GAAG;QACZ,IAAI,aAAa,CAAC,OAAO;YAAE;YAAK,eAAe;QAAQ;IACzD;IAEA,IAAI,OAAO;QACT,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;YAC5D,OAAO;QACT;QAEA,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ;IACjC;IAEA,IAAI,KAAK,CAAC,EAAE;QACV,IAAI,MAAM,IAAI;YACZ,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG;YACrB;QACF;QAEA,MAAM,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE,SAAS,CAAC,KAAK;QAC9C,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG;QACrB,IAAI,aAAa,CAAC,OAAO;YAAE,KAAK,IAAI,CAAC,IAAI;YAAE,eAAe;QAAW;IACvE;IAEA,SAAS;QACP,OAAO,IAAI,CAAC,IAAI;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/whatwg-url/lib/URL.js"], "sourcesContent": ["\"use strict\";\n\nconst conversions = require(\"webidl-conversions\");\nconst utils = require(\"./utils.js\");\nconst Impl = require(\".//URL-impl.js\");\n\nconst impl = utils.implSymbol;\n\nfunction URL(url) {\n  if (!this || this[impl] || !(this instanceof URL)) {\n    throw new TypeError(\"Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.\");\n  }\n  if (arguments.length < 1) {\n    throw new TypeError(\"Failed to construct 'URL': 1 argument required, but only \" + arguments.length + \" present.\");\n  }\n  const args = [];\n  for (let i = 0; i < arguments.length && i < 2; ++i) {\n    args[i] = arguments[i];\n  }\n  args[0] = conversions[\"USVString\"](args[0]);\n  if (args[1] !== undefined) {\n  args[1] = conversions[\"USVString\"](args[1]);\n  }\n\n  module.exports.setup(this, args);\n}\n\nURL.prototype.toJSON = function toJSON() {\n  if (!this || !module.exports.is(this)) {\n    throw new TypeError(\"Illegal invocation\");\n  }\n  const args = [];\n  for (let i = 0; i < arguments.length && i < 0; ++i) {\n    args[i] = arguments[i];\n  }\n  return this[impl].toJSON.apply(this[impl], args);\n};\nObject.defineProperty(URL.prototype, \"href\", {\n  get() {\n    return this[impl].href;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].href = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nURL.prototype.toString = function () {\n  if (!this || !module.exports.is(this)) {\n    throw new TypeError(\"Illegal invocation\");\n  }\n  return this.href;\n};\n\nObject.defineProperty(URL.prototype, \"origin\", {\n  get() {\n    return this[impl].origin;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"protocol\", {\n  get() {\n    return this[impl].protocol;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].protocol = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"username\", {\n  get() {\n    return this[impl].username;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].username = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"password\", {\n  get() {\n    return this[impl].password;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].password = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"host\", {\n  get() {\n    return this[impl].host;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].host = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"hostname\", {\n  get() {\n    return this[impl].hostname;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].hostname = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"port\", {\n  get() {\n    return this[impl].port;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].port = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"pathname\", {\n  get() {\n    return this[impl].pathname;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].pathname = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"search\", {\n  get() {\n    return this[impl].search;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].search = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"hash\", {\n  get() {\n    return this[impl].hash;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].hash = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\n\nmodule.exports = {\n  is(obj) {\n    return !!obj && obj[impl] instanceof Impl.implementation;\n  },\n  create(constructorArgs, privateData) {\n    let obj = Object.create(URL.prototype);\n    this.setup(obj, constructorArgs, privateData);\n    return obj;\n  },\n  setup(obj, constructorArgs, privateData) {\n    if (!privateData) privateData = {};\n    privateData.wrapper = obj;\n\n    obj[impl] = new Impl.implementation(constructorArgs, privateData);\n    obj[impl][utils.wrapperSymbol] = obj;\n  },\n  interface: URL,\n  expose: {\n    Window: { URL: URL },\n    Worker: { URL: URL }\n  }\n};\n\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM;AAEN,MAAM,OAAO,MAAM,UAAU;AAE7B,SAAS,IAAI,GAAG;IACd,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,YAAY,GAAG,GAAG;QACjD,MAAM,IAAI,UAAU;IACtB;IACA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,MAAM,IAAI,UAAU,8DAA8D,UAAU,MAAM,GAAG;IACvG;IACA,MAAM,OAAO,EAAE;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,IAAI,IAAI,GAAG,EAAE,EAAG;QAClD,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;IACxB;IACA,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;IAC1C,IAAI,IAAI,CAAC,EAAE,KAAK,WAAW;QAC3B,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;IAC1C;IAEA,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE;AAC7B;AAEA,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS;IAC9B,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;QACrC,MAAM,IAAI,UAAU;IACtB;IACA,MAAM,OAAO,EAAE;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,IAAI,IAAI,GAAG,EAAE,EAAG;QAClD,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;IACxB;IACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;AAC7C;AACA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,QAAQ;IAC3C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;IACxB;IACA,KAAI,CAAC;QACH,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;IACpB;IACA,YAAY;IACZ,cAAc;AAChB;AAEA,IAAI,SAAS,CAAC,QAAQ,GAAG;IACvB,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;QACrC,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,UAAU;IAC7C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC1B;IACA,YAAY;IACZ,cAAc;AAChB;AAEA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,YAAY;IAC/C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IACA,KAAI,CAAC;QACH,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IACxB;IACA,YAAY;IACZ,cAAc;AAChB;AAEA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,YAAY;IAC/C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IACA,KAAI,CAAC;QACH,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IACxB;IACA,YAAY;IACZ,cAAc;AAChB;AAEA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,YAAY;IAC/C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IACA,KAAI,CAAC;QACH,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IACxB;IACA,YAAY;IACZ,cAAc;AAChB;AAEA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,QAAQ;IAC3C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;IACxB;IACA,KAAI,CAAC;QACH,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;IACpB;IACA,YAAY;IACZ,cAAc;AAChB;AAEA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,YAAY;IAC/C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IACA,KAAI,CAAC;QACH,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IACxB;IACA,YAAY;IACZ,cAAc;AAChB;AAEA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,QAAQ;IAC3C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;IACxB;IACA,KAAI,CAAC;QACH,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;IACpB;IACA,YAAY;IACZ,cAAc;AAChB;AAEA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,YAAY;IAC/C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IACA,KAAI,CAAC;QACH,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IACxB;IACA,YAAY;IACZ,cAAc;AAChB;AAEA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,UAAU;IAC7C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC1B;IACA,KAAI,CAAC;QACH,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IACtB;IACA,YAAY;IACZ,cAAc;AAChB;AAEA,OAAO,cAAc,CAAC,IAAI,SAAS,EAAE,QAAQ;IAC3C;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;IACxB;IACA,KAAI,CAAC;QACH,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;IACpB;IACA,YAAY;IACZ,cAAc;AAChB;AAGA,OAAO,OAAO,GAAG;IACf,IAAG,GAAG;QACJ,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC,KAAK,YAAY,KAAK,cAAc;IAC1D;IACA,QAAO,eAAe,EAAE,WAAW;QACjC,IAAI,MAAM,OAAO,MAAM,CAAC,IAAI,SAAS;QACrC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB;QACjC,OAAO;IACT;IACA,OAAM,GAAG,EAAE,eAAe,EAAE,WAAW;QACrC,IAAI,CAAC,aAAa,cAAc,CAAC;QACjC,YAAY,OAAO,GAAG;QAEtB,GAAG,CAAC,KAAK,GAAG,IAAI,KAAK,cAAc,CAAC,iBAAiB;QACrD,GAAG,CAAC,KAAK,CAAC,MAAM,aAAa,CAAC,GAAG;IACnC;IACA,WAAW;IACX,QAAQ;QACN,QAAQ;YAAE,KAAK;QAAI;QACnB,QAAQ;YAAE,KAAK;QAAI;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4914, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/whatwg-url/lib/public-api.js"], "sourcesContent": ["\"use strict\";\n\nexports.URL = require(\"./URL\").interface;\nexports.serializeURL = require(\"./url-state-machine\").serializeURL;\nexports.serializeURLOrigin = require(\"./url-state-machine\").serializeURLOrigin;\nexports.basicURLParse = require(\"./url-state-machine\").basicURLParse;\nexports.setTheUsername = require(\"./url-state-machine\").setTheUsername;\nexports.setThePassword = require(\"./url-state-machine\").setThePassword;\nexports.serializeHost = require(\"./url-state-machine\").serializeHost;\nexports.serializeInteger = require(\"./url-state-machine\").serializeInteger;\nexports.parseURL = require(\"./url-state-machine\").parseURL;\n"], "names": [], "mappings": "AAAA;AAEA,QAAQ,GAAG,GAAG,+FAAiB,SAAS;AACxC,QAAQ,YAAY,GAAG,6GAA+B,YAAY;AAClE,QAAQ,kBAAkB,GAAG,6GAA+B,kBAAkB;AAC9E,QAAQ,aAAa,GAAG,6GAA+B,aAAa;AACpE,QAAQ,cAAc,GAAG,6GAA+B,cAAc;AACtE,QAAQ,cAAc,GAAG,6GAA+B,cAAc;AACtE,QAAQ,aAAa,GAAG,6GAA+B,aAAa;AACpE,QAAQ,gBAAgB,GAAG,6GAA+B,gBAAgB;AAC1E,QAAQ,QAAQ,GAAG,6GAA+B,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/node-fetch/lib/index.mjs"], "sourcesContent": ["import Stream from 'stream';\nimport http from 'http';\nimport Url from 'url';\nimport whatwgUrl from 'whatwg-url';\nimport https from 'https';\nimport zlib from 'zlib';\n\n// Based on https://github.com/tmpvar/jsdom/blob/aa85b2abf07766ff7bf5c1f6daafb3726f2f2db5/lib/jsdom/living/blob.js\n\n// fix for \"Readable\" isn't a named export issue\nconst Readable = Stream.Readable;\n\nconst BUFFER = Symbol('buffer');\nconst TYPE = Symbol('type');\n\nclass Blob {\n\tconstructor() {\n\t\tthis[TYPE] = '';\n\n\t\tconst blobParts = arguments[0];\n\t\tconst options = arguments[1];\n\n\t\tconst buffers = [];\n\t\tlet size = 0;\n\n\t\tif (blobParts) {\n\t\t\tconst a = blobParts;\n\t\t\tconst length = Number(a.length);\n\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\tconst element = a[i];\n\t\t\t\tlet buffer;\n\t\t\t\tif (element instanceof Buffer) {\n\t\t\t\t\tbuffer = element;\n\t\t\t\t} else if (ArrayBuffer.isView(element)) {\n\t\t\t\t\tbuffer = Buffer.from(element.buffer, element.byteOffset, element.byteLength);\n\t\t\t\t} else if (element instanceof ArrayBuffer) {\n\t\t\t\t\tbuffer = Buffer.from(element);\n\t\t\t\t} else if (element instanceof Blob) {\n\t\t\t\t\tbuffer = element[BUFFER];\n\t\t\t\t} else {\n\t\t\t\t\tbuffer = Buffer.from(typeof element === 'string' ? element : String(element));\n\t\t\t\t}\n\t\t\t\tsize += buffer.length;\n\t\t\t\tbuffers.push(buffer);\n\t\t\t}\n\t\t}\n\n\t\tthis[BUFFER] = Buffer.concat(buffers);\n\n\t\tlet type = options && options.type !== undefined && String(options.type).toLowerCase();\n\t\tif (type && !/[^\\u0020-\\u007E]/.test(type)) {\n\t\t\tthis[TYPE] = type;\n\t\t}\n\t}\n\tget size() {\n\t\treturn this[BUFFER].length;\n\t}\n\tget type() {\n\t\treturn this[TYPE];\n\t}\n\ttext() {\n\t\treturn Promise.resolve(this[BUFFER].toString());\n\t}\n\tarrayBuffer() {\n\t\tconst buf = this[BUFFER];\n\t\tconst ab = buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);\n\t\treturn Promise.resolve(ab);\n\t}\n\tstream() {\n\t\tconst readable = new Readable();\n\t\treadable._read = function () {};\n\t\treadable.push(this[BUFFER]);\n\t\treadable.push(null);\n\t\treturn readable;\n\t}\n\ttoString() {\n\t\treturn '[object Blob]';\n\t}\n\tslice() {\n\t\tconst size = this.size;\n\n\t\tconst start = arguments[0];\n\t\tconst end = arguments[1];\n\t\tlet relativeStart, relativeEnd;\n\t\tif (start === undefined) {\n\t\t\trelativeStart = 0;\n\t\t} else if (start < 0) {\n\t\t\trelativeStart = Math.max(size + start, 0);\n\t\t} else {\n\t\t\trelativeStart = Math.min(start, size);\n\t\t}\n\t\tif (end === undefined) {\n\t\t\trelativeEnd = size;\n\t\t} else if (end < 0) {\n\t\t\trelativeEnd = Math.max(size + end, 0);\n\t\t} else {\n\t\t\trelativeEnd = Math.min(end, size);\n\t\t}\n\t\tconst span = Math.max(relativeEnd - relativeStart, 0);\n\n\t\tconst buffer = this[BUFFER];\n\t\tconst slicedBuffer = buffer.slice(relativeStart, relativeStart + span);\n\t\tconst blob = new Blob([], { type: arguments[2] });\n\t\tblob[BUFFER] = slicedBuffer;\n\t\treturn blob;\n\t}\n}\n\nObject.defineProperties(Blob.prototype, {\n\tsize: { enumerable: true },\n\ttype: { enumerable: true },\n\tslice: { enumerable: true }\n});\n\nObject.defineProperty(Blob.prototype, Symbol.toStringTag, {\n\tvalue: 'Blob',\n\twritable: false,\n\tenumerable: false,\n\tconfigurable: true\n});\n\n/**\n * fetch-error.js\n *\n * FetchError interface for operational errors\n */\n\n/**\n * Create FetchError instance\n *\n * @param   String      message      Error message for human\n * @param   String      type         Error type for machine\n * @param   String      systemError  For Node.js system error\n * @return  FetchError\n */\nfunction FetchError(message, type, systemError) {\n  Error.call(this, message);\n\n  this.message = message;\n  this.type = type;\n\n  // when err.type is `system`, err.code contains system error code\n  if (systemError) {\n    this.code = this.errno = systemError.code;\n  }\n\n  // hide custom error implementation details from end-users\n  Error.captureStackTrace(this, this.constructor);\n}\n\nFetchError.prototype = Object.create(Error.prototype);\nFetchError.prototype.constructor = FetchError;\nFetchError.prototype.name = 'FetchError';\n\nlet convert;\ntry {\n\tconvert = require('encoding').convert;\n} catch (e) {}\n\nconst INTERNALS = Symbol('Body internals');\n\n// fix an issue where \"PassThrough\" isn't a named export for node <10\nconst PassThrough = Stream.PassThrough;\n\n/**\n * Body mixin\n *\n * Ref: https://fetch.spec.whatwg.org/#body\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */\nfunction Body(body) {\n\tvar _this = this;\n\n\tvar _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n\t    _ref$size = _ref.size;\n\n\tlet size = _ref$size === undefined ? 0 : _ref$size;\n\tvar _ref$timeout = _ref.timeout;\n\tlet timeout = _ref$timeout === undefined ? 0 : _ref$timeout;\n\n\tif (body == null) {\n\t\t// body is undefined or null\n\t\tbody = null;\n\t} else if (isURLSearchParams(body)) {\n\t\t// body is a URLSearchParams\n\t\tbody = Buffer.from(body.toString());\n\t} else if (isBlob(body)) ; else if (Buffer.isBuffer(body)) ; else if (Object.prototype.toString.call(body) === '[object ArrayBuffer]') {\n\t\t// body is ArrayBuffer\n\t\tbody = Buffer.from(body);\n\t} else if (ArrayBuffer.isView(body)) {\n\t\t// body is ArrayBufferView\n\t\tbody = Buffer.from(body.buffer, body.byteOffset, body.byteLength);\n\t} else if (body instanceof Stream) ; else {\n\t\t// none of the above\n\t\t// coerce to string then buffer\n\t\tbody = Buffer.from(String(body));\n\t}\n\tthis[INTERNALS] = {\n\t\tbody,\n\t\tdisturbed: false,\n\t\terror: null\n\t};\n\tthis.size = size;\n\tthis.timeout = timeout;\n\n\tif (body instanceof Stream) {\n\t\tbody.on('error', function (err) {\n\t\t\tconst error = err.name === 'AbortError' ? err : new FetchError(`Invalid response body while trying to fetch ${_this.url}: ${err.message}`, 'system', err);\n\t\t\t_this[INTERNALS].error = error;\n\t\t});\n\t}\n}\n\nBody.prototype = {\n\tget body() {\n\t\treturn this[INTERNALS].body;\n\t},\n\n\tget bodyUsed() {\n\t\treturn this[INTERNALS].disturbed;\n\t},\n\n\t/**\n  * Decode response as ArrayBuffer\n  *\n  * @return  Promise\n  */\n\tarrayBuffer() {\n\t\treturn consumeBody.call(this).then(function (buf) {\n\t\t\treturn buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);\n\t\t});\n\t},\n\n\t/**\n  * Return raw response as Blob\n  *\n  * @return Promise\n  */\n\tblob() {\n\t\tlet ct = this.headers && this.headers.get('content-type') || '';\n\t\treturn consumeBody.call(this).then(function (buf) {\n\t\t\treturn Object.assign(\n\t\t\t// Prevent copying\n\t\t\tnew Blob([], {\n\t\t\t\ttype: ct.toLowerCase()\n\t\t\t}), {\n\t\t\t\t[BUFFER]: buf\n\t\t\t});\n\t\t});\n\t},\n\n\t/**\n  * Decode response as json\n  *\n  * @return  Promise\n  */\n\tjson() {\n\t\tvar _this2 = this;\n\n\t\treturn consumeBody.call(this).then(function (buffer) {\n\t\t\ttry {\n\t\t\t\treturn JSON.parse(buffer.toString());\n\t\t\t} catch (err) {\n\t\t\t\treturn Body.Promise.reject(new FetchError(`invalid json response body at ${_this2.url} reason: ${err.message}`, 'invalid-json'));\n\t\t\t}\n\t\t});\n\t},\n\n\t/**\n  * Decode response as text\n  *\n  * @return  Promise\n  */\n\ttext() {\n\t\treturn consumeBody.call(this).then(function (buffer) {\n\t\t\treturn buffer.toString();\n\t\t});\n\t},\n\n\t/**\n  * Decode response as buffer (non-spec api)\n  *\n  * @return  Promise\n  */\n\tbuffer() {\n\t\treturn consumeBody.call(this);\n\t},\n\n\t/**\n  * Decode response as text, while automatically detecting the encoding and\n  * trying to decode to UTF-8 (non-spec api)\n  *\n  * @return  Promise\n  */\n\ttextConverted() {\n\t\tvar _this3 = this;\n\n\t\treturn consumeBody.call(this).then(function (buffer) {\n\t\t\treturn convertBody(buffer, _this3.headers);\n\t\t});\n\t}\n};\n\n// In browsers, all properties are enumerable.\nObject.defineProperties(Body.prototype, {\n\tbody: { enumerable: true },\n\tbodyUsed: { enumerable: true },\n\tarrayBuffer: { enumerable: true },\n\tblob: { enumerable: true },\n\tjson: { enumerable: true },\n\ttext: { enumerable: true }\n});\n\nBody.mixIn = function (proto) {\n\tfor (const name of Object.getOwnPropertyNames(Body.prototype)) {\n\t\t// istanbul ignore else: future proof\n\t\tif (!(name in proto)) {\n\t\t\tconst desc = Object.getOwnPropertyDescriptor(Body.prototype, name);\n\t\t\tObject.defineProperty(proto, name, desc);\n\t\t}\n\t}\n};\n\n/**\n * Consume and convert an entire Body to a Buffer.\n *\n * Ref: https://fetch.spec.whatwg.org/#concept-body-consume-body\n *\n * @return  Promise\n */\nfunction consumeBody() {\n\tvar _this4 = this;\n\n\tif (this[INTERNALS].disturbed) {\n\t\treturn Body.Promise.reject(new TypeError(`body used already for: ${this.url}`));\n\t}\n\n\tthis[INTERNALS].disturbed = true;\n\n\tif (this[INTERNALS].error) {\n\t\treturn Body.Promise.reject(this[INTERNALS].error);\n\t}\n\n\tlet body = this.body;\n\n\t// body is null\n\tif (body === null) {\n\t\treturn Body.Promise.resolve(Buffer.alloc(0));\n\t}\n\n\t// body is blob\n\tif (isBlob(body)) {\n\t\tbody = body.stream();\n\t}\n\n\t// body is buffer\n\tif (Buffer.isBuffer(body)) {\n\t\treturn Body.Promise.resolve(body);\n\t}\n\n\t// istanbul ignore if: should never happen\n\tif (!(body instanceof Stream)) {\n\t\treturn Body.Promise.resolve(Buffer.alloc(0));\n\t}\n\n\t// body is stream\n\t// get ready to actually consume the body\n\tlet accum = [];\n\tlet accumBytes = 0;\n\tlet abort = false;\n\n\treturn new Body.Promise(function (resolve, reject) {\n\t\tlet resTimeout;\n\n\t\t// allow timeout on slow response body\n\t\tif (_this4.timeout) {\n\t\t\tresTimeout = setTimeout(function () {\n\t\t\t\tabort = true;\n\t\t\t\treject(new FetchError(`Response timeout while trying to fetch ${_this4.url} (over ${_this4.timeout}ms)`, 'body-timeout'));\n\t\t\t}, _this4.timeout);\n\t\t}\n\n\t\t// handle stream errors\n\t\tbody.on('error', function (err) {\n\t\t\tif (err.name === 'AbortError') {\n\t\t\t\t// if the request was aborted, reject with this Error\n\t\t\t\tabort = true;\n\t\t\t\treject(err);\n\t\t\t} else {\n\t\t\t\t// other errors, such as incorrect content-encoding\n\t\t\t\treject(new FetchError(`Invalid response body while trying to fetch ${_this4.url}: ${err.message}`, 'system', err));\n\t\t\t}\n\t\t});\n\n\t\tbody.on('data', function (chunk) {\n\t\t\tif (abort || chunk === null) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (_this4.size && accumBytes + chunk.length > _this4.size) {\n\t\t\t\tabort = true;\n\t\t\t\treject(new FetchError(`content size at ${_this4.url} over limit: ${_this4.size}`, 'max-size'));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\taccumBytes += chunk.length;\n\t\t\taccum.push(chunk);\n\t\t});\n\n\t\tbody.on('end', function () {\n\t\t\tif (abort) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tclearTimeout(resTimeout);\n\n\t\t\ttry {\n\t\t\t\tresolve(Buffer.concat(accum, accumBytes));\n\t\t\t} catch (err) {\n\t\t\t\t// handle streams that have accumulated too much data (issue #414)\n\t\t\t\treject(new FetchError(`Could not create Buffer from response body for ${_this4.url}: ${err.message}`, 'system', err));\n\t\t\t}\n\t\t});\n\t});\n}\n\n/**\n * Detect buffer encoding and convert to target encoding\n * ref: http://www.w3.org/TR/2011/WD-html5-20110113/parsing.html#determining-the-character-encoding\n *\n * @param   Buffer  buffer    Incoming buffer\n * @param   String  encoding  Target encoding\n * @return  String\n */\nfunction convertBody(buffer, headers) {\n\tif (typeof convert !== 'function') {\n\t\tthrow new Error('The package `encoding` must be installed to use the textConverted() function');\n\t}\n\n\tconst ct = headers.get('content-type');\n\tlet charset = 'utf-8';\n\tlet res, str;\n\n\t// header\n\tif (ct) {\n\t\tres = /charset=([^;]*)/i.exec(ct);\n\t}\n\n\t// no charset in content type, peek at response body for at most 1024 bytes\n\tstr = buffer.slice(0, 1024).toString();\n\n\t// html5\n\tif (!res && str) {\n\t\tres = /<meta.+?charset=(['\"])(.+?)\\1/i.exec(str);\n\t}\n\n\t// html4\n\tif (!res && str) {\n\t\tres = /<meta[\\s]+?http-equiv=(['\"])content-type\\1[\\s]+?content=(['\"])(.+?)\\2/i.exec(str);\n\t\tif (!res) {\n\t\t\tres = /<meta[\\s]+?content=(['\"])(.+?)\\1[\\s]+?http-equiv=(['\"])content-type\\3/i.exec(str);\n\t\t\tif (res) {\n\t\t\t\tres.pop(); // drop last quote\n\t\t\t}\n\t\t}\n\n\t\tif (res) {\n\t\t\tres = /charset=(.*)/i.exec(res.pop());\n\t\t}\n\t}\n\n\t// xml\n\tif (!res && str) {\n\t\tres = /<\\?xml.+?encoding=(['\"])(.+?)\\1/i.exec(str);\n\t}\n\n\t// found charset\n\tif (res) {\n\t\tcharset = res.pop();\n\n\t\t// prevent decode issues when sites use incorrect encoding\n\t\t// ref: https://hsivonen.fi/encoding-menu/\n\t\tif (charset === 'gb2312' || charset === 'gbk') {\n\t\t\tcharset = 'gb18030';\n\t\t}\n\t}\n\n\t// turn raw buffers into a single utf-8 buffer\n\treturn convert(buffer, 'UTF-8', charset).toString();\n}\n\n/**\n * Detect a URLSearchParams object\n * ref: https://github.com/bitinn/node-fetch/issues/296#issuecomment-307598143\n *\n * @param   Object  obj     Object to detect by type or brand\n * @return  String\n */\nfunction isURLSearchParams(obj) {\n\t// Duck-typing as a necessary condition.\n\tif (typeof obj !== 'object' || typeof obj.append !== 'function' || typeof obj.delete !== 'function' || typeof obj.get !== 'function' || typeof obj.getAll !== 'function' || typeof obj.has !== 'function' || typeof obj.set !== 'function') {\n\t\treturn false;\n\t}\n\n\t// Brand-checking and more duck-typing as optional condition.\n\treturn obj.constructor.name === 'URLSearchParams' || Object.prototype.toString.call(obj) === '[object URLSearchParams]' || typeof obj.sort === 'function';\n}\n\n/**\n * Check if `obj` is a W3C `Blob` object (which `File` inherits from)\n * @param  {*} obj\n * @return {boolean}\n */\nfunction isBlob(obj) {\n\treturn typeof obj === 'object' && typeof obj.arrayBuffer === 'function' && typeof obj.type === 'string' && typeof obj.stream === 'function' && typeof obj.constructor === 'function' && typeof obj.constructor.name === 'string' && /^(Blob|File)$/.test(obj.constructor.name) && /^(Blob|File)$/.test(obj[Symbol.toStringTag]);\n}\n\n/**\n * Clone body given Res/Req instance\n *\n * @param   Mixed  instance  Response or Request instance\n * @return  Mixed\n */\nfunction clone(instance) {\n\tlet p1, p2;\n\tlet body = instance.body;\n\n\t// don't allow cloning a used body\n\tif (instance.bodyUsed) {\n\t\tthrow new Error('cannot clone body after it is used');\n\t}\n\n\t// check that body is a stream and not form-data object\n\t// note: we can't clone the form-data object without having it as a dependency\n\tif (body instanceof Stream && typeof body.getBoundary !== 'function') {\n\t\t// tee instance body\n\t\tp1 = new PassThrough();\n\t\tp2 = new PassThrough();\n\t\tbody.pipe(p1);\n\t\tbody.pipe(p2);\n\t\t// set instance body to teed body and return the other teed body\n\t\tinstance[INTERNALS].body = p1;\n\t\tbody = p2;\n\t}\n\n\treturn body;\n}\n\n/**\n * Performs the operation \"extract a `Content-Type` value from |object|\" as\n * specified in the specification:\n * https://fetch.spec.whatwg.org/#concept-bodyinit-extract\n *\n * This function assumes that instance.body is present.\n *\n * @param   Mixed  instance  Any options.body input\n */\nfunction extractContentType(body) {\n\tif (body === null) {\n\t\t// body is null\n\t\treturn null;\n\t} else if (typeof body === 'string') {\n\t\t// body is string\n\t\treturn 'text/plain;charset=UTF-8';\n\t} else if (isURLSearchParams(body)) {\n\t\t// body is a URLSearchParams\n\t\treturn 'application/x-www-form-urlencoded;charset=UTF-8';\n\t} else if (isBlob(body)) {\n\t\t// body is blob\n\t\treturn body.type || null;\n\t} else if (Buffer.isBuffer(body)) {\n\t\t// body is buffer\n\t\treturn null;\n\t} else if (Object.prototype.toString.call(body) === '[object ArrayBuffer]') {\n\t\t// body is ArrayBuffer\n\t\treturn null;\n\t} else if (ArrayBuffer.isView(body)) {\n\t\t// body is ArrayBufferView\n\t\treturn null;\n\t} else if (typeof body.getBoundary === 'function') {\n\t\t// detect form data input from form-data module\n\t\treturn `multipart/form-data;boundary=${body.getBoundary()}`;\n\t} else if (body instanceof Stream) {\n\t\t// body is stream\n\t\t// can't really do much about this\n\t\treturn null;\n\t} else {\n\t\t// Body constructor defaults other things to string\n\t\treturn 'text/plain;charset=UTF-8';\n\t}\n}\n\n/**\n * The Fetch Standard treats this as if \"total bytes\" is a property on the body.\n * For us, we have to explicitly get it with a function.\n *\n * ref: https://fetch.spec.whatwg.org/#concept-body-total-bytes\n *\n * @param   Body    instance   Instance of Body\n * @return  Number?            Number of bytes, or null if not possible\n */\nfunction getTotalBytes(instance) {\n\tconst body = instance.body;\n\n\n\tif (body === null) {\n\t\t// body is null\n\t\treturn 0;\n\t} else if (isBlob(body)) {\n\t\treturn body.size;\n\t} else if (Buffer.isBuffer(body)) {\n\t\t// body is buffer\n\t\treturn body.length;\n\t} else if (body && typeof body.getLengthSync === 'function') {\n\t\t// detect form data input from form-data module\n\t\tif (body._lengthRetrievers && body._lengthRetrievers.length == 0 || // 1.x\n\t\tbody.hasKnownLength && body.hasKnownLength()) {\n\t\t\t// 2.x\n\t\t\treturn body.getLengthSync();\n\t\t}\n\t\treturn null;\n\t} else {\n\t\t// body is stream\n\t\treturn null;\n\t}\n}\n\n/**\n * Write a Body to a Node.js WritableStream (e.g. http.Request) object.\n *\n * @param   Body    instance   Instance of Body\n * @return  Void\n */\nfunction writeToStream(dest, instance) {\n\tconst body = instance.body;\n\n\n\tif (body === null) {\n\t\t// body is null\n\t\tdest.end();\n\t} else if (isBlob(body)) {\n\t\tbody.stream().pipe(dest);\n\t} else if (Buffer.isBuffer(body)) {\n\t\t// body is buffer\n\t\tdest.write(body);\n\t\tdest.end();\n\t} else {\n\t\t// body is stream\n\t\tbody.pipe(dest);\n\t}\n}\n\n// expose Promise\nBody.Promise = global.Promise;\n\n/**\n * headers.js\n *\n * Headers class offers convenient helpers\n */\n\nconst invalidTokenRegex = /[^\\^_`a-zA-Z\\-0-9!#$%&'*+.|~]/;\nconst invalidHeaderCharRegex = /[^\\t\\x20-\\x7e\\x80-\\xff]/;\n\nfunction validateName(name) {\n\tname = `${name}`;\n\tif (invalidTokenRegex.test(name) || name === '') {\n\t\tthrow new TypeError(`${name} is not a legal HTTP header name`);\n\t}\n}\n\nfunction validateValue(value) {\n\tvalue = `${value}`;\n\tif (invalidHeaderCharRegex.test(value)) {\n\t\tthrow new TypeError(`${value} is not a legal HTTP header value`);\n\t}\n}\n\n/**\n * Find the key in the map object given a header name.\n *\n * Returns undefined if not found.\n *\n * @param   String  name  Header name\n * @return  String|Undefined\n */\nfunction find(map, name) {\n\tname = name.toLowerCase();\n\tfor (const key in map) {\n\t\tif (key.toLowerCase() === name) {\n\t\t\treturn key;\n\t\t}\n\t}\n\treturn undefined;\n}\n\nconst MAP = Symbol('map');\nclass Headers {\n\t/**\n  * Headers class\n  *\n  * @param   Object  headers  Response headers\n  * @return  Void\n  */\n\tconstructor() {\n\t\tlet init = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : undefined;\n\n\t\tthis[MAP] = Object.create(null);\n\n\t\tif (init instanceof Headers) {\n\t\t\tconst rawHeaders = init.raw();\n\t\t\tconst headerNames = Object.keys(rawHeaders);\n\n\t\t\tfor (const headerName of headerNames) {\n\t\t\t\tfor (const value of rawHeaders[headerName]) {\n\t\t\t\t\tthis.append(headerName, value);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t// We don't worry about converting prop to ByteString here as append()\n\t\t// will handle it.\n\t\tif (init == null) ; else if (typeof init === 'object') {\n\t\t\tconst method = init[Symbol.iterator];\n\t\t\tif (method != null) {\n\t\t\t\tif (typeof method !== 'function') {\n\t\t\t\t\tthrow new TypeError('Header pairs must be iterable');\n\t\t\t\t}\n\n\t\t\t\t// sequence<sequence<ByteString>>\n\t\t\t\t// Note: per spec we have to first exhaust the lists then process them\n\t\t\t\tconst pairs = [];\n\t\t\t\tfor (const pair of init) {\n\t\t\t\t\tif (typeof pair !== 'object' || typeof pair[Symbol.iterator] !== 'function') {\n\t\t\t\t\t\tthrow new TypeError('Each header pair must be iterable');\n\t\t\t\t\t}\n\t\t\t\t\tpairs.push(Array.from(pair));\n\t\t\t\t}\n\n\t\t\t\tfor (const pair of pairs) {\n\t\t\t\t\tif (pair.length !== 2) {\n\t\t\t\t\t\tthrow new TypeError('Each header pair must be a name/value tuple');\n\t\t\t\t\t}\n\t\t\t\t\tthis.append(pair[0], pair[1]);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// record<ByteString, ByteString>\n\t\t\t\tfor (const key of Object.keys(init)) {\n\t\t\t\t\tconst value = init[key];\n\t\t\t\t\tthis.append(key, value);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tthrow new TypeError('Provided initializer must be an object');\n\t\t}\n\t}\n\n\t/**\n  * Return combined header value given name\n  *\n  * @param   String  name  Header name\n  * @return  Mixed\n  */\n\tget(name) {\n\t\tname = `${name}`;\n\t\tvalidateName(name);\n\t\tconst key = find(this[MAP], name);\n\t\tif (key === undefined) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn this[MAP][key].join(', ');\n\t}\n\n\t/**\n  * Iterate over all headers\n  *\n  * @param   Function  callback  Executed for each item with parameters (value, name, thisArg)\n  * @param   Boolean   thisArg   `this` context for callback function\n  * @return  Void\n  */\n\tforEach(callback) {\n\t\tlet thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : undefined;\n\n\t\tlet pairs = getHeaders(this);\n\t\tlet i = 0;\n\t\twhile (i < pairs.length) {\n\t\t\tvar _pairs$i = pairs[i];\n\t\t\tconst name = _pairs$i[0],\n\t\t\t      value = _pairs$i[1];\n\n\t\t\tcallback.call(thisArg, value, name, this);\n\t\t\tpairs = getHeaders(this);\n\t\t\ti++;\n\t\t}\n\t}\n\n\t/**\n  * Overwrite header values given name\n  *\n  * @param   String  name   Header name\n  * @param   String  value  Header value\n  * @return  Void\n  */\n\tset(name, value) {\n\t\tname = `${name}`;\n\t\tvalue = `${value}`;\n\t\tvalidateName(name);\n\t\tvalidateValue(value);\n\t\tconst key = find(this[MAP], name);\n\t\tthis[MAP][key !== undefined ? key : name] = [value];\n\t}\n\n\t/**\n  * Append a value onto existing header\n  *\n  * @param   String  name   Header name\n  * @param   String  value  Header value\n  * @return  Void\n  */\n\tappend(name, value) {\n\t\tname = `${name}`;\n\t\tvalue = `${value}`;\n\t\tvalidateName(name);\n\t\tvalidateValue(value);\n\t\tconst key = find(this[MAP], name);\n\t\tif (key !== undefined) {\n\t\t\tthis[MAP][key].push(value);\n\t\t} else {\n\t\t\tthis[MAP][name] = [value];\n\t\t}\n\t}\n\n\t/**\n  * Check for header name existence\n  *\n  * @param   String   name  Header name\n  * @return  Boolean\n  */\n\thas(name) {\n\t\tname = `${name}`;\n\t\tvalidateName(name);\n\t\treturn find(this[MAP], name) !== undefined;\n\t}\n\n\t/**\n  * Delete all header values given name\n  *\n  * @param   String  name  Header name\n  * @return  Void\n  */\n\tdelete(name) {\n\t\tname = `${name}`;\n\t\tvalidateName(name);\n\t\tconst key = find(this[MAP], name);\n\t\tif (key !== undefined) {\n\t\t\tdelete this[MAP][key];\n\t\t}\n\t}\n\n\t/**\n  * Return raw headers (non-spec api)\n  *\n  * @return  Object\n  */\n\traw() {\n\t\treturn this[MAP];\n\t}\n\n\t/**\n  * Get an iterator on keys.\n  *\n  * @return  Iterator\n  */\n\tkeys() {\n\t\treturn createHeadersIterator(this, 'key');\n\t}\n\n\t/**\n  * Get an iterator on values.\n  *\n  * @return  Iterator\n  */\n\tvalues() {\n\t\treturn createHeadersIterator(this, 'value');\n\t}\n\n\t/**\n  * Get an iterator on entries.\n  *\n  * This is the default iterator of the Headers object.\n  *\n  * @return  Iterator\n  */\n\t[Symbol.iterator]() {\n\t\treturn createHeadersIterator(this, 'key+value');\n\t}\n}\nHeaders.prototype.entries = Headers.prototype[Symbol.iterator];\n\nObject.defineProperty(Headers.prototype, Symbol.toStringTag, {\n\tvalue: 'Headers',\n\twritable: false,\n\tenumerable: false,\n\tconfigurable: true\n});\n\nObject.defineProperties(Headers.prototype, {\n\tget: { enumerable: true },\n\tforEach: { enumerable: true },\n\tset: { enumerable: true },\n\tappend: { enumerable: true },\n\thas: { enumerable: true },\n\tdelete: { enumerable: true },\n\tkeys: { enumerable: true },\n\tvalues: { enumerable: true },\n\tentries: { enumerable: true }\n});\n\nfunction getHeaders(headers) {\n\tlet kind = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'key+value';\n\n\tconst keys = Object.keys(headers[MAP]).sort();\n\treturn keys.map(kind === 'key' ? function (k) {\n\t\treturn k.toLowerCase();\n\t} : kind === 'value' ? function (k) {\n\t\treturn headers[MAP][k].join(', ');\n\t} : function (k) {\n\t\treturn [k.toLowerCase(), headers[MAP][k].join(', ')];\n\t});\n}\n\nconst INTERNAL = Symbol('internal');\n\nfunction createHeadersIterator(target, kind) {\n\tconst iterator = Object.create(HeadersIteratorPrototype);\n\titerator[INTERNAL] = {\n\t\ttarget,\n\t\tkind,\n\t\tindex: 0\n\t};\n\treturn iterator;\n}\n\nconst HeadersIteratorPrototype = Object.setPrototypeOf({\n\tnext() {\n\t\t// istanbul ignore if\n\t\tif (!this || Object.getPrototypeOf(this) !== HeadersIteratorPrototype) {\n\t\t\tthrow new TypeError('Value of `this` is not a HeadersIterator');\n\t\t}\n\n\t\tvar _INTERNAL = this[INTERNAL];\n\t\tconst target = _INTERNAL.target,\n\t\t      kind = _INTERNAL.kind,\n\t\t      index = _INTERNAL.index;\n\n\t\tconst values = getHeaders(target, kind);\n\t\tconst len = values.length;\n\t\tif (index >= len) {\n\t\t\treturn {\n\t\t\t\tvalue: undefined,\n\t\t\t\tdone: true\n\t\t\t};\n\t\t}\n\n\t\tthis[INTERNAL].index = index + 1;\n\n\t\treturn {\n\t\t\tvalue: values[index],\n\t\t\tdone: false\n\t\t};\n\t}\n}, Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));\n\nObject.defineProperty(HeadersIteratorPrototype, Symbol.toStringTag, {\n\tvalue: 'HeadersIterator',\n\twritable: false,\n\tenumerable: false,\n\tconfigurable: true\n});\n\n/**\n * Export the Headers object in a form that Node.js can consume.\n *\n * @param   Headers  headers\n * @return  Object\n */\nfunction exportNodeCompatibleHeaders(headers) {\n\tconst obj = Object.assign({ __proto__: null }, headers[MAP]);\n\n\t// http.request() only supports string as Host header. This hack makes\n\t// specifying custom Host header possible.\n\tconst hostHeaderKey = find(headers[MAP], 'Host');\n\tif (hostHeaderKey !== undefined) {\n\t\tobj[hostHeaderKey] = obj[hostHeaderKey][0];\n\t}\n\n\treturn obj;\n}\n\n/**\n * Create a Headers object from an object of headers, ignoring those that do\n * not conform to HTTP grammar productions.\n *\n * @param   Object  obj  Object of headers\n * @return  Headers\n */\nfunction createHeadersLenient(obj) {\n\tconst headers = new Headers();\n\tfor (const name of Object.keys(obj)) {\n\t\tif (invalidTokenRegex.test(name)) {\n\t\t\tcontinue;\n\t\t}\n\t\tif (Array.isArray(obj[name])) {\n\t\t\tfor (const val of obj[name]) {\n\t\t\t\tif (invalidHeaderCharRegex.test(val)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tif (headers[MAP][name] === undefined) {\n\t\t\t\t\theaders[MAP][name] = [val];\n\t\t\t\t} else {\n\t\t\t\t\theaders[MAP][name].push(val);\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (!invalidHeaderCharRegex.test(obj[name])) {\n\t\t\theaders[MAP][name] = [obj[name]];\n\t\t}\n\t}\n\treturn headers;\n}\n\nconst INTERNALS$1 = Symbol('Response internals');\n\n// fix an issue where \"STATUS_CODES\" aren't a named export for node <10\nconst STATUS_CODES = http.STATUS_CODES;\n\n/**\n * Response class\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */\nclass Response {\n\tconstructor() {\n\t\tlet body = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n\t\tlet opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n\t\tBody.call(this, body, opts);\n\n\t\tconst status = opts.status || 200;\n\t\tconst headers = new Headers(opts.headers);\n\n\t\tif (body != null && !headers.has('Content-Type')) {\n\t\t\tconst contentType = extractContentType(body);\n\t\t\tif (contentType) {\n\t\t\t\theaders.append('Content-Type', contentType);\n\t\t\t}\n\t\t}\n\n\t\tthis[INTERNALS$1] = {\n\t\t\turl: opts.url,\n\t\t\tstatus,\n\t\t\tstatusText: opts.statusText || STATUS_CODES[status],\n\t\t\theaders,\n\t\t\tcounter: opts.counter\n\t\t};\n\t}\n\n\tget url() {\n\t\treturn this[INTERNALS$1].url || '';\n\t}\n\n\tget status() {\n\t\treturn this[INTERNALS$1].status;\n\t}\n\n\t/**\n  * Convenience property representing if the request ended normally\n  */\n\tget ok() {\n\t\treturn this[INTERNALS$1].status >= 200 && this[INTERNALS$1].status < 300;\n\t}\n\n\tget redirected() {\n\t\treturn this[INTERNALS$1].counter > 0;\n\t}\n\n\tget statusText() {\n\t\treturn this[INTERNALS$1].statusText;\n\t}\n\n\tget headers() {\n\t\treturn this[INTERNALS$1].headers;\n\t}\n\n\t/**\n  * Clone this response\n  *\n  * @return  Response\n  */\n\tclone() {\n\t\treturn new Response(clone(this), {\n\t\t\turl: this.url,\n\t\t\tstatus: this.status,\n\t\t\tstatusText: this.statusText,\n\t\t\theaders: this.headers,\n\t\t\tok: this.ok,\n\t\t\tredirected: this.redirected\n\t\t});\n\t}\n}\n\nBody.mixIn(Response.prototype);\n\nObject.defineProperties(Response.prototype, {\n\turl: { enumerable: true },\n\tstatus: { enumerable: true },\n\tok: { enumerable: true },\n\tredirected: { enumerable: true },\n\tstatusText: { enumerable: true },\n\theaders: { enumerable: true },\n\tclone: { enumerable: true }\n});\n\nObject.defineProperty(Response.prototype, Symbol.toStringTag, {\n\tvalue: 'Response',\n\twritable: false,\n\tenumerable: false,\n\tconfigurable: true\n});\n\nconst INTERNALS$2 = Symbol('Request internals');\nconst URL = Url.URL || whatwgUrl.URL;\n\n// fix an issue where \"format\", \"parse\" aren't a named export for node <10\nconst parse_url = Url.parse;\nconst format_url = Url.format;\n\n/**\n * Wrapper around `new URL` to handle arbitrary URLs\n *\n * @param  {string} urlStr\n * @return {void}\n */\nfunction parseURL(urlStr) {\n\t/*\n \tCheck whether the URL is absolute or not\n \t\tScheme: https://tools.ietf.org/html/rfc3986#section-3.1\n \tAbsolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\n */\n\tif (/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.exec(urlStr)) {\n\t\turlStr = new URL(urlStr).toString();\n\t}\n\n\t// Fallback to old implementation for arbitrary URLs\n\treturn parse_url(urlStr);\n}\n\nconst streamDestructionSupported = 'destroy' in Stream.Readable.prototype;\n\n/**\n * Check if a value is an instance of Request.\n *\n * @param   Mixed   input\n * @return  Boolean\n */\nfunction isRequest(input) {\n\treturn typeof input === 'object' && typeof input[INTERNALS$2] === 'object';\n}\n\nfunction isAbortSignal(signal) {\n\tconst proto = signal && typeof signal === 'object' && Object.getPrototypeOf(signal);\n\treturn !!(proto && proto.constructor.name === 'AbortSignal');\n}\n\n/**\n * Request class\n *\n * @param   Mixed   input  Url or Request instance\n * @param   Object  init   Custom options\n * @return  Void\n */\nclass Request {\n\tconstructor(input) {\n\t\tlet init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n\t\tlet parsedURL;\n\n\t\t// normalize input\n\t\tif (!isRequest(input)) {\n\t\t\tif (input && input.href) {\n\t\t\t\t// in order to support Node.js' Url objects; though WHATWG's URL objects\n\t\t\t\t// will fall into this branch also (since their `toString()` will return\n\t\t\t\t// `href` property anyway)\n\t\t\t\tparsedURL = parseURL(input.href);\n\t\t\t} else {\n\t\t\t\t// coerce input to a string before attempting to parse\n\t\t\t\tparsedURL = parseURL(`${input}`);\n\t\t\t}\n\t\t\tinput = {};\n\t\t} else {\n\t\t\tparsedURL = parseURL(input.url);\n\t\t}\n\n\t\tlet method = init.method || input.method || 'GET';\n\t\tmethod = method.toUpperCase();\n\n\t\tif ((init.body != null || isRequest(input) && input.body !== null) && (method === 'GET' || method === 'HEAD')) {\n\t\t\tthrow new TypeError('Request with GET/HEAD method cannot have body');\n\t\t}\n\n\t\tlet inputBody = init.body != null ? init.body : isRequest(input) && input.body !== null ? clone(input) : null;\n\n\t\tBody.call(this, inputBody, {\n\t\t\ttimeout: init.timeout || input.timeout || 0,\n\t\t\tsize: init.size || input.size || 0\n\t\t});\n\n\t\tconst headers = new Headers(init.headers || input.headers || {});\n\n\t\tif (inputBody != null && !headers.has('Content-Type')) {\n\t\t\tconst contentType = extractContentType(inputBody);\n\t\t\tif (contentType) {\n\t\t\t\theaders.append('Content-Type', contentType);\n\t\t\t}\n\t\t}\n\n\t\tlet signal = isRequest(input) ? input.signal : null;\n\t\tif ('signal' in init) signal = init.signal;\n\n\t\tif (signal != null && !isAbortSignal(signal)) {\n\t\t\tthrow new TypeError('Expected signal to be an instanceof AbortSignal');\n\t\t}\n\n\t\tthis[INTERNALS$2] = {\n\t\t\tmethod,\n\t\t\tredirect: init.redirect || input.redirect || 'follow',\n\t\t\theaders,\n\t\t\tparsedURL,\n\t\t\tsignal\n\t\t};\n\n\t\t// node-fetch-only options\n\t\tthis.follow = init.follow !== undefined ? init.follow : input.follow !== undefined ? input.follow : 20;\n\t\tthis.compress = init.compress !== undefined ? init.compress : input.compress !== undefined ? input.compress : true;\n\t\tthis.counter = init.counter || input.counter || 0;\n\t\tthis.agent = init.agent || input.agent;\n\t}\n\n\tget method() {\n\t\treturn this[INTERNALS$2].method;\n\t}\n\n\tget url() {\n\t\treturn format_url(this[INTERNALS$2].parsedURL);\n\t}\n\n\tget headers() {\n\t\treturn this[INTERNALS$2].headers;\n\t}\n\n\tget redirect() {\n\t\treturn this[INTERNALS$2].redirect;\n\t}\n\n\tget signal() {\n\t\treturn this[INTERNALS$2].signal;\n\t}\n\n\t/**\n  * Clone this request\n  *\n  * @return  Request\n  */\n\tclone() {\n\t\treturn new Request(this);\n\t}\n}\n\nBody.mixIn(Request.prototype);\n\nObject.defineProperty(Request.prototype, Symbol.toStringTag, {\n\tvalue: 'Request',\n\twritable: false,\n\tenumerable: false,\n\tconfigurable: true\n});\n\nObject.defineProperties(Request.prototype, {\n\tmethod: { enumerable: true },\n\turl: { enumerable: true },\n\theaders: { enumerable: true },\n\tredirect: { enumerable: true },\n\tclone: { enumerable: true },\n\tsignal: { enumerable: true }\n});\n\n/**\n * Convert a Request to Node.js http request options.\n *\n * @param   Request  A Request instance\n * @return  Object   The options object to be passed to http.request\n */\nfunction getNodeRequestOptions(request) {\n\tconst parsedURL = request[INTERNALS$2].parsedURL;\n\tconst headers = new Headers(request[INTERNALS$2].headers);\n\n\t// fetch step 1.3\n\tif (!headers.has('Accept')) {\n\t\theaders.set('Accept', '*/*');\n\t}\n\n\t// Basic fetch\n\tif (!parsedURL.protocol || !parsedURL.hostname) {\n\t\tthrow new TypeError('Only absolute URLs are supported');\n\t}\n\n\tif (!/^https?:$/.test(parsedURL.protocol)) {\n\t\tthrow new TypeError('Only HTTP(S) protocols are supported');\n\t}\n\n\tif (request.signal && request.body instanceof Stream.Readable && !streamDestructionSupported) {\n\t\tthrow new Error('Cancellation of streamed requests with AbortSignal is not supported in node < 8');\n\t}\n\n\t// HTTP-network-or-cache fetch steps 2.4-2.7\n\tlet contentLengthValue = null;\n\tif (request.body == null && /^(POST|PUT)$/i.test(request.method)) {\n\t\tcontentLengthValue = '0';\n\t}\n\tif (request.body != null) {\n\t\tconst totalBytes = getTotalBytes(request);\n\t\tif (typeof totalBytes === 'number') {\n\t\t\tcontentLengthValue = String(totalBytes);\n\t\t}\n\t}\n\tif (contentLengthValue) {\n\t\theaders.set('Content-Length', contentLengthValue);\n\t}\n\n\t// HTTP-network-or-cache fetch step 2.11\n\tif (!headers.has('User-Agent')) {\n\t\theaders.set('User-Agent', 'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)');\n\t}\n\n\t// HTTP-network-or-cache fetch step 2.15\n\tif (request.compress && !headers.has('Accept-Encoding')) {\n\t\theaders.set('Accept-Encoding', 'gzip,deflate');\n\t}\n\n\tlet agent = request.agent;\n\tif (typeof agent === 'function') {\n\t\tagent = agent(parsedURL);\n\t}\n\n\t// HTTP-network fetch step 4.2\n\t// chunked encoding is handled by Node.js\n\n\treturn Object.assign({}, parsedURL, {\n\t\tmethod: request.method,\n\t\theaders: exportNodeCompatibleHeaders(headers),\n\t\tagent\n\t});\n}\n\n/**\n * abort-error.js\n *\n * AbortError interface for cancelled requests\n */\n\n/**\n * Create AbortError instance\n *\n * @param   String      message      Error message for human\n * @return  AbortError\n */\nfunction AbortError(message) {\n  Error.call(this, message);\n\n  this.type = 'aborted';\n  this.message = message;\n\n  // hide custom error implementation details from end-users\n  Error.captureStackTrace(this, this.constructor);\n}\n\nAbortError.prototype = Object.create(Error.prototype);\nAbortError.prototype.constructor = AbortError;\nAbortError.prototype.name = 'AbortError';\n\nconst URL$1 = Url.URL || whatwgUrl.URL;\n\n// fix an issue where \"PassThrough\", \"resolve\" aren't a named export for node <10\nconst PassThrough$1 = Stream.PassThrough;\n\nconst isDomainOrSubdomain = function isDomainOrSubdomain(destination, original) {\n\tconst orig = new URL$1(original).hostname;\n\tconst dest = new URL$1(destination).hostname;\n\n\treturn orig === dest || orig[orig.length - dest.length - 1] === '.' && orig.endsWith(dest);\n};\n\n/**\n * isSameProtocol reports whether the two provided URLs use the same protocol.\n *\n * Both domains must already be in canonical form.\n * @param {string|URL} original\n * @param {string|URL} destination\n */\nconst isSameProtocol = function isSameProtocol(destination, original) {\n\tconst orig = new URL$1(original).protocol;\n\tconst dest = new URL$1(destination).protocol;\n\n\treturn orig === dest;\n};\n\n/**\n * Fetch function\n *\n * @param   Mixed    url   Absolute url or Request instance\n * @param   Object   opts  Fetch options\n * @return  Promise\n */\nfunction fetch(url, opts) {\n\n\t// allow custom promise\n\tif (!fetch.Promise) {\n\t\tthrow new Error('native promise missing, set fetch.Promise to your favorite alternative');\n\t}\n\n\tBody.Promise = fetch.Promise;\n\n\t// wrap http.request into fetch\n\treturn new fetch.Promise(function (resolve, reject) {\n\t\t// build request object\n\t\tconst request = new Request(url, opts);\n\t\tconst options = getNodeRequestOptions(request);\n\n\t\tconst send = (options.protocol === 'https:' ? https : http).request;\n\t\tconst signal = request.signal;\n\n\t\tlet response = null;\n\n\t\tconst abort = function abort() {\n\t\t\tlet error = new AbortError('The user aborted a request.');\n\t\t\treject(error);\n\t\t\tif (request.body && request.body instanceof Stream.Readable) {\n\t\t\t\tdestroyStream(request.body, error);\n\t\t\t}\n\t\t\tif (!response || !response.body) return;\n\t\t\tresponse.body.emit('error', error);\n\t\t};\n\n\t\tif (signal && signal.aborted) {\n\t\t\tabort();\n\t\t\treturn;\n\t\t}\n\n\t\tconst abortAndFinalize = function abortAndFinalize() {\n\t\t\tabort();\n\t\t\tfinalize();\n\t\t};\n\n\t\t// send request\n\t\tconst req = send(options);\n\t\tlet reqTimeout;\n\n\t\tif (signal) {\n\t\t\tsignal.addEventListener('abort', abortAndFinalize);\n\t\t}\n\n\t\tfunction finalize() {\n\t\t\treq.abort();\n\t\t\tif (signal) signal.removeEventListener('abort', abortAndFinalize);\n\t\t\tclearTimeout(reqTimeout);\n\t\t}\n\n\t\tif (request.timeout) {\n\t\t\treq.once('socket', function (socket) {\n\t\t\t\treqTimeout = setTimeout(function () {\n\t\t\t\t\treject(new FetchError(`network timeout at: ${request.url}`, 'request-timeout'));\n\t\t\t\t\tfinalize();\n\t\t\t\t}, request.timeout);\n\t\t\t});\n\t\t}\n\n\t\treq.on('error', function (err) {\n\t\t\treject(new FetchError(`request to ${request.url} failed, reason: ${err.message}`, 'system', err));\n\n\t\t\tif (response && response.body) {\n\t\t\t\tdestroyStream(response.body, err);\n\t\t\t}\n\n\t\t\tfinalize();\n\t\t});\n\n\t\tfixResponseChunkedTransferBadEnding(req, function (err) {\n\t\t\tif (signal && signal.aborted) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (response && response.body) {\n\t\t\t\tdestroyStream(response.body, err);\n\t\t\t}\n\t\t});\n\n\t\t/* c8 ignore next 18 */\n\t\tif (parseInt(process.version.substring(1)) < 14) {\n\t\t\t// Before Node.js 14, pipeline() does not fully support async iterators and does not always\n\t\t\t// properly handle when the socket close/end events are out of order.\n\t\t\treq.on('socket', function (s) {\n\t\t\t\ts.addListener('close', function (hadError) {\n\t\t\t\t\t// if a data listener is still present we didn't end cleanly\n\t\t\t\t\tconst hasDataListener = s.listenerCount('data') > 0;\n\n\t\t\t\t\t// if end happened before close but the socket didn't emit an error, do it now\n\t\t\t\t\tif (response && hasDataListener && !hadError && !(signal && signal.aborted)) {\n\t\t\t\t\t\tconst err = new Error('Premature close');\n\t\t\t\t\t\terr.code = 'ERR_STREAM_PREMATURE_CLOSE';\n\t\t\t\t\t\tresponse.body.emit('error', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\n\t\treq.on('response', function (res) {\n\t\t\tclearTimeout(reqTimeout);\n\n\t\t\tconst headers = createHeadersLenient(res.headers);\n\n\t\t\t// HTTP fetch step 5\n\t\t\tif (fetch.isRedirect(res.statusCode)) {\n\t\t\t\t// HTTP fetch step 5.2\n\t\t\t\tconst location = headers.get('Location');\n\n\t\t\t\t// HTTP fetch step 5.3\n\t\t\t\tlet locationURL = null;\n\t\t\t\ttry {\n\t\t\t\t\tlocationURL = location === null ? null : new URL$1(location, request.url).toString();\n\t\t\t\t} catch (err) {\n\t\t\t\t\t// error here can only be invalid URL in Location: header\n\t\t\t\t\t// do not throw when options.redirect == manual\n\t\t\t\t\t// let the user extract the errorneous redirect URL\n\t\t\t\t\tif (request.redirect !== 'manual') {\n\t\t\t\t\t\treject(new FetchError(`uri requested responds with an invalid redirect URL: ${location}`, 'invalid-redirect'));\n\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// HTTP fetch step 5.5\n\t\t\t\tswitch (request.redirect) {\n\t\t\t\t\tcase 'error':\n\t\t\t\t\t\treject(new FetchError(`uri requested responds with a redirect, redirect mode is set to error: ${request.url}`, 'no-redirect'));\n\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\treturn;\n\t\t\t\t\tcase 'manual':\n\t\t\t\t\t\t// node-fetch-specific step: make manual redirect a bit easier to use by setting the Location header value to the resolved URL.\n\t\t\t\t\t\tif (locationURL !== null) {\n\t\t\t\t\t\t\t// handle corrupted header\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\theaders.set('Location', locationURL);\n\t\t\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t\t\t// istanbul ignore next: nodejs server prevent invalid response headers, we can't test this through normal request\n\t\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'follow':\n\t\t\t\t\t\t// HTTP-redirect fetch step 2\n\t\t\t\t\t\tif (locationURL === null) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 5\n\t\t\t\t\t\tif (request.counter >= request.follow) {\n\t\t\t\t\t\t\treject(new FetchError(`maximum redirect reached at: ${request.url}`, 'max-redirect'));\n\t\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 6 (counter increment)\n\t\t\t\t\t\t// Create a new Request object.\n\t\t\t\t\t\tconst requestOpts = {\n\t\t\t\t\t\t\theaders: new Headers(request.headers),\n\t\t\t\t\t\t\tfollow: request.follow,\n\t\t\t\t\t\t\tcounter: request.counter + 1,\n\t\t\t\t\t\t\tagent: request.agent,\n\t\t\t\t\t\t\tcompress: request.compress,\n\t\t\t\t\t\t\tmethod: request.method,\n\t\t\t\t\t\t\tbody: request.body,\n\t\t\t\t\t\t\tsignal: request.signal,\n\t\t\t\t\t\t\ttimeout: request.timeout,\n\t\t\t\t\t\t\tsize: request.size\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (!isDomainOrSubdomain(request.url, locationURL) || !isSameProtocol(request.url, locationURL)) {\n\t\t\t\t\t\t\tfor (const name of ['authorization', 'www-authenticate', 'cookie', 'cookie2']) {\n\t\t\t\t\t\t\t\trequestOpts.headers.delete(name);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 9\n\t\t\t\t\t\tif (res.statusCode !== 303 && request.body && getTotalBytes(request) === null) {\n\t\t\t\t\t\t\treject(new FetchError('Cannot follow redirect with body being a readable stream', 'unsupported-redirect'));\n\t\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 11\n\t\t\t\t\t\tif (res.statusCode === 303 || (res.statusCode === 301 || res.statusCode === 302) && request.method === 'POST') {\n\t\t\t\t\t\t\trequestOpts.method = 'GET';\n\t\t\t\t\t\t\trequestOpts.body = undefined;\n\t\t\t\t\t\t\trequestOpts.headers.delete('content-length');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 15\n\t\t\t\t\t\tresolve(fetch(new Request(locationURL, requestOpts)));\n\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// prepare response\n\t\t\tres.once('end', function () {\n\t\t\t\tif (signal) signal.removeEventListener('abort', abortAndFinalize);\n\t\t\t});\n\t\t\tlet body = res.pipe(new PassThrough$1());\n\n\t\t\tconst response_options = {\n\t\t\t\turl: request.url,\n\t\t\t\tstatus: res.statusCode,\n\t\t\t\tstatusText: res.statusMessage,\n\t\t\t\theaders: headers,\n\t\t\t\tsize: request.size,\n\t\t\t\ttimeout: request.timeout,\n\t\t\t\tcounter: request.counter\n\t\t\t};\n\n\t\t\t// HTTP-network fetch step ********\n\t\t\tconst codings = headers.get('Content-Encoding');\n\n\t\t\t// HTTP-network fetch step ********: handle content codings\n\n\t\t\t// in following scenarios we ignore compression support\n\t\t\t// 1. compression support is disabled\n\t\t\t// 2. HEAD request\n\t\t\t// 3. no Content-Encoding header\n\t\t\t// 4. no content response (204)\n\t\t\t// 5. content not modified response (304)\n\t\t\tif (!request.compress || request.method === 'HEAD' || codings === null || res.statusCode === 204 || res.statusCode === 304) {\n\t\t\t\tresponse = new Response(body, response_options);\n\t\t\t\tresolve(response);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// For Node v6+\n\t\t\t// Be less strict when decoding compressed responses, since sometimes\n\t\t\t// servers send slightly invalid responses that are still accepted\n\t\t\t// by common browsers.\n\t\t\t// Always using Z_SYNC_FLUSH is what cURL does.\n\t\t\tconst zlibOptions = {\n\t\t\t\tflush: zlib.Z_SYNC_FLUSH,\n\t\t\t\tfinishFlush: zlib.Z_SYNC_FLUSH\n\t\t\t};\n\n\t\t\t// for gzip\n\t\t\tif (codings == 'gzip' || codings == 'x-gzip') {\n\t\t\t\tbody = body.pipe(zlib.createGunzip(zlibOptions));\n\t\t\t\tresponse = new Response(body, response_options);\n\t\t\t\tresolve(response);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// for deflate\n\t\t\tif (codings == 'deflate' || codings == 'x-deflate') {\n\t\t\t\t// handle the infamous raw deflate response from old servers\n\t\t\t\t// a hack for old IIS and Apache servers\n\t\t\t\tconst raw = res.pipe(new PassThrough$1());\n\t\t\t\traw.once('data', function (chunk) {\n\t\t\t\t\t// see http://stackoverflow.com/questions/37519828\n\t\t\t\t\tif ((chunk[0] & 0x0F) === 0x08) {\n\t\t\t\t\t\tbody = body.pipe(zlib.createInflate());\n\t\t\t\t\t} else {\n\t\t\t\t\t\tbody = body.pipe(zlib.createInflateRaw());\n\t\t\t\t\t}\n\t\t\t\t\tresponse = new Response(body, response_options);\n\t\t\t\t\tresolve(response);\n\t\t\t\t});\n\t\t\t\traw.on('end', function () {\n\t\t\t\t\t// some old IIS servers return zero-length OK deflate responses, so 'data' is never emitted.\n\t\t\t\t\tif (!response) {\n\t\t\t\t\t\tresponse = new Response(body, response_options);\n\t\t\t\t\t\tresolve(response);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// for br\n\t\t\tif (codings == 'br' && typeof zlib.createBrotliDecompress === 'function') {\n\t\t\t\tbody = body.pipe(zlib.createBrotliDecompress());\n\t\t\t\tresponse = new Response(body, response_options);\n\t\t\t\tresolve(response);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// otherwise, use response as-is\n\t\t\tresponse = new Response(body, response_options);\n\t\t\tresolve(response);\n\t\t});\n\n\t\twriteToStream(req, request);\n\t});\n}\nfunction fixResponseChunkedTransferBadEnding(request, errorCallback) {\n\tlet socket;\n\n\trequest.on('socket', function (s) {\n\t\tsocket = s;\n\t});\n\n\trequest.on('response', function (response) {\n\t\tconst headers = response.headers;\n\n\t\tif (headers['transfer-encoding'] === 'chunked' && !headers['content-length']) {\n\t\t\tresponse.once('close', function (hadError) {\n\t\t\t\t// tests for socket presence, as in some situations the\n\t\t\t\t// the 'socket' event is not triggered for the request\n\t\t\t\t// (happens in deno), avoids `TypeError`\n\t\t\t\t// if a data listener is still present we didn't end cleanly\n\t\t\t\tconst hasDataListener = socket && socket.listenerCount('data') > 0;\n\n\t\t\t\tif (hasDataListener && !hadError) {\n\t\t\t\t\tconst err = new Error('Premature close');\n\t\t\t\t\terr.code = 'ERR_STREAM_PREMATURE_CLOSE';\n\t\t\t\t\terrorCallback(err);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t});\n}\n\nfunction destroyStream(stream, err) {\n\tif (stream.destroy) {\n\t\tstream.destroy(err);\n\t} else {\n\t\t// node < 8\n\t\tstream.emit('error', err);\n\t\tstream.end();\n\t}\n}\n\n/**\n * Redirect code matching\n *\n * @param   Number   code  Status code\n * @return  Boolean\n */\nfetch.isRedirect = function (code) {\n\treturn code === 301 || code === 302 || code === 303 || code === 307 || code === 308;\n};\n\n// expose Promise\nfetch.Promise = global.Promise;\n\nexport default fetch;\nexport { Headers, Request, Response, FetchError, AbortError };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,kHAAkH;AAElH,gDAAgD;AAChD,MAAM,WAAW,qGAAA,CAAA,UAAM,CAAC,QAAQ;AAEhC,MAAM,SAAS,OAAO;AACtB,MAAM,OAAO,OAAO;AAEpB,MAAM;IACL,aAAc;QACb,IAAI,CAAC,KAAK,GAAG;QAEb,MAAM,YAAY,SAAS,CAAC,EAAE;QAC9B,MAAM,UAAU,SAAS,CAAC,EAAE;QAE5B,MAAM,UAAU,EAAE;QAClB,IAAI,OAAO;QAEX,IAAI,WAAW;YACd,MAAM,IAAI;YACV,MAAM,SAAS,OAAO,EAAE,MAAM;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;gBAChC,MAAM,UAAU,CAAC,CAAC,EAAE;gBACpB,IAAI;gBACJ,IAAI,mBAAmB,QAAQ;oBAC9B,SAAS;gBACV,OAAO,IAAI,YAAY,MAAM,CAAC,UAAU;oBACvC,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU;gBAC5E,OAAO,IAAI,mBAAmB,aAAa;oBAC1C,SAAS,OAAO,IAAI,CAAC;gBACtB,OAAO,IAAI,mBAAmB,MAAM;oBACnC,SAAS,OAAO,CAAC,OAAO;gBACzB,OAAO;oBACN,SAAS,OAAO,IAAI,CAAC,OAAO,YAAY,WAAW,UAAU,OAAO;gBACrE;gBACA,QAAQ,OAAO,MAAM;gBACrB,QAAQ,IAAI,CAAC;YACd;QACD;QAEA,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,CAAC;QAE7B,IAAI,OAAO,WAAW,QAAQ,IAAI,KAAK,aAAa,OAAO,QAAQ,IAAI,EAAE,WAAW;QACpF,IAAI,QAAQ,CAAC,mBAAmB,IAAI,CAAC,OAAO;YAC3C,IAAI,CAAC,KAAK,GAAG;QACd;IACD;IACA,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;IAC3B;IACA,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,KAAK;IAClB;IACA,OAAO;QACN,OAAO,QAAQ,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;IAC7C;IACA,cAAc;QACb,MAAM,MAAM,IAAI,CAAC,OAAO;QACxB,MAAM,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,UAAU,EAAE,IAAI,UAAU,GAAG,IAAI,UAAU;QAC3E,OAAO,QAAQ,OAAO,CAAC;IACxB;IACA,SAAS;QACR,MAAM,WAAW,IAAI;QACrB,SAAS,KAAK,GAAG,YAAa;QAC9B,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO;QAC1B,SAAS,IAAI,CAAC;QACd,OAAO;IACR;IACA,WAAW;QACV,OAAO;IACR;IACA,QAAQ;QACP,MAAM,OAAO,IAAI,CAAC,IAAI;QAEtB,MAAM,QAAQ,SAAS,CAAC,EAAE;QAC1B,MAAM,MAAM,SAAS,CAAC,EAAE;QACxB,IAAI,eAAe;QACnB,IAAI,UAAU,WAAW;YACxB,gBAAgB;QACjB,OAAO,IAAI,QAAQ,GAAG;YACrB,gBAAgB,KAAK,GAAG,CAAC,OAAO,OAAO;QACxC,OAAO;YACN,gBAAgB,KAAK,GAAG,CAAC,OAAO;QACjC;QACA,IAAI,QAAQ,WAAW;YACtB,cAAc;QACf,OAAO,IAAI,MAAM,GAAG;YACnB,cAAc,KAAK,GAAG,CAAC,OAAO,KAAK;QACpC,OAAO;YACN,cAAc,KAAK,GAAG,CAAC,KAAK;QAC7B;QACA,MAAM,OAAO,KAAK,GAAG,CAAC,cAAc,eAAe;QAEnD,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,MAAM,eAAe,OAAO,KAAK,CAAC,eAAe,gBAAgB;QACjE,MAAM,OAAO,IAAI,KAAK,EAAE,EAAE;YAAE,MAAM,SAAS,CAAC,EAAE;QAAC;QAC/C,IAAI,CAAC,OAAO,GAAG;QACf,OAAO;IACR;AACD;AAEA,OAAO,gBAAgB,CAAC,KAAK,SAAS,EAAE;IACvC,MAAM;QAAE,YAAY;IAAK;IACzB,MAAM;QAAE,YAAY;IAAK;IACzB,OAAO;QAAE,YAAY;IAAK;AAC3B;AAEA,OAAO,cAAc,CAAC,KAAK,SAAS,EAAE,OAAO,WAAW,EAAE;IACzD,OAAO;IACP,UAAU;IACV,YAAY;IACZ,cAAc;AACf;AAEA;;;;CAIC,GAED;;;;;;;CAOC,GACD,SAAS,WAAW,OAAO,EAAE,IAAI,EAAE,WAAW;IAC5C,MAAM,IAAI,CAAC,IAAI,EAAE;IAEjB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,IAAI,GAAG;IAEZ,iEAAiE;IACjE,IAAI,aAAa;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,YAAY,IAAI;IAC3C;IAEA,0DAA0D;IAC1D,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;AAChD;AAEA,WAAW,SAAS,GAAG,OAAO,MAAM,CAAC,MAAM,SAAS;AACpD,WAAW,SAAS,CAAC,WAAW,GAAG;AACnC,WAAW,SAAS,CAAC,IAAI,GAAG;AAE5B,IAAI;AACJ,IAAI;IACH,UAAU;;;;SAAoB,OAAO;AACtC,EAAE,OAAO,GAAG,CAAC;AAEb,MAAM,YAAY,OAAO;AAEzB,qEAAqE;AACrE,MAAM,cAAc,qGAAA,CAAA,UAAM,CAAC,WAAW;AAEtC;;;;;;;;CAQC,GACD,SAAS,KAAK,IAAI;IACjB,IAAI,QAAQ,IAAI;IAEhB,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC,GAC5E,YAAY,KAAK,IAAI;IAEzB,IAAI,OAAO,cAAc,YAAY,IAAI;IACzC,IAAI,eAAe,KAAK,OAAO;IAC/B,IAAI,UAAU,iBAAiB,YAAY,IAAI;IAE/C,IAAI,QAAQ,MAAM;QACjB,4BAA4B;QAC5B,OAAO;IACR,OAAO,IAAI,kBAAkB,OAAO;QACnC,4BAA4B;QAC5B,OAAO,OAAO,IAAI,CAAC,KAAK,QAAQ;IACjC,OAAO,IAAI,OAAO;SAAc,IAAI,OAAO,QAAQ,CAAC;SAAc,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,wBAAwB;QACtI,sBAAsB;QACtB,OAAO,OAAO,IAAI,CAAC;IACpB,OAAO,IAAI,YAAY,MAAM,CAAC,OAAO;QACpC,0BAA0B;QAC1B,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE,KAAK,UAAU,EAAE,KAAK,UAAU;IACjE,OAAO,IAAI,gBAAgB,qGAAA,CAAA,UAAM;SAAS;QACzC,oBAAoB;QACpB,+BAA+B;QAC/B,OAAO,OAAO,IAAI,CAAC,OAAO;IAC3B;IACA,IAAI,CAAC,UAAU,GAAG;QACjB;QACA,WAAW;QACX,OAAO;IACR;IACA,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,OAAO,GAAG;IAEf,IAAI,gBAAgB,qGAAA,CAAA,UAAM,EAAE;QAC3B,KAAK,EAAE,CAAC,SAAS,SAAU,GAAG;YAC7B,MAAM,QAAQ,IAAI,IAAI,KAAK,eAAe,MAAM,IAAI,WAAW,CAAC,4CAA4C,EAAE,MAAM,GAAG,CAAC,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,UAAU;YACrJ,KAAK,CAAC,UAAU,CAAC,KAAK,GAAG;QAC1B;IACD;AACD;AAEA,KAAK,SAAS,GAAG;IAChB,IAAI,QAAO;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC5B;IAEA,IAAI,YAAW;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS;IACjC;IAEA;;;;EAIC,GACD;QACC,OAAO,YAAY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAU,GAAG;YAC/C,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,UAAU,EAAE,IAAI,UAAU,GAAG,IAAI,UAAU;QACxE;IACD;IAEA;;;;EAIC,GACD;QACC,IAAI,KAAK,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAC7D,OAAO,YAAY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAU,GAAG;YAC/C,OAAO,OAAO,MAAM,CACpB,kBAAkB;YAClB,IAAI,KAAK,EAAE,EAAE;gBACZ,MAAM,GAAG,WAAW;YACrB,IAAI;gBACH,CAAC,OAAO,EAAE;YACX;QACD;IACD;IAEA;;;;EAIC,GACD;QACC,IAAI,SAAS,IAAI;QAEjB,OAAO,YAAY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAU,MAAM;YAClD,IAAI;gBACH,OAAO,KAAK,KAAK,CAAC,OAAO,QAAQ;YAClC,EAAE,OAAO,KAAK;gBACb,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,8BAA8B,EAAE,OAAO,GAAG,CAAC,SAAS,EAAE,IAAI,OAAO,EAAE,EAAE;YACjH;QACD;IACD;IAEA;;;;EAIC,GACD;QACC,OAAO,YAAY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAU,MAAM;YAClD,OAAO,OAAO,QAAQ;QACvB;IACD;IAEA;;;;EAIC,GACD;QACC,OAAO,YAAY,IAAI,CAAC,IAAI;IAC7B;IAEA;;;;;EAKC,GACD;QACC,IAAI,SAAS,IAAI;QAEjB,OAAO,YAAY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAU,MAAM;YAClD,OAAO,YAAY,QAAQ,OAAO,OAAO;QAC1C;IACD;AACD;AAEA,8CAA8C;AAC9C,OAAO,gBAAgB,CAAC,KAAK,SAAS,EAAE;IACvC,MAAM;QAAE,YAAY;IAAK;IACzB,UAAU;QAAE,YAAY;IAAK;IAC7B,aAAa;QAAE,YAAY;IAAK;IAChC,MAAM;QAAE,YAAY;IAAK;IACzB,MAAM;QAAE,YAAY;IAAK;IACzB,MAAM;QAAE,YAAY;IAAK;AAC1B;AAEA,KAAK,KAAK,GAAG,SAAU,KAAK;IAC3B,KAAK,MAAM,QAAQ,OAAO,mBAAmB,CAAC,KAAK,SAAS,EAAG;QAC9D,qCAAqC;QACrC,IAAI,CAAC,CAAC,QAAQ,KAAK,GAAG;YACrB,MAAM,OAAO,OAAO,wBAAwB,CAAC,KAAK,SAAS,EAAE;YAC7D,OAAO,cAAc,CAAC,OAAO,MAAM;QACpC;IACD;AACD;AAEA;;;;;;CAMC,GACD,SAAS;IACR,IAAI,SAAS,IAAI;IAEjB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;QAC9B,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,GAAG,EAAE;IAC9E;IAEA,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG;IAE5B,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;QAC1B,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK;IACjD;IAEA,IAAI,OAAO,IAAI,CAAC,IAAI;IAEpB,eAAe;IACf,IAAI,SAAS,MAAM;QAClB,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC;IAC1C;IAEA,eAAe;IACf,IAAI,OAAO,OAAO;QACjB,OAAO,KAAK,MAAM;IACnB;IAEA,iBAAiB;IACjB,IAAI,OAAO,QAAQ,CAAC,OAAO;QAC1B,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC;IAC7B;IAEA,0CAA0C;IAC1C,IAAI,CAAC,CAAC,gBAAgB,qGAAA,CAAA,UAAM,GAAG;QAC9B,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC;IAC1C;IAEA,iBAAiB;IACjB,yCAAyC;IACzC,IAAI,QAAQ,EAAE;IACd,IAAI,aAAa;IACjB,IAAI,QAAQ;IAEZ,OAAO,IAAI,KAAK,OAAO,CAAC,SAAU,OAAO,EAAE,MAAM;QAChD,IAAI;QAEJ,sCAAsC;QACtC,IAAI,OAAO,OAAO,EAAE;YACnB,aAAa,WAAW;gBACvB,QAAQ;gBACR,OAAO,IAAI,WAAW,CAAC,uCAAuC,EAAE,OAAO,GAAG,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,EAAE;YAC1G,GAAG,OAAO,OAAO;QAClB;QAEA,uBAAuB;QACvB,KAAK,EAAE,CAAC,SAAS,SAAU,GAAG;YAC7B,IAAI,IAAI,IAAI,KAAK,cAAc;gBAC9B,qDAAqD;gBACrD,QAAQ;gBACR,OAAO;YACR,OAAO;gBACN,mDAAmD;gBACnD,OAAO,IAAI,WAAW,CAAC,4CAA4C,EAAE,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,UAAU;YAC9G;QACD;QAEA,KAAK,EAAE,CAAC,QAAQ,SAAU,KAAK;YAC9B,IAAI,SAAS,UAAU,MAAM;gBAC5B;YACD;YAEA,IAAI,OAAO,IAAI,IAAI,aAAa,MAAM,MAAM,GAAG,OAAO,IAAI,EAAE;gBAC3D,QAAQ;gBACR,OAAO,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,GAAG,CAAC,aAAa,EAAE,OAAO,IAAI,EAAE,EAAE;gBAClF;YACD;YAEA,cAAc,MAAM,MAAM;YAC1B,MAAM,IAAI,CAAC;QACZ;QAEA,KAAK,EAAE,CAAC,OAAO;YACd,IAAI,OAAO;gBACV;YACD;YAEA,aAAa;YAEb,IAAI;gBACH,QAAQ,OAAO,MAAM,CAAC,OAAO;YAC9B,EAAE,OAAO,KAAK;gBACb,kEAAkE;gBAClE,OAAO,IAAI,WAAW,CAAC,+CAA+C,EAAE,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,UAAU;YACjH;QACD;IACD;AACD;AAEA;;;;;;;CAOC,GACD,SAAS,YAAY,MAAM,EAAE,OAAO;IACnC,IAAI,OAAO,YAAY,YAAY;QAClC,MAAM,IAAI,MAAM;IACjB;IAEA,MAAM,KAAK,QAAQ,GAAG,CAAC;IACvB,IAAI,UAAU;IACd,IAAI,KAAK;IAET,SAAS;IACT,IAAI,IAAI;QACP,MAAM,mBAAmB,IAAI,CAAC;IAC/B;IAEA,2EAA2E;IAC3E,MAAM,OAAO,KAAK,CAAC,GAAG,MAAM,QAAQ;IAEpC,QAAQ;IACR,IAAI,CAAC,OAAO,KAAK;QAChB,MAAM,iCAAiC,IAAI,CAAC;IAC7C;IAEA,QAAQ;IACR,IAAI,CAAC,OAAO,KAAK;QAChB,MAAM,yEAAyE,IAAI,CAAC;QACpF,IAAI,CAAC,KAAK;YACT,MAAM,yEAAyE,IAAI,CAAC;YACpF,IAAI,KAAK;gBACR,IAAI,GAAG,IAAI,kBAAkB;YAC9B;QACD;QAEA,IAAI,KAAK;YACR,MAAM,gBAAgB,IAAI,CAAC,IAAI,GAAG;QACnC;IACD;IAEA,MAAM;IACN,IAAI,CAAC,OAAO,KAAK;QAChB,MAAM,mCAAmC,IAAI,CAAC;IAC/C;IAEA,gBAAgB;IAChB,IAAI,KAAK;QACR,UAAU,IAAI,GAAG;QAEjB,0DAA0D;QAC1D,0CAA0C;QAC1C,IAAI,YAAY,YAAY,YAAY,OAAO;YAC9C,UAAU;QACX;IACD;IAEA,8CAA8C;IAC9C,OAAO,QAAQ,QAAQ,SAAS,SAAS,QAAQ;AAClD;AAEA;;;;;;CAMC,GACD,SAAS,kBAAkB,GAAG;IAC7B,wCAAwC;IACxC,IAAI,OAAO,QAAQ,YAAY,OAAO,IAAI,MAAM,KAAK,cAAc,OAAO,IAAI,MAAM,KAAK,cAAc,OAAO,IAAI,GAAG,KAAK,cAAc,OAAO,IAAI,MAAM,KAAK,cAAc,OAAO,IAAI,GAAG,KAAK,cAAc,OAAO,IAAI,GAAG,KAAK,YAAY;QAC3O,OAAO;IACR;IAEA,6DAA6D;IAC7D,OAAO,IAAI,WAAW,CAAC,IAAI,KAAK,qBAAqB,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,8BAA8B,OAAO,IAAI,IAAI,KAAK;AAChJ;AAEA;;;;CAIC,GACD,SAAS,OAAO,GAAG;IAClB,OAAO,OAAO,QAAQ,YAAY,OAAO,IAAI,WAAW,KAAK,cAAc,OAAO,IAAI,IAAI,KAAK,YAAY,OAAO,IAAI,MAAM,KAAK,cAAc,OAAO,IAAI,WAAW,KAAK,cAAc,OAAO,IAAI,WAAW,CAAC,IAAI,KAAK,YAAY,gBAAgB,IAAI,CAAC,IAAI,WAAW,CAAC,IAAI,KAAK,gBAAgB,IAAI,CAAC,GAAG,CAAC,OAAO,WAAW,CAAC;AAC/T;AAEA;;;;;CAKC,GACD,SAAS,MAAM,QAAQ;IACtB,IAAI,IAAI;IACR,IAAI,OAAO,SAAS,IAAI;IAExB,kCAAkC;IAClC,IAAI,SAAS,QAAQ,EAAE;QACtB,MAAM,IAAI,MAAM;IACjB;IAEA,uDAAuD;IACvD,8EAA8E;IAC9E,IAAI,gBAAgB,qGAAA,CAAA,UAAM,IAAI,OAAO,KAAK,WAAW,KAAK,YAAY;QACrE,oBAAoB;QACpB,KAAK,IAAI;QACT,KAAK,IAAI;QACT,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,gEAAgE;QAChE,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG;QAC3B,OAAO;IACR;IAEA,OAAO;AACR;AAEA;;;;;;;;CAQC,GACD,SAAS,mBAAmB,IAAI;IAC/B,IAAI,SAAS,MAAM;QAClB,eAAe;QACf,OAAO;IACR,OAAO,IAAI,OAAO,SAAS,UAAU;QACpC,iBAAiB;QACjB,OAAO;IACR,OAAO,IAAI,kBAAkB,OAAO;QACnC,4BAA4B;QAC5B,OAAO;IACR,OAAO,IAAI,OAAO,OAAO;QACxB,eAAe;QACf,OAAO,KAAK,IAAI,IAAI;IACrB,OAAO,IAAI,OAAO,QAAQ,CAAC,OAAO;QACjC,iBAAiB;QACjB,OAAO;IACR,OAAO,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,wBAAwB;QAC3E,sBAAsB;QACtB,OAAO;IACR,OAAO,IAAI,YAAY,MAAM,CAAC,OAAO;QACpC,0BAA0B;QAC1B,OAAO;IACR,OAAO,IAAI,OAAO,KAAK,WAAW,KAAK,YAAY;QAClD,+CAA+C;QAC/C,OAAO,CAAC,6BAA6B,EAAE,KAAK,WAAW,IAAI;IAC5D,OAAO,IAAI,gBAAgB,qGAAA,CAAA,UAAM,EAAE;QAClC,iBAAiB;QACjB,kCAAkC;QAClC,OAAO;IACR,OAAO;QACN,mDAAmD;QACnD,OAAO;IACR;AACD;AAEA;;;;;;;;CAQC,GACD,SAAS,cAAc,QAAQ;IAC9B,MAAM,OAAO,SAAS,IAAI;IAG1B,IAAI,SAAS,MAAM;QAClB,eAAe;QACf,OAAO;IACR,OAAO,IAAI,OAAO,OAAO;QACxB,OAAO,KAAK,IAAI;IACjB,OAAO,IAAI,OAAO,QAAQ,CAAC,OAAO;QACjC,iBAAiB;QACjB,OAAO,KAAK,MAAM;IACnB,OAAO,IAAI,QAAQ,OAAO,KAAK,aAAa,KAAK,YAAY;QAC5D,+CAA+C;QAC/C,IAAI,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,CAAC,MAAM,IAAI,KAAK,MAAM;QAC1E,KAAK,cAAc,IAAI,KAAK,cAAc,IAAI;YAC7C,MAAM;YACN,OAAO,KAAK,aAAa;QAC1B;QACA,OAAO;IACR,OAAO;QACN,iBAAiB;QACjB,OAAO;IACR;AACD;AAEA;;;;;CAKC,GACD,SAAS,cAAc,IAAI,EAAE,QAAQ;IACpC,MAAM,OAAO,SAAS,IAAI;IAG1B,IAAI,SAAS,MAAM;QAClB,eAAe;QACf,KAAK,GAAG;IACT,OAAO,IAAI,OAAO,OAAO;QACxB,KAAK,MAAM,GAAG,IAAI,CAAC;IACpB,OAAO,IAAI,OAAO,QAAQ,CAAC,OAAO;QACjC,iBAAiB;QACjB,KAAK,KAAK,CAAC;QACX,KAAK,GAAG;IACT,OAAO;QACN,iBAAiB;QACjB,KAAK,IAAI,CAAC;IACX;AACD;AAEA,iBAAiB;AACjB,KAAK,OAAO,GAAG,OAAO,OAAO;AAE7B;;;;CAIC,GAED,MAAM,oBAAoB;AAC1B,MAAM,yBAAyB;AAE/B,SAAS,aAAa,IAAI;IACzB,OAAO,GAAG,MAAM;IAChB,IAAI,kBAAkB,IAAI,CAAC,SAAS,SAAS,IAAI;QAChD,MAAM,IAAI,UAAU,GAAG,KAAK,gCAAgC,CAAC;IAC9D;AACD;AAEA,SAAS,cAAc,KAAK;IAC3B,QAAQ,GAAG,OAAO;IAClB,IAAI,uBAAuB,IAAI,CAAC,QAAQ;QACvC,MAAM,IAAI,UAAU,GAAG,MAAM,iCAAiC,CAAC;IAChE;AACD;AAEA;;;;;;;CAOC,GACD,SAAS,KAAK,GAAG,EAAE,IAAI;IACtB,OAAO,KAAK,WAAW;IACvB,IAAK,MAAM,OAAO,IAAK;QACtB,IAAI,IAAI,WAAW,OAAO,MAAM;YAC/B,OAAO;QACR;IACD;IACA,OAAO;AACR;AAEA,MAAM,MAAM,OAAO;AACnB,MAAM;IACL;;;;;EAKC,GACD,aAAc;QACb,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAE/E,IAAI,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC;QAE1B,IAAI,gBAAgB,SAAS;YAC5B,MAAM,aAAa,KAAK,GAAG;YAC3B,MAAM,cAAc,OAAO,IAAI,CAAC;YAEhC,KAAK,MAAM,cAAc,YAAa;gBACrC,KAAK,MAAM,SAAS,UAAU,CAAC,WAAW,CAAE;oBAC3C,IAAI,CAAC,MAAM,CAAC,YAAY;gBACzB;YACD;YAEA;QACD;QAEA,sEAAsE;QACtE,kBAAkB;QAClB,IAAI,QAAQ;aAAa,IAAI,OAAO,SAAS,UAAU;YACtD,MAAM,SAAS,IAAI,CAAC,OAAO,QAAQ,CAAC;YACpC,IAAI,UAAU,MAAM;gBACnB,IAAI,OAAO,WAAW,YAAY;oBACjC,MAAM,IAAI,UAAU;gBACrB;gBAEA,iCAAiC;gBACjC,sEAAsE;gBACtE,MAAM,QAAQ,EAAE;gBAChB,KAAK,MAAM,QAAQ,KAAM;oBACxB,IAAI,OAAO,SAAS,YAAY,OAAO,IAAI,CAAC,OAAO,QAAQ,CAAC,KAAK,YAAY;wBAC5E,MAAM,IAAI,UAAU;oBACrB;oBACA,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC;gBACvB;gBAEA,KAAK,MAAM,QAAQ,MAAO;oBACzB,IAAI,KAAK,MAAM,KAAK,GAAG;wBACtB,MAAM,IAAI,UAAU;oBACrB;oBACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;gBAC7B;YACD,OAAO;gBACN,iCAAiC;gBACjC,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,MAAO;oBACpC,MAAM,QAAQ,IAAI,CAAC,IAAI;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK;gBAClB;YACD;QACD,OAAO;YACN,MAAM,IAAI,UAAU;QACrB;IACD;IAEA;;;;;EAKC,GACD,IAAI,IAAI,EAAE;QACT,OAAO,GAAG,MAAM;QAChB,aAAa;QACb,MAAM,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE;QAC5B,IAAI,QAAQ,WAAW;YACtB,OAAO;QACR;QAEA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5B;IAEA;;;;;;EAMC,GACD,QAAQ,QAAQ,EAAE;QACjB,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAElF,IAAI,QAAQ,WAAW,IAAI;QAC3B,IAAI,IAAI;QACR,MAAO,IAAI,MAAM,MAAM,CAAE;YACxB,IAAI,WAAW,KAAK,CAAC,EAAE;YACvB,MAAM,OAAO,QAAQ,CAAC,EAAE,EAClB,QAAQ,QAAQ,CAAC,EAAE;YAEzB,SAAS,IAAI,CAAC,SAAS,OAAO,MAAM,IAAI;YACxC,QAAQ,WAAW,IAAI;YACvB;QACD;IACD;IAEA;;;;;;EAMC,GACD,IAAI,IAAI,EAAE,KAAK,EAAE;QAChB,OAAO,GAAG,MAAM;QAChB,QAAQ,GAAG,OAAO;QAClB,aAAa;QACb,cAAc;QACd,MAAM,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,YAAY,MAAM,KAAK,GAAG;YAAC;SAAM;IACpD;IAEA;;;;;;EAMC,GACD,OAAO,IAAI,EAAE,KAAK,EAAE;QACnB,OAAO,GAAG,MAAM;QAChB,QAAQ,GAAG,OAAO;QAClB,aAAa;QACb,cAAc;QACd,MAAM,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE;QAC5B,IAAI,QAAQ,WAAW;YACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACrB,OAAO;YACN,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;gBAAC;aAAM;QAC1B;IACD;IAEA;;;;;EAKC,GACD,IAAI,IAAI,EAAE;QACT,OAAO,GAAG,MAAM;QAChB,aAAa;QACb,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE,UAAU;IAClC;IAEA;;;;;EAKC,GACD,OAAO,IAAI,EAAE;QACZ,OAAO,GAAG,MAAM;QAChB,aAAa;QACb,MAAM,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE;QAC5B,IAAI,QAAQ,WAAW;YACtB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;QACtB;IACD;IAEA;;;;EAIC,GACD,MAAM;QACL,OAAO,IAAI,CAAC,IAAI;IACjB;IAEA;;;;EAIC,GACD,OAAO;QACN,OAAO,sBAAsB,IAAI,EAAE;IACpC;IAEA;;;;EAIC,GACD,SAAS;QACR,OAAO,sBAAsB,IAAI,EAAE;IACpC;IAEA;;;;;;EAMC,GACD,CAAC,OAAO,QAAQ,CAAC,GAAG;QACnB,OAAO,sBAAsB,IAAI,EAAE;IACpC;AACD;AACA,QAAQ,SAAS,CAAC,OAAO,GAAG,QAAQ,SAAS,CAAC,OAAO,QAAQ,CAAC;AAE9D,OAAO,cAAc,CAAC,QAAQ,SAAS,EAAE,OAAO,WAAW,EAAE;IAC5D,OAAO;IACP,UAAU;IACV,YAAY;IACZ,cAAc;AACf;AAEA,OAAO,gBAAgB,CAAC,QAAQ,SAAS,EAAE;IAC1C,KAAK;QAAE,YAAY;IAAK;IACxB,SAAS;QAAE,YAAY;IAAK;IAC5B,KAAK;QAAE,YAAY;IAAK;IACxB,QAAQ;QAAE,YAAY;IAAK;IAC3B,KAAK;QAAE,YAAY;IAAK;IACxB,QAAQ;QAAE,YAAY;IAAK;IAC3B,MAAM;QAAE,YAAY;IAAK;IACzB,QAAQ;QAAE,YAAY;IAAK;IAC3B,SAAS;QAAE,YAAY;IAAK;AAC7B;AAEA,SAAS,WAAW,OAAO;IAC1B,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAE/E,MAAM,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI;IAC3C,OAAO,KAAK,GAAG,CAAC,SAAS,QAAQ,SAAU,CAAC;QAC3C,OAAO,EAAE,WAAW;IACrB,IAAI,SAAS,UAAU,SAAU,CAAC;QACjC,OAAO,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IAC7B,IAAI,SAAU,CAAC;QACd,OAAO;YAAC,EAAE,WAAW;YAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;SAAM;IACrD;AACD;AAEA,MAAM,WAAW,OAAO;AAExB,SAAS,sBAAsB,MAAM,EAAE,IAAI;IAC1C,MAAM,WAAW,OAAO,MAAM,CAAC;IAC/B,QAAQ,CAAC,SAAS,GAAG;QACpB;QACA;QACA,OAAO;IACR;IACA,OAAO;AACR;AAEA,MAAM,2BAA2B,OAAO,cAAc,CAAC;IACtD;QACC,qBAAqB;QACrB,IAAI,CAAC,IAAI,IAAI,OAAO,cAAc,CAAC,IAAI,MAAM,0BAA0B;YACtE,MAAM,IAAI,UAAU;QACrB;QAEA,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,MAAM,SAAS,UAAU,MAAM,EACzB,OAAO,UAAU,IAAI,EACrB,QAAQ,UAAU,KAAK;QAE7B,MAAM,SAAS,WAAW,QAAQ;QAClC,MAAM,MAAM,OAAO,MAAM;QACzB,IAAI,SAAS,KAAK;YACjB,OAAO;gBACN,OAAO;gBACP,MAAM;YACP;QACD;QAEA,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,QAAQ;QAE/B,OAAO;YACN,OAAO,MAAM,CAAC,MAAM;YACpB,MAAM;QACP;IACD;AACD,GAAG,OAAO,cAAc,CAAC,OAAO,cAAc,CAAC,EAAE,CAAC,OAAO,QAAQ,CAAC;AAElE,OAAO,cAAc,CAAC,0BAA0B,OAAO,WAAW,EAAE;IACnE,OAAO;IACP,UAAU;IACV,YAAY;IACZ,cAAc;AACf;AAEA;;;;;CAKC,GACD,SAAS,4BAA4B,OAAO;IAC3C,MAAM,MAAM,OAAO,MAAM,CAAC;QAAE,WAAW;IAAK,GAAG,OAAO,CAAC,IAAI;IAE3D,sEAAsE;IACtE,0CAA0C;IAC1C,MAAM,gBAAgB,KAAK,OAAO,CAAC,IAAI,EAAE;IACzC,IAAI,kBAAkB,WAAW;QAChC,GAAG,CAAC,cAAc,GAAG,GAAG,CAAC,cAAc,CAAC,EAAE;IAC3C;IAEA,OAAO;AACR;AAEA;;;;;;CAMC,GACD,SAAS,qBAAqB,GAAG;IAChC,MAAM,UAAU,IAAI;IACpB,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC,KAAM;QACpC,IAAI,kBAAkB,IAAI,CAAC,OAAO;YACjC;QACD;QACA,IAAI,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG;YAC7B,KAAK,MAAM,OAAO,GAAG,CAAC,KAAK,CAAE;gBAC5B,IAAI,uBAAuB,IAAI,CAAC,MAAM;oBACrC;gBACD;gBACA,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,KAAK,WAAW;oBACrC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG;wBAAC;qBAAI;gBAC3B,OAAO;oBACN,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBACzB;YACD;QACD,OAAO,IAAI,CAAC,uBAAuB,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG;YACnD,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG;gBAAC,GAAG,CAAC,KAAK;aAAC;QACjC;IACD;IACA,OAAO;AACR;AAEA,MAAM,cAAc,OAAO;AAE3B,uEAAuE;AACvE,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,YAAY;AAEtC;;;;;;CAMC,GACD,MAAM;IACL,aAAc;QACb,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAC/E,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;QAEhF,KAAK,IAAI,CAAC,IAAI,EAAE,MAAM;QAEtB,MAAM,SAAS,KAAK,MAAM,IAAI;QAC9B,MAAM,UAAU,IAAI,QAAQ,KAAK,OAAO;QAExC,IAAI,QAAQ,QAAQ,CAAC,QAAQ,GAAG,CAAC,iBAAiB;YACjD,MAAM,cAAc,mBAAmB;YACvC,IAAI,aAAa;gBAChB,QAAQ,MAAM,CAAC,gBAAgB;YAChC;QACD;QAEA,IAAI,CAAC,YAAY,GAAG;YACnB,KAAK,KAAK,GAAG;YACb;YACA,YAAY,KAAK,UAAU,IAAI,YAAY,CAAC,OAAO;YACnD;YACA,SAAS,KAAK,OAAO;QACtB;IACD;IAEA,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI;IACjC;IAEA,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;IAChC;IAEA;;EAEC,GACD,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;IACtE;IAEA,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG;IACpC;IAEA,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU;IACpC;IAEA,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO;IACjC;IAEA;;;;EAIC,GACD,QAAQ;QACP,OAAO,IAAI,SAAS,MAAM,IAAI,GAAG;YAChC,KAAK,IAAI,CAAC,GAAG;YACb,QAAQ,IAAI,CAAC,MAAM;YACnB,YAAY,IAAI,CAAC,UAAU;YAC3B,SAAS,IAAI,CAAC,OAAO;YACrB,IAAI,IAAI,CAAC,EAAE;YACX,YAAY,IAAI,CAAC,UAAU;QAC5B;IACD;AACD;AAEA,KAAK,KAAK,CAAC,SAAS,SAAS;AAE7B,OAAO,gBAAgB,CAAC,SAAS,SAAS,EAAE;IAC3C,KAAK;QAAE,YAAY;IAAK;IACxB,QAAQ;QAAE,YAAY;IAAK;IAC3B,IAAI;QAAE,YAAY;IAAK;IACvB,YAAY;QAAE,YAAY;IAAK;IAC/B,YAAY;QAAE,YAAY;IAAK;IAC/B,SAAS;QAAE,YAAY;IAAK;IAC5B,OAAO;QAAE,YAAY;IAAK;AAC3B;AAEA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,OAAO,WAAW,EAAE;IAC7D,OAAO;IACP,UAAU;IACV,YAAY;IACZ,cAAc;AACf;AAEA,MAAM,cAAc,OAAO;AAC3B,MAAM,MAAM,+FAAA,CAAA,UAAG,CAAC,GAAG,IAAI,qJAAA,CAAA,UAAS,CAAC,GAAG;AAEpC,0EAA0E;AAC1E,MAAM,YAAY,+FAAA,CAAA,UAAG,CAAC,KAAK;AAC3B,MAAM,aAAa,+FAAA,CAAA,UAAG,CAAC,MAAM;AAE7B;;;;;CAKC,GACD,SAAS,SAAS,MAAM;IACvB;;;;CAIA,GACA,IAAI,4BAA4B,IAAI,CAAC,SAAS;QAC7C,SAAS,IAAI,IAAI,QAAQ,QAAQ;IAClC;IAEA,oDAAoD;IACpD,OAAO,UAAU;AAClB;AAEA,MAAM,6BAA6B,aAAa,qGAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,SAAS;AAEzE;;;;;CAKC,GACD,SAAS,UAAU,KAAK;IACvB,OAAO,OAAO,UAAU,YAAY,OAAO,KAAK,CAAC,YAAY,KAAK;AACnE;AAEA,SAAS,cAAc,MAAM;IAC5B,MAAM,QAAQ,UAAU,OAAO,WAAW,YAAY,OAAO,cAAc,CAAC;IAC5E,OAAO,CAAC,CAAC,CAAC,SAAS,MAAM,WAAW,CAAC,IAAI,KAAK,aAAa;AAC5D;AAEA;;;;;;CAMC,GACD,MAAM;IACL,YAAY,KAAK,CAAE;QAClB,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;QAEhF,IAAI;QAEJ,kBAAkB;QAClB,IAAI,CAAC,UAAU,QAAQ;YACtB,IAAI,SAAS,MAAM,IAAI,EAAE;gBACxB,wEAAwE;gBACxE,wEAAwE;gBACxE,0BAA0B;gBAC1B,YAAY,SAAS,MAAM,IAAI;YAChC,OAAO;gBACN,sDAAsD;gBACtD,YAAY,SAAS,GAAG,OAAO;YAChC;YACA,QAAQ,CAAC;QACV,OAAO;YACN,YAAY,SAAS,MAAM,GAAG;QAC/B;QAEA,IAAI,SAAS,KAAK,MAAM,IAAI,MAAM,MAAM,IAAI;QAC5C,SAAS,OAAO,WAAW;QAE3B,IAAI,CAAC,KAAK,IAAI,IAAI,QAAQ,UAAU,UAAU,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,SAAS,WAAW,MAAM,GAAG;YAC9G,MAAM,IAAI,UAAU;QACrB;QAEA,IAAI,YAAY,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,GAAG,UAAU,UAAU,MAAM,IAAI,KAAK,OAAO,MAAM,SAAS;QAEzG,KAAK,IAAI,CAAC,IAAI,EAAE,WAAW;YAC1B,SAAS,KAAK,OAAO,IAAI,MAAM,OAAO,IAAI;YAC1C,MAAM,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI;QAClC;QAEA,MAAM,UAAU,IAAI,QAAQ,KAAK,OAAO,IAAI,MAAM,OAAO,IAAI,CAAC;QAE9D,IAAI,aAAa,QAAQ,CAAC,QAAQ,GAAG,CAAC,iBAAiB;YACtD,MAAM,cAAc,mBAAmB;YACvC,IAAI,aAAa;gBAChB,QAAQ,MAAM,CAAC,gBAAgB;YAChC;QACD;QAEA,IAAI,SAAS,UAAU,SAAS,MAAM,MAAM,GAAG;QAC/C,IAAI,YAAY,MAAM,SAAS,KAAK,MAAM;QAE1C,IAAI,UAAU,QAAQ,CAAC,cAAc,SAAS;YAC7C,MAAM,IAAI,UAAU;QACrB;QAEA,IAAI,CAAC,YAAY,GAAG;YACnB;YACA,UAAU,KAAK,QAAQ,IAAI,MAAM,QAAQ,IAAI;YAC7C;YACA;YACA;QACD;QAEA,0BAA0B;QAC1B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,KAAK,YAAY,KAAK,MAAM,GAAG,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,GAAG;QACpG,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,MAAM,QAAQ,KAAK,YAAY,MAAM,QAAQ,GAAG;QAC9G,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO,IAAI,MAAM,OAAO,IAAI;QAChD,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI,MAAM,KAAK;IACvC;IAEA,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;IAChC;IAEA,IAAI,MAAM;QACT,OAAO,WAAW,IAAI,CAAC,YAAY,CAAC,SAAS;IAC9C;IAEA,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO;IACjC;IAEA,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;IAClC;IAEA,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;IAChC;IAEA;;;;EAIC,GACD,QAAQ;QACP,OAAO,IAAI,QAAQ,IAAI;IACxB;AACD;AAEA,KAAK,KAAK,CAAC,QAAQ,SAAS;AAE5B,OAAO,cAAc,CAAC,QAAQ,SAAS,EAAE,OAAO,WAAW,EAAE;IAC5D,OAAO;IACP,UAAU;IACV,YAAY;IACZ,cAAc;AACf;AAEA,OAAO,gBAAgB,CAAC,QAAQ,SAAS,EAAE;IAC1C,QAAQ;QAAE,YAAY;IAAK;IAC3B,KAAK;QAAE,YAAY;IAAK;IACxB,SAAS;QAAE,YAAY;IAAK;IAC5B,UAAU;QAAE,YAAY;IAAK;IAC7B,OAAO;QAAE,YAAY;IAAK;IAC1B,QAAQ;QAAE,YAAY;IAAK;AAC5B;AAEA;;;;;CAKC,GACD,SAAS,sBAAsB,OAAO;IACrC,MAAM,YAAY,OAAO,CAAC,YAAY,CAAC,SAAS;IAChD,MAAM,UAAU,IAAI,QAAQ,OAAO,CAAC,YAAY,CAAC,OAAO;IAExD,iBAAiB;IACjB,IAAI,CAAC,QAAQ,GAAG,CAAC,WAAW;QAC3B,QAAQ,GAAG,CAAC,UAAU;IACvB;IAEA,cAAc;IACd,IAAI,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,QAAQ,EAAE;QAC/C,MAAM,IAAI,UAAU;IACrB;IAEA,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,QAAQ,GAAG;QAC1C,MAAM,IAAI,UAAU;IACrB;IAEA,IAAI,QAAQ,MAAM,IAAI,QAAQ,IAAI,YAAY,qGAAA,CAAA,UAAM,CAAC,QAAQ,IAAI,CAAC,4BAA4B;QAC7F,MAAM,IAAI,MAAM;IACjB;IAEA,4CAA4C;IAC5C,IAAI,qBAAqB;IACzB,IAAI,QAAQ,IAAI,IAAI,QAAQ,gBAAgB,IAAI,CAAC,QAAQ,MAAM,GAAG;QACjE,qBAAqB;IACtB;IACA,IAAI,QAAQ,IAAI,IAAI,MAAM;QACzB,MAAM,aAAa,cAAc;QACjC,IAAI,OAAO,eAAe,UAAU;YACnC,qBAAqB,OAAO;QAC7B;IACD;IACA,IAAI,oBAAoB;QACvB,QAAQ,GAAG,CAAC,kBAAkB;IAC/B;IAEA,wCAAwC;IACxC,IAAI,CAAC,QAAQ,GAAG,CAAC,eAAe;QAC/B,QAAQ,GAAG,CAAC,cAAc;IAC3B;IAEA,wCAAwC;IACxC,IAAI,QAAQ,QAAQ,IAAI,CAAC,QAAQ,GAAG,CAAC,oBAAoB;QACxD,QAAQ,GAAG,CAAC,mBAAmB;IAChC;IAEA,IAAI,QAAQ,QAAQ,KAAK;IACzB,IAAI,OAAO,UAAU,YAAY;QAChC,QAAQ,MAAM;IACf;IAEA,8BAA8B;IAC9B,yCAAyC;IAEzC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,WAAW;QACnC,QAAQ,QAAQ,MAAM;QACtB,SAAS,4BAA4B;QACrC;IACD;AACD;AAEA;;;;CAIC,GAED;;;;;CAKC,GACD,SAAS,WAAW,OAAO;IACzB,MAAM,IAAI,CAAC,IAAI,EAAE;IAEjB,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,OAAO,GAAG;IAEf,0DAA0D;IAC1D,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;AAChD;AAEA,WAAW,SAAS,GAAG,OAAO,MAAM,CAAC,MAAM,SAAS;AACpD,WAAW,SAAS,CAAC,WAAW,GAAG;AACnC,WAAW,SAAS,CAAC,IAAI,GAAG;AAE5B,MAAM,QAAQ,+FAAA,CAAA,UAAG,CAAC,GAAG,IAAI,qJAAA,CAAA,UAAS,CAAC,GAAG;AAEtC,iFAAiF;AACjF,MAAM,gBAAgB,qGAAA,CAAA,UAAM,CAAC,WAAW;AAExC,MAAM,sBAAsB,SAAS,oBAAoB,WAAW,EAAE,QAAQ;IAC7E,MAAM,OAAO,IAAI,MAAM,UAAU,QAAQ;IACzC,MAAM,OAAO,IAAI,MAAM,aAAa,QAAQ;IAE5C,OAAO,SAAS,QAAQ,IAAI,CAAC,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,EAAE,KAAK,OAAO,KAAK,QAAQ,CAAC;AACtF;AAEA;;;;;;CAMC,GACD,MAAM,iBAAiB,SAAS,eAAe,WAAW,EAAE,QAAQ;IACnE,MAAM,OAAO,IAAI,MAAM,UAAU,QAAQ;IACzC,MAAM,OAAO,IAAI,MAAM,aAAa,QAAQ;IAE5C,OAAO,SAAS;AACjB;AAEA;;;;;;CAMC,GACD,SAAS,MAAM,GAAG,EAAE,IAAI;IAEvB,uBAAuB;IACvB,IAAI,CAAC,MAAM,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM;IACjB;IAEA,KAAK,OAAO,GAAG,MAAM,OAAO;IAE5B,+BAA+B;IAC/B,OAAO,IAAI,MAAM,OAAO,CAAC,SAAU,OAAO,EAAE,MAAM;QACjD,uBAAuB;QACvB,MAAM,UAAU,IAAI,QAAQ,KAAK;QACjC,MAAM,UAAU,sBAAsB;QAEtC,MAAM,OAAO,CAAC,QAAQ,QAAQ,KAAK,WAAW,mGAAA,CAAA,UAAK,GAAG,iGAAA,CAAA,UAAI,EAAE,OAAO;QACnE,MAAM,SAAS,QAAQ,MAAM;QAE7B,IAAI,WAAW;QAEf,MAAM,QAAQ,SAAS;YACtB,IAAI,QAAQ,IAAI,WAAW;YAC3B,OAAO;YACP,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,YAAY,qGAAA,CAAA,UAAM,CAAC,QAAQ,EAAE;gBAC5D,cAAc,QAAQ,IAAI,EAAE;YAC7B;YACA,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,EAAE;YACjC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS;QAC7B;QAEA,IAAI,UAAU,OAAO,OAAO,EAAE;YAC7B;YACA;QACD;QAEA,MAAM,mBAAmB,SAAS;YACjC;YACA;QACD;QAEA,eAAe;QACf,MAAM,MAAM,KAAK;QACjB,IAAI;QAEJ,IAAI,QAAQ;YACX,OAAO,gBAAgB,CAAC,SAAS;QAClC;QAEA,SAAS;YACR,IAAI,KAAK;YACT,IAAI,QAAQ,OAAO,mBAAmB,CAAC,SAAS;YAChD,aAAa;QACd;QAEA,IAAI,QAAQ,OAAO,EAAE;YACpB,IAAI,IAAI,CAAC,UAAU,SAAU,MAAM;gBAClC,aAAa,WAAW;oBACvB,OAAO,IAAI,WAAW,CAAC,oBAAoB,EAAE,QAAQ,GAAG,EAAE,EAAE;oBAC5D;gBACD,GAAG,QAAQ,OAAO;YACnB;QACD;QAEA,IAAI,EAAE,CAAC,SAAS,SAAU,GAAG;YAC5B,OAAO,IAAI,WAAW,CAAC,WAAW,EAAE,QAAQ,GAAG,CAAC,iBAAiB,EAAE,IAAI,OAAO,EAAE,EAAE,UAAU;YAE5F,IAAI,YAAY,SAAS,IAAI,EAAE;gBAC9B,cAAc,SAAS,IAAI,EAAE;YAC9B;YAEA;QACD;QAEA,oCAAoC,KAAK,SAAU,GAAG;YACrD,IAAI,UAAU,OAAO,OAAO,EAAE;gBAC7B;YACD;YAEA,IAAI,YAAY,SAAS,IAAI,EAAE;gBAC9B,cAAc,SAAS,IAAI,EAAE;YAC9B;QACD;QAEA,qBAAqB,GACrB,IAAI,SAAS,QAAQ,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI;YAChD,2FAA2F;YAC3F,qEAAqE;YACrE,IAAI,EAAE,CAAC,UAAU,SAAU,CAAC;gBAC3B,EAAE,WAAW,CAAC,SAAS,SAAU,QAAQ;oBACxC,4DAA4D;oBAC5D,MAAM,kBAAkB,EAAE,aAAa,CAAC,UAAU;oBAElD,8EAA8E;oBAC9E,IAAI,YAAY,mBAAmB,CAAC,YAAY,CAAC,CAAC,UAAU,OAAO,OAAO,GAAG;wBAC5E,MAAM,MAAM,IAAI,MAAM;wBACtB,IAAI,IAAI,GAAG;wBACX,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS;oBAC7B;gBACD;YACD;QACD;QAEA,IAAI,EAAE,CAAC,YAAY,SAAU,GAAG;YAC/B,aAAa;YAEb,MAAM,UAAU,qBAAqB,IAAI,OAAO;YAEhD,oBAAoB;YACpB,IAAI,MAAM,UAAU,CAAC,IAAI,UAAU,GAAG;gBACrC,sBAAsB;gBACtB,MAAM,WAAW,QAAQ,GAAG,CAAC;gBAE7B,sBAAsB;gBACtB,IAAI,cAAc;gBAClB,IAAI;oBACH,cAAc,aAAa,OAAO,OAAO,IAAI,MAAM,UAAU,QAAQ,GAAG,EAAE,QAAQ;gBACnF,EAAE,OAAO,KAAK;oBACb,yDAAyD;oBACzD,+CAA+C;oBAC/C,mDAAmD;oBACnD,IAAI,QAAQ,QAAQ,KAAK,UAAU;wBAClC,OAAO,IAAI,WAAW,CAAC,qDAAqD,EAAE,UAAU,EAAE;wBAC1F;wBACA;oBACD;gBACD;gBAEA,sBAAsB;gBACtB,OAAQ,QAAQ,QAAQ;oBACvB,KAAK;wBACJ,OAAO,IAAI,WAAW,CAAC,uEAAuE,EAAE,QAAQ,GAAG,EAAE,EAAE;wBAC/G;wBACA;oBACD,KAAK;wBACJ,+HAA+H;wBAC/H,IAAI,gBAAgB,MAAM;4BACzB,0BAA0B;4BAC1B,IAAI;gCACH,QAAQ,GAAG,CAAC,YAAY;4BACzB,EAAE,OAAO,KAAK;gCACb,kHAAkH;gCAClH,OAAO;4BACR;wBACD;wBACA;oBACD,KAAK;wBACJ,6BAA6B;wBAC7B,IAAI,gBAAgB,MAAM;4BACzB;wBACD;wBAEA,6BAA6B;wBAC7B,IAAI,QAAQ,OAAO,IAAI,QAAQ,MAAM,EAAE;4BACtC,OAAO,IAAI,WAAW,CAAC,6BAA6B,EAAE,QAAQ,GAAG,EAAE,EAAE;4BACrE;4BACA;wBACD;wBAEA,iDAAiD;wBACjD,+BAA+B;wBAC/B,MAAM,cAAc;4BACnB,SAAS,IAAI,QAAQ,QAAQ,OAAO;4BACpC,QAAQ,QAAQ,MAAM;4BACtB,SAAS,QAAQ,OAAO,GAAG;4BAC3B,OAAO,QAAQ,KAAK;4BACpB,UAAU,QAAQ,QAAQ;4BAC1B,QAAQ,QAAQ,MAAM;4BACtB,MAAM,QAAQ,IAAI;4BAClB,QAAQ,QAAQ,MAAM;4BACtB,SAAS,QAAQ,OAAO;4BACxB,MAAM,QAAQ,IAAI;wBACnB;wBAEA,IAAI,CAAC,oBAAoB,QAAQ,GAAG,EAAE,gBAAgB,CAAC,eAAe,QAAQ,GAAG,EAAE,cAAc;4BAChG,KAAK,MAAM,QAAQ;gCAAC;gCAAiB;gCAAoB;gCAAU;6BAAU,CAAE;gCAC9E,YAAY,OAAO,CAAC,MAAM,CAAC;4BAC5B;wBACD;wBAEA,6BAA6B;wBAC7B,IAAI,IAAI,UAAU,KAAK,OAAO,QAAQ,IAAI,IAAI,cAAc,aAAa,MAAM;4BAC9E,OAAO,IAAI,WAAW,4DAA4D;4BAClF;4BACA;wBACD;wBAEA,8BAA8B;wBAC9B,IAAI,IAAI,UAAU,KAAK,OAAO,CAAC,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,GAAG,KAAK,QAAQ,MAAM,KAAK,QAAQ;4BAC9G,YAAY,MAAM,GAAG;4BACrB,YAAY,IAAI,GAAG;4BACnB,YAAY,OAAO,CAAC,MAAM,CAAC;wBAC5B;wBAEA,8BAA8B;wBAC9B,QAAQ,MAAM,IAAI,QAAQ,aAAa;wBACvC;wBACA;gBACF;YACD;YAEA,mBAAmB;YACnB,IAAI,IAAI,CAAC,OAAO;gBACf,IAAI,QAAQ,OAAO,mBAAmB,CAAC,SAAS;YACjD;YACA,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI;YAExB,MAAM,mBAAmB;gBACxB,KAAK,QAAQ,GAAG;gBAChB,QAAQ,IAAI,UAAU;gBACtB,YAAY,IAAI,aAAa;gBAC7B,SAAS;gBACT,MAAM,QAAQ,IAAI;gBAClB,SAAS,QAAQ,OAAO;gBACxB,SAAS,QAAQ,OAAO;YACzB;YAEA,mCAAmC;YACnC,MAAM,UAAU,QAAQ,GAAG,CAAC;YAE5B,2DAA2D;YAE3D,uDAAuD;YACvD,qCAAqC;YACrC,kBAAkB;YAClB,gCAAgC;YAChC,+BAA+B;YAC/B,yCAAyC;YACzC,IAAI,CAAC,QAAQ,QAAQ,IAAI,QAAQ,MAAM,KAAK,UAAU,YAAY,QAAQ,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,KAAK;gBAC3H,WAAW,IAAI,SAAS,MAAM;gBAC9B,QAAQ;gBACR;YACD;YAEA,eAAe;YACf,qEAAqE;YACrE,kEAAkE;YAClE,sBAAsB;YACtB,+CAA+C;YAC/C,MAAM,cAAc;gBACnB,OAAO,iGAAA,CAAA,UAAI,CAAC,YAAY;gBACxB,aAAa,iGAAA,CAAA,UAAI,CAAC,YAAY;YAC/B;YAEA,WAAW;YACX,IAAI,WAAW,UAAU,WAAW,UAAU;gBAC7C,OAAO,KAAK,IAAI,CAAC,iGAAA,CAAA,UAAI,CAAC,YAAY,CAAC;gBACnC,WAAW,IAAI,SAAS,MAAM;gBAC9B,QAAQ;gBACR;YACD;YAEA,cAAc;YACd,IAAI,WAAW,aAAa,WAAW,aAAa;gBACnD,4DAA4D;gBAC5D,wCAAwC;gBACxC,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI;gBACzB,IAAI,IAAI,CAAC,QAAQ,SAAU,KAAK;oBAC/B,kDAAkD;oBAClD,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;wBAC/B,OAAO,KAAK,IAAI,CAAC,iGAAA,CAAA,UAAI,CAAC,aAAa;oBACpC,OAAO;wBACN,OAAO,KAAK,IAAI,CAAC,iGAAA,CAAA,UAAI,CAAC,gBAAgB;oBACvC;oBACA,WAAW,IAAI,SAAS,MAAM;oBAC9B,QAAQ;gBACT;gBACA,IAAI,EAAE,CAAC,OAAO;oBACb,4FAA4F;oBAC5F,IAAI,CAAC,UAAU;wBACd,WAAW,IAAI,SAAS,MAAM;wBAC9B,QAAQ;oBACT;gBACD;gBACA;YACD;YAEA,SAAS;YACT,IAAI,WAAW,QAAQ,OAAO,iGAAA,CAAA,UAAI,CAAC,sBAAsB,KAAK,YAAY;gBACzE,OAAO,KAAK,IAAI,CAAC,iGAAA,CAAA,UAAI,CAAC,sBAAsB;gBAC5C,WAAW,IAAI,SAAS,MAAM;gBAC9B,QAAQ;gBACR;YACD;YAEA,gCAAgC;YAChC,WAAW,IAAI,SAAS,MAAM;YAC9B,QAAQ;QACT;QAEA,cAAc,KAAK;IACpB;AACD;AACA,SAAS,oCAAoC,OAAO,EAAE,aAAa;IAClE,IAAI;IAEJ,QAAQ,EAAE,CAAC,UAAU,SAAU,CAAC;QAC/B,SAAS;IACV;IAEA,QAAQ,EAAE,CAAC,YAAY,SAAU,QAAQ;QACxC,MAAM,UAAU,SAAS,OAAO;QAEhC,IAAI,OAAO,CAAC,oBAAoB,KAAK,aAAa,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC7E,SAAS,IAAI,CAAC,SAAS,SAAU,QAAQ;gBACxC,uDAAuD;gBACvD,sDAAsD;gBACtD,wCAAwC;gBACxC,4DAA4D;gBAC5D,MAAM,kBAAkB,UAAU,OAAO,aAAa,CAAC,UAAU;gBAEjE,IAAI,mBAAmB,CAAC,UAAU;oBACjC,MAAM,MAAM,IAAI,MAAM;oBACtB,IAAI,IAAI,GAAG;oBACX,cAAc;gBACf;YACD;QACD;IACD;AACD;AAEA,SAAS,cAAc,MAAM,EAAE,GAAG;IACjC,IAAI,OAAO,OAAO,EAAE;QACnB,OAAO,OAAO,CAAC;IAChB,OAAO;QACN,WAAW;QACX,OAAO,IAAI,CAAC,SAAS;QACrB,OAAO,GAAG;IACX;AACD;AAEA;;;;;CAKC,GACD,MAAM,UAAU,GAAG,SAAU,IAAI;IAChC,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS;AACjF;AAEA,iBAAiB;AACjB,MAAM,OAAO,GAAG,OAAO,OAAO;uCAEf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/isomorphic-fetch/index.mjs"], "sourcesContent": ["import realFetch from 'node-fetch';\nfunction fetch(url, options) {\n    if (/^\\/\\//.test(url)) {\n        url = 'https:' + url;\n    }\n    return realFetch.call(this, url, options);\n}\n\nif (!global.fetch) {\n    global.fetch = fetch;\n    global.Response = realFetch.Response;\n    global.Headers = realFetch.Headers;\n    global.Request = realFetch.Request;\n}\n"], "names": [], "mappings": ";AAAA;;AACA,SAAS,MAAM,GAAG,EAAE,OAAO;IACvB,IAAI,QAAQ,IAAI,CAAC,MAAM;QACnB,MAAM,WAAW;IACrB;IACA,OAAO,8IAAA,CAAA,UAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK;AACrC;AAEA,IAAI,CAAC,OAAO,KAAK,EAAE;IACf,OAAO,KAAK,GAAG;IACf,OAAO,QAAQ,GAAG,8IAAA,CAAA,UAAS,CAAC,QAAQ;IACpC,OAAO,OAAO,GAAG,8IAAA,CAAA,UAAS,CAAC,OAAO;IAClC,OAAO,OAAO,GAAG,8IAAA,CAAA,UAAS,CAAC,OAAO;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/ua-parser-js/src/ua-parser.js"], "sourcesContent": ["/////////////////////////////////////////////////////////////////////////////////\n/* UAParser.js v1.0.40\n   Copyright © 2012-2024 F<PERSON><PERSON> <<EMAIL>>\n   MIT License *//*\n   Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data.\n   Supports browser & node.js environment. \n   Demo   : https://faisalman.github.io/ua-parser-js\n   Source : https://github.com/faisalman/ua-parser-js */\n/////////////////////////////////////////////////////////////////////////////////\n\n(function (window, undefined) {\n\n    'use strict';\n\n    //////////////\n    // Constants\n    /////////////\n\n\n    var LIBVERSION  = '1.0.40',\n        EMPTY       = '',\n        UNKNOWN     = '?',\n        FUNC_TYPE   = 'function',\n        UNDEF_TYPE  = 'undefined',\n        OBJ_TYPE    = 'object',\n        STR_TYPE    = 'string',\n        MAJOR       = 'major',\n        MODEL       = 'model',\n        NAME        = 'name',\n        TYPE        = 'type',\n        VENDOR      = 'vendor',\n        VERSION     = 'version',\n        ARCHITECTURE= 'architecture',\n        CONSOLE     = 'console',\n        MOBILE      = 'mobile',\n        TABLET      = 'tablet',\n        SMARTTV     = 'smarttv',\n        WEARABLE    = 'wearable',\n        EMBEDDED    = 'embedded',\n        UA_MAX_LENGTH = 500;\n\n    var AMAZON  = 'Amazon',\n        APPLE   = 'Apple',\n        ASUS    = 'ASUS',\n        BLACKBERRY = 'BlackBerry',\n        BROWSER = 'Browser',\n        CHROME  = 'Chrome',\n        EDGE    = 'Edge',\n        FIREFOX = 'Firefox',\n        GOOGLE  = 'Google',\n        HUAWEI  = 'Huawei',\n        LG      = 'LG',\n        MICROSOFT = 'Microsoft',\n        MOTOROLA  = 'Motorola',\n        OPERA   = 'Opera',\n        SAMSUNG = 'Samsung',\n        SHARP   = 'Sharp',\n        SONY    = 'Sony',\n        XIAOMI  = 'Xiaomi',\n        ZEBRA   = 'Zebra',\n        FACEBOOK    = 'Facebook',\n        CHROMIUM_OS = 'Chromium OS',\n        MAC_OS  = 'Mac OS',\n        SUFFIX_BROWSER = ' Browser';\n\n    ///////////\n    // Helper\n    //////////\n\n    var extend = function (regexes, extensions) {\n            var mergedRegexes = {};\n            for (var i in regexes) {\n                if (extensions[i] && extensions[i].length % 2 === 0) {\n                    mergedRegexes[i] = extensions[i].concat(regexes[i]);\n                } else {\n                    mergedRegexes[i] = regexes[i];\n                }\n            }\n            return mergedRegexes;\n        },\n        enumerize = function (arr) {\n            var enums = {};\n            for (var i=0; i<arr.length; i++) {\n                enums[arr[i].toUpperCase()] = arr[i];\n            }\n            return enums;\n        },\n        has = function (str1, str2) {\n            return typeof str1 === STR_TYPE ? lowerize(str2).indexOf(lowerize(str1)) !== -1 : false;\n        },\n        lowerize = function (str) {\n            return str.toLowerCase();\n        },\n        majorize = function (version) {\n            return typeof(version) === STR_TYPE ? version.replace(/[^\\d\\.]/g, EMPTY).split('.')[0] : undefined;\n        },\n        trim = function (str, len) {\n            if (typeof(str) === STR_TYPE) {\n                str = str.replace(/^\\s\\s*/, EMPTY);\n                return typeof(len) === UNDEF_TYPE ? str : str.substring(0, UA_MAX_LENGTH);\n            }\n    };\n\n    ///////////////\n    // Map helper\n    //////////////\n\n    var rgxMapper = function (ua, arrays) {\n\n            var i = 0, j, k, p, q, matches, match;\n\n            // loop through all regexes maps\n            while (i < arrays.length && !matches) {\n\n                var regex = arrays[i],       // even sequence (0,2,4,..)\n                    props = arrays[i + 1];   // odd sequence (1,3,5,..)\n                j = k = 0;\n\n                // try matching uastring with regexes\n                while (j < regex.length && !matches) {\n\n                    if (!regex[j]) { break; }\n                    matches = regex[j++].exec(ua);\n\n                    if (!!matches) {\n                        for (p = 0; p < props.length; p++) {\n                            match = matches[++k];\n                            q = props[p];\n                            // check if given property is actually array\n                            if (typeof q === OBJ_TYPE && q.length > 0) {\n                                if (q.length === 2) {\n                                    if (typeof q[1] == FUNC_TYPE) {\n                                        // assign modified match\n                                        this[q[0]] = q[1].call(this, match);\n                                    } else {\n                                        // assign given value, ignore regex match\n                                        this[q[0]] = q[1];\n                                    }\n                                } else if (q.length === 3) {\n                                    // check whether function or regex\n                                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {\n                                        // call function (usually string mapper)\n                                        this[q[0]] = match ? q[1].call(this, match, q[2]) : undefined;\n                                    } else {\n                                        // sanitize match using given regex\n                                        this[q[0]] = match ? match.replace(q[1], q[2]) : undefined;\n                                    }\n                                } else if (q.length === 4) {\n                                        this[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined;\n                                }\n                            } else {\n                                this[q] = match ? match : undefined;\n                            }\n                        }\n                    }\n                }\n                i += 2;\n            }\n        },\n\n        strMapper = function (str, map) {\n\n            for (var i in map) {\n                // check if current value is array\n                if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {\n                    for (var j = 0; j < map[i].length; j++) {\n                        if (has(map[i][j], str)) {\n                            return (i === UNKNOWN) ? undefined : i;\n                        }\n                    }\n                } else if (has(map[i], str)) {\n                    return (i === UNKNOWN) ? undefined : i;\n                }\n            }\n            return map.hasOwnProperty('*') ? map['*'] : str;\n    };\n\n    ///////////////\n    // String map\n    //////////////\n\n    // Safari < 3.0\n    var oldSafariMap = {\n            '1.0'   : '/8',\n            '1.2'   : '/1',\n            '1.3'   : '/3',\n            '2.0'   : '/412',\n            '2.0.2' : '/416',\n            '2.0.3' : '/417',\n            '2.0.4' : '/419',\n            '?'     : '/'\n        },\n        windowsVersionMap = {\n            'ME'        : '4.90',\n            'NT 3.11'   : 'NT3.51',\n            'NT 4.0'    : 'NT4.0',\n            '2000'      : 'NT 5.0',\n            'XP'        : ['NT 5.1', 'NT 5.2'],\n            'Vista'     : 'NT 6.0',\n            '7'         : 'NT 6.1',\n            '8'         : 'NT 6.2',\n            '8.1'       : 'NT 6.3',\n            '10'        : ['NT 6.4', 'NT 10.0'],\n            'RT'        : 'ARM'\n    };\n\n    //////////////\n    // Regex map\n    /////////////\n\n    var regexes = {\n\n        browser : [[\n\n            /\\b(?:crmo|crios)\\/([\\w\\.]+)/i                                      // Chrome for Android/iOS\n            ], [VERSION, [NAME, 'Chrome']], [\n            /edg(?:e|ios|a)?\\/([\\w\\.]+)/i                                       // Microsoft Edge\n            ], [VERSION, [NAME, 'Edge']], [\n\n            // Presto based\n            /(opera mini)\\/([-\\w\\.]+)/i,                                        // Opera Mini\n            /(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,                 // Opera Mobi/Tablet\n            /(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i                           // Opera\n            ], [NAME, VERSION], [\n            /opios[\\/ ]+([\\w\\.]+)/i                                             // Opera mini on iphone >= 8.0\n            ], [VERSION, [NAME, OPERA+' Mini']], [\n            /\\bop(?:rg)?x\\/([\\w\\.]+)/i                                          // Opera GX\n            ], [VERSION, [NAME, OPERA+' GX']], [\n            /\\bopr\\/([\\w\\.]+)/i                                                 // Opera Webkit\n            ], [VERSION, [NAME, OPERA]], [\n\n            // Mixed\n            /\\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\\/ ]?([\\w\\.]+)/i            // Baidu\n            ], [VERSION, [NAME, 'Baidu']], [\n            /\\b(?:mxbrowser|mxios|myie2)\\/?([-\\w\\.]*)\\b/i                       // Maxthon\n            ], [VERSION, [NAME, 'Maxthon']], [\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\\/ ]?([\\w\\.]*)/i,      \n                                                                                // Lunascape/Maxthon/Netfront/Jasmine/Blazer/Sleipnir\n            // Trident based\n            /(avant|iemobile|slim(?:browser|boat|jet))[\\/ ]?([\\d\\.]*)/i,        // Avant/IEMobile/SlimBrowser/SlimBoat/Slimjet\n            /(?:ms|\\()(ie) ([\\w\\.]+)/i,                                         // Internet Explorer\n\n            // Blink/Webkit/KHTML based                                         // Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS/Bowser/QupZilla/Falkon\n            /(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\\/([-\\w\\.]+)/i,\n                                                                                // Rekonq/Puffin/Brave/Whale/QQBrowserLite/QQ//Vivaldi/DuckDuckGo/Klar/Helio/Dragon\n            /(heytap|ovi|115)browser\\/([\\d\\.]+)/i,                              // HeyTap/Ovi/115\n            /(weibo)__([\\d\\.]+)/i                                               // Weibo\n            ], [NAME, VERSION], [\n            /quark(?:pc)?\\/([-\\w\\.]+)/i                                         // Quark\n            ], [VERSION, [NAME, 'Quark']], [\n            /\\bddg\\/([\\w\\.]+)/i                                                 // DuckDuckGo\n            ], [VERSION, [NAME, 'DuckDuckGo']], [\n            /(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i                 // UCBrowser\n            ], [VERSION, [NAME, 'UC'+BROWSER]], [\n            /microm.+\\bqbcore\\/([\\w\\.]+)/i,                                     // WeChat Desktop for Windows Built-in Browser\n            /\\bqbcore\\/([\\w\\.]+).+microm/i,\n            /micromessenger\\/([\\w\\.]+)/i                                        // WeChat\n            ], [VERSION, [NAME, 'WeChat']], [\n            /konqueror\\/([\\w\\.]+)/i                                             // Konqueror\n            ], [VERSION, [NAME, 'Konqueror']], [\n            /trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i                       // IE11\n            ], [VERSION, [NAME, 'IE']], [\n            /ya(?:search)?browser\\/([\\w\\.]+)/i                                  // Yandex\n            ], [VERSION, [NAME, 'Yandex']], [\n            /slbrowser\\/([\\w\\.]+)/i                                             // Smart Lenovo Browser\n            ], [VERSION, [NAME, 'Smart Lenovo '+BROWSER]], [\n            /(avast|avg)\\/([\\w\\.]+)/i                                           // Avast/AVG Secure Browser\n            ], [[NAME, /(.+)/, '$1 Secure '+BROWSER], VERSION], [\n            /\\bfocus\\/([\\w\\.]+)/i                                               // Firefox Focus\n            ], [VERSION, [NAME, FIREFOX+' Focus']], [\n            /\\bopt\\/([\\w\\.]+)/i                                                 // Opera Touch\n            ], [VERSION, [NAME, OPERA+' Touch']], [\n            /coc_coc\\w+\\/([\\w\\.]+)/i                                            // Coc Coc Browser\n            ], [VERSION, [NAME, 'Coc Coc']], [\n            /dolfin\\/([\\w\\.]+)/i                                                // Dolphin\n            ], [VERSION, [NAME, 'Dolphin']], [\n            /coast\\/([\\w\\.]+)/i                                                 // Opera Coast\n            ], [VERSION, [NAME, OPERA+' Coast']], [\n            /miuibrowser\\/([\\w\\.]+)/i                                           // MIUI Browser\n            ], [VERSION, [NAME, 'MIUI' + SUFFIX_BROWSER]], [\n            /fxios\\/([\\w\\.-]+)/i                                                // Firefox for iOS\n            ], [VERSION, [NAME, FIREFOX]], [\n            /\\bqihoobrowser\\/?([\\w\\.]*)/i                                       // 360\n            ], [VERSION, [NAME, '360']], [\n            /\\b(qq)\\/([\\w\\.]+)/i                                                // QQ\n            ], [[NAME, /(.+)/, '$1Browser'], VERSION], [\n            /(oculus|sailfish|huawei|vivo|pico)browser\\/([\\w\\.]+)/i\n            ], [[NAME, /(.+)/, '$1' + SUFFIX_BROWSER], VERSION], [              // Oculus/Sailfish/HuaweiBrowser/VivoBrowser/PicoBrowser\n            /samsungbrowser\\/([\\w\\.]+)/i                                        // Samsung Internet\n            ], [VERSION, [NAME, SAMSUNG + ' Internet']], [\n            /metasr[\\/ ]?([\\d\\.]+)/i                                            // Sogou Explorer\n            ], [VERSION, [NAME, 'Sogou Explorer']], [\n            /(sogou)mo\\w+\\/([\\d\\.]+)/i                                          // Sogou Mobile\n            ], [[NAME, 'Sogou Mobile'], VERSION], [\n            /(electron)\\/([\\w\\.]+) safari/i,                                    // Electron-based App\n            /(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,                   // Tesla\n            /m?(qqbrowser|2345(?=browser|chrome|explorer))\\w*[\\/ ]?v?([\\w\\.]+)/i   // QQ/2345\n            ], [NAME, VERSION], [\n            /(lbbrowser|rekonq)/i,                                              // LieBao Browser/Rekonq\n            /\\[(linkedin)app\\]/i                                                // LinkedIn App for iOS & Android\n            ], [NAME], [\n            /ome\\/([\\w\\.]+) \\w* ?(iron) saf/i,                                  // Iron\n            /ome\\/([\\w\\.]+).+qihu (360)[es]e/i                                  // 360\n            ], [VERSION, NAME], [\n\n            // WebView\n            /((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i       // Facebook App for iOS & Android\n            ], [[NAME, FACEBOOK], VERSION], [\n            /(Klarna)\\/([\\w\\.]+)/i,                                             // Klarna Shopping Browser for iOS & Android\n            /(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,                             // Kakao App\n            /(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,                                  // Naver InApp\n            /safari (line)\\/([\\w\\.]+)/i,                                        // Line App for iOS\n            /\\b(line)\\/([\\w\\.]+)\\/iab/i,                                        // Line App for Android\n            /(alipay)client\\/([\\w\\.]+)/i,                                       // Alipay\n            /(twitter)(?:and| f.+e\\/([\\w\\.]+))/i,                               // Twitter\n            /(chromium|instagram|snapchat)[\\/ ]([-\\w\\.]+)/i                     // Chromium/Instagram/Snapchat\n            ], [NAME, VERSION], [\n            /\\bgsa\\/([\\w\\.]+) .*safari\\//i                                      // Google Search Appliance on iOS\n            ], [VERSION, [NAME, 'GSA']], [\n            /musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i                        // TikTok\n            ], [VERSION, [NAME, 'TikTok']], [\n\n            /headlesschrome(?:\\/([\\w\\.]+)| )/i                                  // Chrome Headless\n            ], [VERSION, [NAME, CHROME+' Headless']], [\n\n            / wv\\).+(chrome)\\/([\\w\\.]+)/i                                       // Chrome WebView\n            ], [[NAME, CHROME+' WebView'], VERSION], [\n\n            /droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i           // Android Browser\n            ], [VERSION, [NAME, 'Android '+BROWSER]], [\n\n            /(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i       // Chrome/OmniWeb/Arora/Tizen/Nokia\n            ], [NAME, VERSION], [\n\n            /version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i                      // Mobile Safari\n            ], [VERSION, [NAME, 'Mobile Safari']], [\n            /version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i                // Safari & Safari Mobile\n            ], [VERSION, NAME], [\n            /webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i                      // Safari < 3.0\n            ], [NAME, [VERSION, strMapper, oldSafariMap]], [\n\n            /(webkit|khtml)\\/([\\w\\.]+)/i\n            ], [NAME, VERSION], [\n\n            // Gecko based\n            /(navigator|netscape\\d?)\\/([-\\w\\.]+)/i                              // Netscape\n            ], [[NAME, 'Netscape'], VERSION], [\n            /(wolvic|librewolf)\\/([\\w\\.]+)/i                                    // Wolvic/LibreWolf\n            ], [NAME, VERSION], [\n            /mobile vr; rv:([\\w\\.]+)\\).+firefox/i                               // Firefox Reality\n            ], [VERSION, [NAME, FIREFOX+' Reality']], [\n            /ekiohf.+(flow)\\/([\\w\\.]+)/i,                                       // Flow\n            /(swiftfox)/i,                                                      // Swiftfox\n            /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\\/ ]?([\\w\\.\\+]+)/i,\n                                                                                // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror\n            /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,\n                                                                                // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix\n            /(firefox)\\/([\\w\\.]+)/i,                                            // Other Firefox-based\n            /(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,                         // Mozilla\n\n            // Other\n            /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,\n                                                                                // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Obigo/Mosaic/Go/ICE/UP.Browser\n            /(links) \\(([\\w\\.]+)/i                                              // Links\n            ], [NAME, [VERSION, /_/g, '.']], [\n            \n            /(cobalt)\\/([\\w\\.]+)/i                                              // Cobalt\n            ], [NAME, [VERSION, /master.|lts./, \"\"]]\n        ],\n\n        cpu : [[\n\n            /(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\\)]/i                     // AMD64 (x64)\n            ], [[ARCHITECTURE, 'amd64']], [\n\n            /(ia32(?=;))/i                                                      // IA32 (quicktime)\n            ], [[ARCHITECTURE, lowerize]], [\n\n            /((?:i[346]|x)86)[;\\)]/i                                            // IA32 (x86)\n            ], [[ARCHITECTURE, 'ia32']], [\n\n            /\\b(aarch64|arm(v?8e?l?|_?64))\\b/i                                 // ARM64\n            ], [[ARCHITECTURE, 'arm64']], [\n\n            /\\b(arm(?:v[67])?ht?n?[fl]p?)\\b/i                                   // ARMHF\n            ], [[ARCHITECTURE, 'armhf']], [\n\n            // PocketPC mistakenly identified as PowerPC\n            /windows (ce|mobile); ppc;/i\n            ], [[ARCHITECTURE, 'arm']], [\n\n            /((?:ppc|powerpc)(?:64)?)(?: mac|;|\\))/i                            // PowerPC\n            ], [[ARCHITECTURE, /ower/, EMPTY, lowerize]], [\n\n            /(sun4\\w)[;\\)]/i                                                    // SPARC\n            ], [[ARCHITECTURE, 'sparc']], [\n\n            /((?:avr32|ia64(?=;))|68k(?=\\))|\\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\\b|pa-risc)/i\n                                                                                // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC\n            ], [[ARCHITECTURE, lowerize]]\n        ],\n\n        device : [[\n\n            //////////////////////////\n            // MOBILES & TABLETS\n            /////////////////////////\n\n            // Samsung\n            /\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, TABLET]], [\n            /\\b((?:s[cgp]h|gt|sm)-(?![lr])\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,\n            /samsung[- ]((?!sm-[lr])[-\\w]+)/i,\n            /sec-(sgh\\w+)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, MOBILE]], [\n\n            // Apple\n            /(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i                          // iPod/iPhone\n            ], [MODEL, [VENDOR, APPLE], [TYPE, MOBILE]], [\n            /\\((ipad);[-\\w\\),; ]+apple/i,                                       // iPad\n            /applecoremedia\\/[\\w\\.]+ \\((ipad)/i,\n            /\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i\n            ], [MODEL, [VENDOR, APPLE], [TYPE, TABLET]], [\n            /(macintosh);/i\n            ], [MODEL, [VENDOR, APPLE]], [\n\n            // Sharp\n            /\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i\n            ], [MODEL, [VENDOR, SHARP], [TYPE, MOBILE]], [\n\n            // Honor\n            /(?:honor)([-\\w ]+)[;\\)]/i\n            ], [MODEL, [VENDOR, 'Honor'], [TYPE, MOBILE]], [\n\n            // Huawei\n            /\\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\\d{2})\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, TABLET]], [\n            /(?:huawei)([-\\w ]+)[;\\)]/i,\n            /\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, MOBILE]], [\n\n            // Xiaomi\n            /\\b(poco[\\w ]+|m2\\d{3}j\\d\\d[a-z]{2})(?: bui|\\))/i,                  // Xiaomi POCO\n            /\\b; (\\w+) build\\/hm\\1/i,                                           // Xiaomi Hongmi 'numeric' models\n            /\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,                             // Xiaomi Hongmi\n            /\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,                   // Xiaomi Redmi\n            /oid[^\\)]+; (m?[12][0-389][01]\\w{3,6}[c-y])( bui|; wv|\\))/i,        // Xiaomi Redmi 'numeric' models\n            /\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\\))/i // Xiaomi Mi\n            ], [[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, MOBILE]], [\n            /oid[^\\)]+; (2\\d{4}(283|rpbf)[cgl])( bui|\\))/i,                     // Redmi Pad\n            /\\b(mi[-_ ]?(?:pad)(?:[\\w_ ]+))(?: bui|\\))/i                        // Mi Pad tablets\n            ],[[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, TABLET]], [\n\n            // OPPO\n            /; (\\w+) bui.+ oppo/i,\n            /\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, MOBILE]], [\n            /\\b(opd2\\d{3}a?) bui/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, TABLET]], [\n\n            // Vivo\n            /vivo (\\w+)(?: bui|\\))/i,\n            /\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i\n            ], [MODEL, [VENDOR, 'Vivo'], [TYPE, MOBILE]], [\n\n            // Realme\n            /\\b(rmx[1-3]\\d{3})(?: bui|;|\\))/i\n            ], [MODEL, [VENDOR, 'Realme'], [TYPE, MOBILE]], [\n\n            // Motorola\n            /\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,\n            /\\bmot(?:orola)?[- ](\\w*)/i,\n            /((?:moto[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, MOBILE]], [\n            /\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, TABLET]], [\n\n            // LG\n            /((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i\n            ], [MODEL, [VENDOR, LG], [TYPE, TABLET]], [\n            /(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,\n            /\\blg[-e;\\/ ]+((?!browser|netcast|android tv)\\w+)/i,\n            /\\blg-?([\\d\\w]+) bui/i\n            ], [MODEL, [VENDOR, LG], [TYPE, MOBILE]], [\n\n            // Lenovo\n            /(ideatab[-\\w ]+)/i,\n            /lenovo ?(s[56]000[-\\w]+|tab(?:[\\w ]+)|yt[-\\d\\w]{6}|tb[-\\d\\w]{6})/i\n            ], [MODEL, [VENDOR, 'Lenovo'], [TYPE, TABLET]], [\n\n            // Nokia\n            /(?:maemo|nokia).*(n900|lumia \\d+)/i,\n            /nokia[-_ ]?([-\\w\\.]*)/i\n            ], [[MODEL, /_/g, ' '], [VENDOR, 'Nokia'], [TYPE, MOBILE]], [\n\n            // Google\n            /(pixel c)\\b/i                                                      // Google Pixel C\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, TABLET]], [\n            /droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i                         // Google Pixel\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, MOBILE]], [\n\n            // Sony\n            /droid.+; (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i\n            ], [MODEL, [VENDOR, SONY], [TYPE, MOBILE]], [\n            /sony tablet [ps]/i,\n            /\\b(?:sony)?sgp\\w+(?: bui|\\))/i\n            ], [[MODEL, 'Xperia Tablet'], [VENDOR, SONY], [TYPE, TABLET]], [\n\n            // OnePlus\n            / (kb2005|in20[12]5|be20[12][59])\\b/i,\n            /(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i\n            ], [MODEL, [VENDOR, 'OnePlus'], [TYPE, MOBILE]], [\n\n            // Amazon\n            /(alexa)webm/i,\n            /(kf[a-z]{2}wi|aeo(?!bc)\\w\\w)( bui|\\))/i,                           // Kindle Fire without Silk / Echo Show\n            /(kf[a-z]+)( bui|\\)).+silk\\//i                                      // Kindle Fire HD\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, TABLET]], [\n            /((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i                     // Fire Phone\n            ], [[MODEL, /(.+)/g, 'Fire Phone $1'], [VENDOR, AMAZON], [TYPE, MOBILE]], [\n\n            // BlackBerry\n            /(playbook);[-\\w\\),; ]+(rim)/i                                      // BlackBerry PlayBook\n            ], [MODEL, VENDOR, [TYPE, TABLET]], [\n            /\\b((?:bb[a-f]|st[hv])100-\\d)/i,\n            /\\(bb10; (\\w+)/i                                                    // BlackBerry 10\n            ], [MODEL, [VENDOR, BLACKBERRY], [TYPE, MOBILE]], [\n\n            // Asus\n            /(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, TABLET]], [\n            / (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, MOBILE]], [\n\n            // HTC\n            /(nexus 9)/i                                                        // HTC Nexus 9\n            ], [MODEL, [VENDOR, 'HTC'], [TYPE, TABLET]], [\n            /(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,                         // HTC\n\n            // ZTE\n            /(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,\n            /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i         // Alcatel/GeeksPhone/Nexian/Panasonic/Sony\n            ], [VENDOR, [MODEL, /_/g, ' '], [TYPE, MOBILE]], [\n\n            // TCL\n            /droid [\\w\\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\\w*(\\)| bui)/i\n            ], [MODEL, [VENDOR, 'TCL'], [TYPE, TABLET]], [\n\n            // itel\n            /(itel) ((\\w+))/i\n            ], [[VENDOR, lowerize], MODEL, [TYPE, strMapper, { 'tablet' : ['p10001l', 'w7001'], '*' : 'mobile' }]], [\n\n            // Acer\n            /droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i\n            ], [MODEL, [VENDOR, 'Acer'], [TYPE, TABLET]], [\n\n            // Meizu\n            /droid.+; (m[1-5] note) bui/i,\n            /\\bmz-([-\\w]{2,})/i\n            ], [MODEL, [VENDOR, 'Meizu'], [TYPE, MOBILE]], [\n                \n            // Ulefone\n            /; ((?:power )?armor(?:[\\w ]{0,8}))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Ulefone'], [TYPE, MOBILE]], [\n\n            // Energizer\n            /; (energy ?\\w+)(?: bui|\\))/i,\n            /; energizer ([\\w ]+)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Energizer'], [TYPE, MOBILE]], [\n\n            // Cat\n            /; cat (b35);/i,\n            /; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Cat'], [TYPE, MOBILE]], [\n\n            // Smartfren\n            /((?:new )?andromax[\\w- ]+)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Smartfren'], [TYPE, MOBILE]], [\n\n            // Nothing\n            /droid.+; (a(?:015|06[35]|142p?))/i\n            ], [MODEL, [VENDOR, 'Nothing'], [TYPE, MOBILE]], [\n\n            // MIXED\n            /(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\\w]*)/i,\n                                                                                // BlackBerry/BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Meizu/Motorola/Polytron/Infinix/Tecno/Micromax/Advan\n            /; (imo) ((?!tab)[\\w ]+?)(?: bui|\\))/i,                             // IMO\n            /(hp) ([\\w ]+\\w)/i,                                                 // HP iPAQ\n            /(asus)-?(\\w+)/i,                                                   // Asus\n            /(microsoft); (lumia[\\w ]+)/i,                                      // Microsoft Lumia\n            /(lenovo)[-_ ]?([-\\w]+)/i,                                          // Lenovo\n            /(jolla)/i,                                                         // Jolla\n            /(oppo) ?([\\w ]+) bui/i                                             // OPPO\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /(imo) (tab \\w+)/i,                                                 // IMO\n            /(kobo)\\s(ereader|touch)/i,                                         // Kobo\n            /(archos) (gamepad2?)/i,                                            // Archos\n            /(hp).+(touchpad(?!.+tablet)|tablet)/i,                             // HP TouchPad\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(nook)[\\w ]+build\\/(\\w+)/i,                                        // Nook\n            /(dell) (strea[kpr\\d ]*[\\dko])/i,                                   // Dell Streak\n            /(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,                                  // Le Pan Tablets\n            /(trinity)[- ]*(t\\d{3}) bui/i,                                      // Trinity Tablets\n            /(gigaset)[- ]+(q\\w{1,9}) bui/i,                                    // Gigaset Tablets\n            /(vodafone) ([\\w ]+)(?:\\)| bui)/i                                   // Vodafone\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(surface duo)/i                                                    // Surface Duo\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, TABLET]], [\n            /droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i                                 // Fairphone\n            ], [MODEL, [VENDOR, 'Fairphone'], [TYPE, MOBILE]], [\n            /(u304aa)/i                                                         // AT&T\n            ], [MODEL, [VENDOR, 'AT&T'], [TYPE, MOBILE]], [\n            /\\bsie-(\\w*)/i                                                      // Siemens\n            ], [MODEL, [VENDOR, 'Siemens'], [TYPE, MOBILE]], [\n            /\\b(rct\\w+) b/i                                                     // RCA Tablets\n            ], [MODEL, [VENDOR, 'RCA'], [TYPE, TABLET]], [\n            /\\b(venue[\\d ]{2,7}) b/i                                            // Dell Venue Tablets\n            ], [MODEL, [VENDOR, 'Dell'], [TYPE, TABLET]], [\n            /\\b(q(?:mv|ta)\\w+) b/i                                              // Verizon Tablet\n            ], [MODEL, [VENDOR, 'Verizon'], [TYPE, TABLET]], [\n            /\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i                       // Barnes & Noble Tablet\n            ], [MODEL, [VENDOR, 'Barnes & Noble'], [TYPE, TABLET]], [\n            /\\b(tm\\d{3}\\w+) b/i\n            ], [MODEL, [VENDOR, 'NuVision'], [TYPE, TABLET]], [\n            /\\b(k88) b/i                                                        // ZTE K Series Tablet\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, TABLET]], [\n            /\\b(nx\\d{3}j) b/i                                                   // ZTE Nubia\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, MOBILE]], [\n            /\\b(gen\\d{3}) b.+49h/i                                              // Swiss GEN Mobile\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, MOBILE]], [\n            /\\b(zur\\d{3}) b/i                                                   // Swiss ZUR Tablet\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, TABLET]], [\n            /\\b((zeki)?tb.*\\b) b/i                                              // Zeki Tablets\n            ], [MODEL, [VENDOR, 'Zeki'], [TYPE, TABLET]], [\n            /\\b([yr]\\d{2}) b/i,\n            /\\b(dragon[- ]+touch |dt)(\\w{5}) b/i                                // Dragon Touch Tablet\n            ], [[VENDOR, 'Dragon Touch'], MODEL, [TYPE, TABLET]], [\n            /\\b(ns-?\\w{0,9}) b/i                                                // Insignia Tablets\n            ], [MODEL, [VENDOR, 'Insignia'], [TYPE, TABLET]], [\n            /\\b((nxa|next)-?\\w{0,9}) b/i                                        // NextBook Tablets\n            ], [MODEL, [VENDOR, 'NextBook'], [TYPE, TABLET]], [\n            /\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i                  // Voice Xtreme Phones\n            ], [[VENDOR, 'Voice'], MODEL, [TYPE, MOBILE]], [\n            /\\b(lvtel\\-)?(v1[12]) b/i                                           // LvTel Phones\n            ], [[VENDOR, 'LvTel'], MODEL, [TYPE, MOBILE]], [\n            /\\b(ph-1) /i                                                        // Essential PH-1\n            ], [MODEL, [VENDOR, 'Essential'], [TYPE, MOBILE]], [\n            /\\b(v(100md|700na|7011|917g).*\\b) b/i                               // Envizen Tablets\n            ], [MODEL, [VENDOR, 'Envizen'], [TYPE, TABLET]], [\n            /\\b(trio[-\\w\\. ]+) b/i                                              // MachSpeed Tablets\n            ], [MODEL, [VENDOR, 'MachSpeed'], [TYPE, TABLET]], [\n            /\\btu_(1491) b/i                                                    // Rotor Tablets\n            ], [MODEL, [VENDOR, 'Rotor'], [TYPE, TABLET]], [\n            /(shield[\\w ]+) b/i                                                 // Nvidia Shield Tablets\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, TABLET]], [\n            /(sprint) (\\w+)/i                                                   // Sprint Phones\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(kin\\.[onetw]{3})/i                                                // Microsoft Kin\n            ], [[MODEL, /\\./g, ' '], [VENDOR, MICROSOFT], [TYPE, MOBILE]], [\n            /droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i             // Zebra\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, TABLET]], [\n            /droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, MOBILE]], [\n\n            ///////////////////\n            // SMARTTVS\n            ///////////////////\n\n            /smart-tv.+(samsung)/i                                              // Samsung\n            ], [VENDOR, [TYPE, SMARTTV]], [\n            /hbbtv.+maple;(\\d+)/i\n            ], [[MODEL, /^/, 'SmartTV'], [VENDOR, SAMSUNG], [TYPE, SMARTTV]], [\n            /(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i        // LG SmartTV\n            ], [[VENDOR, LG], [TYPE, SMARTTV]], [\n            /(apple) ?tv/i                                                      // Apple TV\n            ], [VENDOR, [MODEL, APPLE+' TV'], [TYPE, SMARTTV]], [\n            /crkey/i                                                            // Google Chromecast\n            ], [[MODEL, CHROME+'cast'], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /droid.+aft(\\w+)( bui|\\))/i                                         // Fire TV\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, SMARTTV]], [\n            /\\(dtv[\\);].+(aquos)/i,\n            /(aquos-tv[\\w ]+)\\)/i                                               // Sharp\n            ], [MODEL, [VENDOR, SHARP], [TYPE, SMARTTV]],[\n            /(bravia[\\w ]+)( bui|\\))/i                                              // Sony\n            ], [MODEL, [VENDOR, SONY], [TYPE, SMARTTV]], [\n            /(mitv-\\w{5}) bui/i                                                 // Xiaomi\n            ], [MODEL, [VENDOR, XIAOMI], [TYPE, SMARTTV]], [\n            /Hbbtv.*(technisat) (.*);/i                                         // TechniSAT\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,                          // Roku\n            /hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i         // HbbTV devices\n            ], [[VENDOR, trim], [MODEL, trim], [TYPE, SMARTTV]], [\n            /\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i                   // SmartTV from Unidentified Vendors\n            ], [[TYPE, SMARTTV]], [\n\n            ///////////////////\n            // CONSOLES\n            ///////////////////\n\n            /(ouya)/i,                                                          // Ouya\n            /(nintendo) ([wids3utch]+)/i                                        // Nintendo\n            ], [VENDOR, MODEL, [TYPE, CONSOLE]], [\n            /droid.+; (shield) bui/i                                            // Nvidia\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, CONSOLE]], [\n            /(playstation [345portablevi]+)/i                                   // Playstation\n            ], [MODEL, [VENDOR, SONY], [TYPE, CONSOLE]], [\n            /\\b(xbox(?: one)?(?!; xbox))[\\); ]/i                                // Microsoft Xbox\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, CONSOLE]], [\n\n            ///////////////////\n            // WEARABLES\n            ///////////////////\n\n            /\\b(sm-[lr]\\d\\d[05][fnuw]?s?)\\b/i                                   // Samsung Galaxy Watch\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, WEARABLE]], [\n            /((pebble))app/i                                                    // Pebble\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i                              // Apple Watch\n            ], [MODEL, [VENDOR, APPLE], [TYPE, WEARABLE]], [\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /droid.+; (wt63?0{2,3})\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // XR\n            ///////////////////\n\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /(pico) (4|neo3(?: link|pro)?)/i                                    // Pico\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /; (quest( \\d| pro)?)/i                                             // Oculus Quest\n            ], [MODEL, [VENDOR, FACEBOOK], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // EMBEDDED\n            ///////////////////\n\n            /(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i                              // Tesla\n            ], [VENDOR, [TYPE, EMBEDDED]], [\n            /(aeobc)\\b/i                                                        // Echo Dot\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, EMBEDDED]], [\n\n            ////////////////////\n            // MIXED (GENERIC)\n            ///////////////////\n\n            /droid .+?; ([^;]+?)(?: bui|; wv\\)|\\) applew).+? mobile safari/i    // Android Phones from Unidentified Vendors\n            ], [MODEL, [TYPE, MOBILE]], [\n            /droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i       // Android Tablets from Unidentified Vendors\n            ], [MODEL, [TYPE, TABLET]], [\n            /\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i                      // Unidentifiable Tablet\n            ], [[TYPE, TABLET]], [\n            /(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i    // Unidentifiable Mobile\n            ], [[TYPE, MOBILE]], [\n            /(android[-\\w\\. ]{0,9});.+buil/i                                    // Generic Android Device\n            ], [MODEL, [VENDOR, 'Generic']]\n        ],\n\n        engine : [[\n\n            /windows.+ edge\\/([\\w\\.]+)/i                                       // EdgeHTML\n            ], [VERSION, [NAME, EDGE+'HTML']], [\n\n            /(arkweb)\\/([\\w\\.]+)/i                                              // ArkWeb\n            ], [NAME, VERSION], [\n\n            /webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i                         // Blink\n            ], [VERSION, [NAME, 'Blink']], [\n\n            /(presto)\\/([\\w\\.]+)/i,                                             // Presto\n            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\\/([\\w\\.]+)/i, // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m/Goanna/Servo\n            /ekioh(flow)\\/([\\w\\.]+)/i,                                          // Flow\n            /(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,                           // KHTML/Tasman/Links\n            /(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,                                      // iCab\n            /\\b(libweb)/i\n            ], [NAME, VERSION], [\n\n            /rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i                                     // Gecko\n            ], [VERSION, NAME]\n        ],\n\n        os : [[\n\n            // Windows\n            /microsoft (windows) (vista|xp)/i                                   // Windows (iTunes)\n            ], [NAME, VERSION], [\n            /(windows (?:phone(?: os)?|mobile))[\\/ ]?([\\d\\.\\w ]*)/i             // Windows Phone\n            ], [NAME, [VERSION, strMapper, windowsVersionMap]], [\n            /windows nt 6\\.2; (arm)/i,                                        // Windows RT\n            /windows[\\/ ]?([ntce\\d\\. ]+\\w)(?!.+xbox)/i,\n            /(?:win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i\n            ], [[VERSION, strMapper, windowsVersionMap], [NAME, 'Windows']], [\n\n            // iOS/macOS\n            /ip[honead]{2,4}\\b(?:.*os ([\\w]+) like mac|; opera)/i,              // iOS\n            /(?:ios;fbsv\\/|iphone.+ios[\\/ ])([\\d\\.]+)/i,\n            /cfnetwork\\/.+darwin/i\n            ], [[VERSION, /_/g, '.'], [NAME, 'iOS']], [\n            /(mac os x) ?([\\w\\. ]*)/i,\n            /(macintosh|mac_powerpc\\b)(?!.+haiku)/i                             // Mac OS\n            ], [[NAME, MAC_OS], [VERSION, /_/g, '.']], [\n\n            // Mobile OSes\n            /droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i                    // Android-x86/HarmonyOS\n            ], [VERSION, NAME], [                                               // Android/WebOS/QNX/Bada/RIM/Maemo/MeeGo/Sailfish OS/OpenHarmony\n            /(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\\/ ]?([\\w\\.]*)/i,\n            /(blackberry)\\w*\\/([\\w\\.]*)/i,                                      // Blackberry\n            /(tizen|kaios)[\\/ ]([\\w\\.]+)/i,                                     // Tizen/KaiOS\n            /\\((series40);/i                                                    // Series 40\n            ], [NAME, VERSION], [\n            /\\(bb(10);/i                                                        // BlackBerry 10\n            ], [VERSION, [NAME, BLACKBERRY]], [\n            /(?:symbian ?os|symbos|s60(?=;)|series60)[-\\/ ]?([\\w\\.]*)/i         // Symbian\n            ], [VERSION, [NAME, 'Symbian']], [\n            /mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i // Firefox OS\n            ], [VERSION, [NAME, FIREFOX+' OS']], [\n            /web0s;.+rt(tv)/i,\n            /\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i                              // WebOS\n            ], [VERSION, [NAME, 'webOS']], [\n            /watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i                              // watchOS\n            ], [VERSION, [NAME, 'watchOS']], [\n\n            // Google Chromecast\n            /crkey\\/([\\d\\.]+)/i                                                 // Google Chromecast\n            ], [VERSION, [NAME, CHROME+'cast']], [\n            /(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i                                  // Chromium OS\n            ], [[NAME, CHROMIUM_OS], VERSION],[\n\n            // Smart TVs\n            /panasonic;(viera)/i,                                               // Panasonic Viera\n            /(netrange)mmh/i,                                                   // Netrange\n            /(nettv)\\/(\\d+\\.[\\w\\.]+)/i,                                         // NetTV\n\n            // Console\n            /(nintendo|playstation) ([wids345portablevuch]+)/i,                 // Nintendo/Playstation\n            /(xbox); +xbox ([^\\);]+)/i,                                         // Microsoft Xbox (360, One, X, S, Series X, Series S)\n\n            // Other\n            /\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,                            // Joli/Palm\n            /(mint)[\\/\\(\\) ]?(\\w*)/i,                                           // Mint\n            /(mageia|vectorlinux)[; ]/i,                                        // Mageia/VectorLinux\n            /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,\n                                                                                // Ubuntu/Debian/SUSE/Gentoo/Arch/Slackware/Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus/Raspbian/Plan9/Minix/RISCOS/Contiki/Deepin/Manjaro/elementary/Sabayon/Linspire\n            /(hurd|linux) ?([\\w\\.]*)/i,                                         // Hurd/Linux\n            /(gnu) ?([\\w\\.]*)/i,                                                // GNU\n            /\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i, // FreeBSD/NetBSD/OpenBSD/PC-BSD/GhostBSD/DragonFly\n            /(haiku) (\\w+)/i                                                    // Haiku\n            ], [NAME, VERSION], [\n            /(sunos) ?([\\w\\.\\d]*)/i                                             // Solaris\n            ], [[NAME, 'Solaris'], VERSION], [\n            /((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,                              // Solaris\n            /(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,                                  // AIX\n            /\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i, // BeOS/OS2/AmigaOS/MorphOS/OpenVMS/Fuchsia/HP-UX/SerenityOS\n            /(unix) ?([\\w\\.]*)/i                                                // UNIX\n            ], [NAME, VERSION]\n        ]\n    };\n\n    /////////////////\n    // Constructor\n    ////////////////\n\n    var UAParser = function (ua, extensions) {\n\n        if (typeof ua === OBJ_TYPE) {\n            extensions = ua;\n            ua = undefined;\n        }\n\n        if (!(this instanceof UAParser)) {\n            return new UAParser(ua, extensions).getResult();\n        }\n\n        var _navigator = (typeof window !== UNDEF_TYPE && window.navigator) ? window.navigator : undefined;\n        var _ua = ua || ((_navigator && _navigator.userAgent) ? _navigator.userAgent : EMPTY);\n        var _uach = (_navigator && _navigator.userAgentData) ? _navigator.userAgentData : undefined;\n        var _rgxmap = extensions ? extend(regexes, extensions) : regexes;\n        var _isSelfNav = _navigator && _navigator.userAgent == _ua;\n\n        this.getBrowser = function () {\n            var _browser = {};\n            _browser[NAME] = undefined;\n            _browser[VERSION] = undefined;\n            rgxMapper.call(_browser, _ua, _rgxmap.browser);\n            _browser[MAJOR] = majorize(_browser[VERSION]);\n            // Brave-specific detection\n            if (_isSelfNav && _navigator && _navigator.brave && typeof _navigator.brave.isBrave == FUNC_TYPE) {\n                _browser[NAME] = 'Brave';\n            }\n            return _browser;\n        };\n        this.getCPU = function () {\n            var _cpu = {};\n            _cpu[ARCHITECTURE] = undefined;\n            rgxMapper.call(_cpu, _ua, _rgxmap.cpu);\n            return _cpu;\n        };\n        this.getDevice = function () {\n            var _device = {};\n            _device[VENDOR] = undefined;\n            _device[MODEL] = undefined;\n            _device[TYPE] = undefined;\n            rgxMapper.call(_device, _ua, _rgxmap.device);\n            if (_isSelfNav && !_device[TYPE] && _uach && _uach.mobile) {\n                _device[TYPE] = MOBILE;\n            }\n            // iPadOS-specific detection: identified as Mac, but has some iOS-only properties\n            if (_isSelfNav && _device[MODEL] == 'Macintosh' && _navigator && typeof _navigator.standalone !== UNDEF_TYPE && _navigator.maxTouchPoints && _navigator.maxTouchPoints > 2) {\n                _device[MODEL] = 'iPad';\n                _device[TYPE] = TABLET;\n            }\n            return _device;\n        };\n        this.getEngine = function () {\n            var _engine = {};\n            _engine[NAME] = undefined;\n            _engine[VERSION] = undefined;\n            rgxMapper.call(_engine, _ua, _rgxmap.engine);\n            return _engine;\n        };\n        this.getOS = function () {\n            var _os = {};\n            _os[NAME] = undefined;\n            _os[VERSION] = undefined;\n            rgxMapper.call(_os, _ua, _rgxmap.os);\n            if (_isSelfNav && !_os[NAME] && _uach && _uach.platform && _uach.platform != 'Unknown') {\n                _os[NAME] = _uach.platform  \n                                    .replace(/chrome os/i, CHROMIUM_OS)\n                                    .replace(/macos/i, MAC_OS);           // backward compatibility\n            }\n            return _os;\n        };\n        this.getResult = function () {\n            return {\n                ua      : this.getUA(),\n                browser : this.getBrowser(),\n                engine  : this.getEngine(),\n                os      : this.getOS(),\n                device  : this.getDevice(),\n                cpu     : this.getCPU()\n            };\n        };\n        this.getUA = function () {\n            return _ua;\n        };\n        this.setUA = function (ua) {\n            _ua = (typeof ua === STR_TYPE && ua.length > UA_MAX_LENGTH) ? trim(ua, UA_MAX_LENGTH) : ua;\n            return this;\n        };\n        this.setUA(_ua);\n        return this;\n    };\n\n    UAParser.VERSION = LIBVERSION;\n    UAParser.BROWSER =  enumerize([NAME, VERSION, MAJOR]);\n    UAParser.CPU = enumerize([ARCHITECTURE]);\n    UAParser.DEVICE = enumerize([MODEL, VENDOR, TYPE, CONSOLE, MOBILE, SMARTTV, TABLET, WEARABLE, EMBEDDED]);\n    UAParser.ENGINE = UAParser.OS = enumerize([NAME, VERSION]);\n\n    ///////////\n    // Export\n    //////////\n\n    // check js environment\n    if (typeof(exports) !== UNDEF_TYPE) {\n        // nodejs env\n        if (typeof module !== UNDEF_TYPE && module.exports) {\n            exports = module.exports = UAParser;\n        }\n        exports.UAParser = UAParser;\n    } else {\n        // requirejs env (optional)\n        if (typeof(define) === FUNC_TYPE && define.amd) {\n            define(function () {\n                return UAParser;\n            });\n        } else if (typeof window !== UNDEF_TYPE) {\n            // browser env\n            window.UAParser = UAParser;\n        }\n    }\n\n    // jQuery/Zepto specific (optional)\n    // Note:\n    //   In AMD env the global scope should be kept clean, but jQuery is an exception.\n    //   jQuery always exports to global scope, unless jQuery.noConflict(true) is used,\n    //   and we should catch that.\n    var $ = typeof window !== UNDEF_TYPE && (window.jQuery || window.Zepto);\n    if ($ && !$.ua) {\n        var parser = new UAParser();\n        $.ua = parser.getResult();\n        $.ua.get = function () {\n            return parser.getUA();\n        };\n        $.ua.set = function (ua) {\n            parser.setUA(ua);\n            var result = parser.getResult();\n            for (var prop in result) {\n                $.ua[prop] = result[prop];\n            }\n        };\n    }\n\n})(typeof window === 'object' ? window : this);\n"], "names": [], "mappings": "AAAA,iFAAiF;AACjF;;eAEe,GAAE;;;;sDAIqC,GACtD,iFAAiF;AAEjF,CAAC,SAAU,OAAM,EAAE,SAAS;IAExB;IAEA,cAAc;IACd,YAAY;IACZ,aAAa;IAGb,IAAI,aAAc,UACd,QAAc,IACd,UAAc,KACd,YAAc,YACd,aAAc,aACd,WAAc,UACd,WAAc,UACd,QAAc,SACd,QAAc,SACd,OAAc,QACd,OAAc,QACd,SAAc,UACd,UAAc,WACd,eAAc,gBACd,UAAc,WACd,SAAc,UACd,SAAc,UACd,UAAc,WACd,WAAc,YACd,WAAc,YACd,gBAAgB;IAEpB,IAAI,SAAU,UACV,QAAU,SACV,OAAU,QACV,aAAa,cACb,UAAU,WACV,SAAU,UACV,OAAU,QACV,UAAU,WACV,SAAU,UACV,SAAU,UACV,KAAU,MACV,YAAY,aACZ,WAAY,YACZ,QAAU,SACV,UAAU,WACV,QAAU,SACV,OAAU,QACV,SAAU,UACV,QAAU,SACV,WAAc,YACd,cAAc,eACd,SAAU,UACV,iBAAiB;IAErB,WAAW;IACX,SAAS;IACT,UAAU;IAEV,IAAI,SAAS,SAAU,OAAO,EAAE,UAAU;QAClC,IAAI,gBAAgB,CAAC;QACrB,IAAK,IAAI,KAAK,QAAS;YACnB,IAAI,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,CAAC,MAAM,GAAG,MAAM,GAAG;gBACjD,aAAa,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACtD,OAAO;gBACH,aAAa,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;YACjC;QACJ;QACA,OAAO;IACX,GACA,YAAY,SAAU,GAAG;QACrB,IAAI,QAAQ,CAAC;QACb,IAAK,IAAI,IAAE,GAAG,IAAE,IAAI,MAAM,EAAE,IAAK;YAC7B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,GAAG,GAAG,GAAG,CAAC,EAAE;QACxC;QACA,OAAO;IACX,GACA,MAAM,SAAU,IAAI,EAAE,IAAI;QACtB,OAAO,OAAO,SAAS,WAAW,SAAS,MAAM,OAAO,CAAC,SAAS,WAAW,CAAC,IAAI;IACtF,GACA,WAAW,SAAU,GAAG;QACpB,OAAO,IAAI,WAAW;IAC1B,GACA,WAAW,SAAU,OAAO;QACxB,OAAO,OAAO,YAAa,WAAW,QAAQ,OAAO,CAAC,YAAY,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;IAC7F,GACA,OAAO,SAAU,GAAG,EAAE,GAAG;QACrB,IAAI,OAAO,QAAS,UAAU;YAC1B,MAAM,IAAI,OAAO,CAAC,UAAU;YAC5B,OAAO,OAAO,QAAS,aAAa,MAAM,IAAI,SAAS,CAAC,GAAG;QAC/D;IACR;IAEA,eAAe;IACf,aAAa;IACb,cAAc;IAEd,IAAI,YAAY,SAAU,EAAE,EAAE,MAAM;QAE5B,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,SAAS;QAEhC,gCAAgC;QAChC,MAAO,IAAI,OAAO,MAAM,IAAI,CAAC,QAAS;YAElC,IAAI,QAAQ,MAAM,CAAC,EAAE,EACjB,QAAQ,MAAM,CAAC,IAAI,EAAE,EAAI,0BAA0B;YACvD,IAAI,IAAI;YAER,qCAAqC;YACrC,MAAO,IAAI,MAAM,MAAM,IAAI,CAAC,QAAS;gBAEjC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;oBAAE;gBAAO;gBACxB,UAAU,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;gBAE1B,IAAI,CAAC,CAAC,SAAS;oBACX,IAAK,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;wBAC/B,QAAQ,OAAO,CAAC,EAAE,EAAE;wBACpB,IAAI,KAAK,CAAC,EAAE;wBACZ,4CAA4C;wBAC5C,IAAI,OAAO,MAAM,YAAY,EAAE,MAAM,GAAG,GAAG;4BACvC,IAAI,EAAE,MAAM,KAAK,GAAG;gCAChB,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,WAAW;oCAC1B,wBAAwB;oCACxB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;gCACjC,OAAO;oCACH,yCAAyC;oCACzC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE;gCACrB;4BACJ,OAAO,IAAI,EAAE,MAAM,KAAK,GAAG;gCACvB,kCAAkC;gCAClC,IAAI,OAAO,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG;oCACxD,wCAAwC;oCACxC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,IAAI;gCACxD,OAAO;oCACH,mCAAmC;oCACnC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,MAAM,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI;gCACrD;4BACJ,OAAO,IAAI,EAAE,MAAM,KAAK,GAAG;gCACnB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK;4BAC1E;wBACJ,OAAO;4BACH,IAAI,CAAC,EAAE,GAAG,QAAQ,QAAQ;wBAC9B;oBACJ;gBACJ;YACJ;YACA,KAAK;QACT;IACJ,GAEA,YAAY,SAAU,GAAG,EAAE,GAAG;QAE1B,IAAK,IAAI,KAAK,IAAK;YACf,kCAAkC;YAClC,IAAI,OAAO,GAAG,CAAC,EAAE,KAAK,YAAY,GAAG,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;gBACjD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,IAAK;oBACpC,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM;wBACrB,OAAO,AAAC,MAAM,UAAW,YAAY;oBACzC;gBACJ;YACJ,OAAO,IAAI,IAAI,GAAG,CAAC,EAAE,EAAE,MAAM;gBACzB,OAAO,AAAC,MAAM,UAAW,YAAY;YACzC;QACJ;QACA,OAAO,IAAI,cAAc,CAAC,OAAO,GAAG,CAAC,IAAI,GAAG;IACpD;IAEA,eAAe;IACf,aAAa;IACb,cAAc;IAEd,eAAe;IACf,IAAI,eAAe;QACX,OAAU;QACV,OAAU;QACV,OAAU;QACV,OAAU;QACV,SAAU;QACV,SAAU;QACV,SAAU;QACV,KAAU;IACd,GACA,oBAAoB;QAChB,MAAc;QACd,WAAc;QACd,UAAc;QACd,QAAc;QACd,MAAc;YAAC;YAAU;SAAS;QAClC,SAAc;QACd,KAAc;QACd,KAAc;QACd,OAAc;QACd,MAAc;YAAC;YAAU;SAAU;QACnC,MAAc;IACtB;IAEA,cAAc;IACd,YAAY;IACZ,aAAa;IAEb,IAAI,UAAU;QAEV,SAAU;YAAC;gBAEP,+BAAoE,yBAAyB;aAC5F;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAChC,8BAAoE,iBAAiB;aACpF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE9B,eAAe;gBACf;gBACA;gBACA,0CAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBACpB,wBAAoE,8BAA8B;aACjG;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,QAAM;iBAAQ;aAAC;YAAE;gBACrC,2BAAoE,WAAW;aAC9E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,QAAM;iBAAM;aAAC;YAAE;gBACnC,oBAAoE,eAAe;aAClF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAM;aAAC;YAAE;gBAE7B,QAAQ;gBACR,yDAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAC/B,8CAAoE,UAAU;aAC7E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAU;aAAC;YAAE;gBACjC;gBACA;gBACoE,qDAAqD;gBACzH,gBAAgB;gBAChB;gBACA;gBAEA,qKAAqK;gBACrK;gBACoE,mFAAmF;gBACvJ;gBACA,sBAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBACpB,4BAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAC/B,oBAAoE,aAAa;aAChF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAa;aAAC;YAAE;gBACpC,oDAAoE,YAAY;aAC/E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,OAAK;iBAAQ;aAAC;YAAE;gBACpC;gBACA;gBACA,6BAAoE,SAAS;aAC5E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAChC,wBAAoE,YAAY;aAC/E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAY;aAAC;YAAE;gBACnC,8CAAoE,OAAO;aAC1E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAK;aAAC;YAAE;gBAC5B,mCAAoE,SAAS;aAC5E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAChC,wBAAoE,uBAAuB;aAC1F;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,kBAAgB;iBAAQ;aAAC;YAAE;gBAC/C,0BAAoE,2BAA2B;aAC9F;YAAE;gBAAC;oBAAC;oBAAM;oBAAQ,eAAa;iBAAQ;gBAAE;aAAQ;YAAE;gBACpD,sBAAoE,gBAAgB;aACnF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,UAAQ;iBAAS;aAAC;YAAE;gBACxC,oBAAoE,cAAc;aACjF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,QAAM;iBAAS;aAAC;YAAE;gBACtC,yBAAoE,kBAAkB;aACrF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAU;aAAC;YAAE;gBACjC,qBAAoE,UAAU;aAC7E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAU;aAAC;YAAE;gBACjC,oBAAoE,cAAc;aACjF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,QAAM;iBAAS;aAAC;YAAE;gBACtC,0BAAoE,eAAe;aAClF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,SAAS;iBAAe;aAAC;YAAE;gBAC/C,qBAAoE,kBAAkB;aACrF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAC/B,8BAAoE,MAAM;aACzE;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAM;aAAC;YAAE;gBAC7B,qBAAoE,KAAK;aACxE;YAAE;gBAAC;oBAAC;oBAAM;oBAAQ;iBAAY;gBAAE;aAAQ;YAAE;gBAC3C;aACC;YAAE;gBAAC;oBAAC;oBAAM;oBAAQ,OAAO;iBAAe;gBAAE;aAAQ;YAAE;gBACrD,6BAAoE,mBAAmB;aACtF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,UAAU;iBAAY;aAAC;YAAE;gBAC7C,yBAAoE,iBAAiB;aACpF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAiB;aAAC;YAAE;gBACxC,2BAAoE,eAAe;aAClF;YAAE;gBAAC;oBAAC;oBAAM;iBAAe;gBAAE;aAAQ;YAAE;gBACtC;gBACA;gBACA,qEAAuE,UAAU;aAChF;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBACpB;gBACA,qBAAoE,iCAAiC;aACpG;YAAE;gBAAC;aAAK;YAAE;gBACX;gBACA,mCAAoE,MAAM;aACzE;YAAE;gBAAC;gBAAS;aAAK;YAAE;gBAEpB,UAAU;gBACV,8DAAoE,iCAAiC;aACpG;YAAE;gBAAC;oBAAC;oBAAM;iBAAS;gBAAE;aAAQ;YAAE;gBAChC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,gDAAoE,8BAA8B;aACjG;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBACpB,+BAAoE,iCAAiC;aACpG;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAM;aAAC;YAAE;gBAC7B,6CAAoE,SAAS;aAC5E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAEhC,mCAAoE,kBAAkB;aACrF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,SAAO;iBAAY;aAAC;YAAE;gBAE1C,8BAAoE,iBAAiB;aACpF;YAAE;gBAAC;oBAAC;oBAAM,SAAO;iBAAW;gBAAE;aAAQ;YAAE;gBAEzC,0DAAoE,kBAAkB;aACrF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,aAAW;iBAAQ;aAAC;YAAE;gBAE1C,8DAAoE,mCAAmC;aACtG;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBAEpB,+CAAoE,gBAAgB;aACnF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAgB;aAAC;YAAE;gBACvC,qDAAoE,yBAAyB;aAC5F;YAAE;gBAAC;gBAAS;aAAK;YAAE;gBACpB,+CAAoE,eAAe;aAClF;YAAE;gBAAC;gBAAM;oBAAC;oBAAS;oBAAW;iBAAa;aAAC;YAAE;gBAE/C;aACC;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBAEpB,cAAc;gBACd,uCAAoE,WAAW;aAC9E;YAAE;gBAAC;oBAAC;oBAAM;iBAAW;gBAAE;aAAQ;YAAE;gBAClC,iCAAoE,mBAAmB;aACtF;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBACpB,sCAAoE,kBAAkB;aACrF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,UAAQ;iBAAW;aAAC;YAAE;gBAC1C;gBACA;gBACA;gBACoE,kEAAkE;gBACtI;gBACoE,4DAA4D;gBAChI;gBACA;gBAEA,QAAQ;gBACR;gBACoE,iFAAiF;gBACrJ,uBAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAM;oBAAC;oBAAS;oBAAM;iBAAI;aAAC;YAAE;gBAEjC,uBAAoE,SAAS;aAC5E;YAAE;gBAAC;gBAAM;oBAAC;oBAAS;oBAAgB;iBAAG;aAAC;SAC3C;QAED,KAAM;YAAC;gBAEH,gDAAoE,cAAc;aACjF;YAAE;gBAAC;oBAAC;oBAAc;iBAAQ;aAAC;YAAE;gBAE9B,eAAoE,mBAAmB;aACtF;YAAE;gBAAC;oBAAC;oBAAc;iBAAS;aAAC;YAAE;gBAE/B,yBAAoE,aAAa;aAChF;YAAE;gBAAC;oBAAC;oBAAc;iBAAO;aAAC;YAAE;gBAE7B,mCAAmE,QAAQ;aAC1E;YAAE;gBAAC;oBAAC;oBAAc;iBAAQ;aAAC;YAAE;gBAE9B,kCAAoE,QAAQ;aAC3E;YAAE;gBAAC;oBAAC;oBAAc;iBAAQ;aAAC;YAAE;gBAE9B,4CAA4C;gBAC5C;aACC;YAAE;gBAAC;oBAAC;oBAAc;iBAAM;aAAC;YAAE;gBAE5B,yCAAoE,UAAU;aAC7E;YAAE;gBAAC;oBAAC;oBAAc;oBAAQ;oBAAO;iBAAS;aAAC;YAAE;gBAE9C,iBAAoE,QAAQ;aAC3E;YAAE;gBAAC;oBAAC;oBAAc;iBAAQ;aAAC;YAAE;gBAE9B;aAEC;YAAE;gBAAC;oBAAC;oBAAc;iBAAS;aAAC;SAChC;QAED,QAAS;YAAC;gBAEN,0BAA0B;gBAC1B,oBAAoB;gBACpB,yBAAyB;gBAEzB,UAAU;gBACV;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAQ;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC/C;gBACA;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAQ;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE/C,QAAQ;gBACR,2CAAoE,cAAc;aACjF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC7C;gBACA;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC7C;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;aAAC;YAAE;gBAE7B,QAAQ;gBACR;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE7C,QAAQ;gBACR;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAQ;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE/C,SAAS;gBACT;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC9C;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE9C,SAAS;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA,4GAA4G,YAAY;aACvH;YAAE;gBAAC;oBAAC;oBAAO;oBAAM;iBAAI;gBAAE;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC3D;gBACA,6CAAoE,iBAAiB;aACpF;YAAC;gBAAC;oBAAC;oBAAO;oBAAM;iBAAI;gBAAE;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE1D,OAAO;gBACP;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC9C;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE9C,OAAO;gBACP;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE9C,SAAS;gBACT;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAS;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEhD,WAAW;gBACX;gBACA;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAS;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAChD;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAS;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEhD,KAAK;gBACL;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAG;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC1C;gBACA;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAG;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE1C,SAAS;gBACT;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAS;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEhD,QAAQ;gBACR;gBACA;aACC;YAAE;gBAAC;oBAAC;oBAAO;oBAAM;iBAAI;gBAAE;oBAAC;oBAAQ;iBAAQ;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE5D,SAAS;gBACT,eAAoE,iBAAiB;aACpF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC9C,4CAAoE,eAAe;aAClF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE9C,OAAO;gBACP;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAK;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC5C;gBACA;aACC;YAAE;gBAAC;oBAAC;oBAAO;iBAAgB;gBAAE;oBAAC;oBAAQ;iBAAK;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE/D,UAAU;gBACV;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAU;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEjD,SAAS;gBACT;gBACA;gBACA,+BAAoE,iBAAiB;aACpF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC9C,gDAAoE,aAAa;aAChF;YAAE;gBAAC;oBAAC;oBAAO;oBAAS;iBAAgB;gBAAE;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE1E,aAAa;gBACb,+BAAoE,sBAAsB;aACzF;YAAE;gBAAC;gBAAO;gBAAQ;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACpC;gBACA,iBAAoE,gBAAgB;aACnF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAW;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAElD,OAAO;gBACP;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAK;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC5C;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAK;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE5C,MAAM;gBACN,aAAoE,cAAc;aACjF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC7C;gBAEA,MAAM;gBACN;gBACA,gFAAwF,2CAA2C;aAClI;YAAE;gBAAC;gBAAQ;oBAAC;oBAAO;oBAAM;iBAAI;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEjD,MAAM;gBACN;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE7C,OAAO;gBACP;aACC;YAAE;gBAAC;oBAAC;oBAAQ;iBAAS;gBAAE;gBAAO;oBAAC;oBAAM;oBAAW;wBAAE,UAAW;4BAAC;4BAAW;yBAAQ;wBAAE,KAAM;oBAAS;iBAAE;aAAC;YAAE;gBAExG,OAAO;gBACP;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE9C,QAAQ;gBACR;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAQ;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE/C,UAAU;gBACV;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAU;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEjD,YAAY;gBACZ;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAY;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEnD,MAAM;gBACN;gBACA;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE7C,YAAY;gBACZ;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAY;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEnD,UAAU;gBACV;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAU;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEjD,QAAQ;gBACR;gBACoE,yGAAyG;gBAC7K;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,wBAAoE,OAAO;aAC1E;YAAE;gBAAC;gBAAQ;gBAAO;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEpC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,kCAAoE,WAAW;aAC9E;YAAE;gBAAC;gBAAQ;gBAAO;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAEpC,iBAAoE,cAAc;aACjF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAU;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACjD,oCAAoE,YAAY;aAC/E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAY;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACnD,YAAoE,OAAO;aAC1E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC9C,eAAoE,UAAU;aAC7E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAU;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACjD,gBAAoE,cAAc;aACjF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC7C,yBAAoE,qBAAqB;aACxF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC9C,uBAAoE,iBAAiB;aACpF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAU;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACjD,8CAAoE,wBAAwB;aAC3F;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAiB;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACxD;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAW;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAClD,aAAoE,sBAAsB;aACzF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC7C,kBAAoE,YAAY;aAC/E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC7C,uBAAoE,mBAAmB;aACtF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAQ;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC/C,kBAAoE,mBAAmB;aACtF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAQ;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC/C,uBAAoE,eAAe;aAClF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC9C;gBACA,qCAAoE,sBAAsB;aACzF;YAAE;gBAAC;oBAAC;oBAAQ;iBAAe;gBAAE;gBAAO;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACtD,qBAAoE,mBAAmB;aACtF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAW;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAClD,6BAAoE,mBAAmB;aACtF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAW;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAClD,mDAAoE,sBAAsB;aACzF;YAAE;gBAAC;oBAAC;oBAAQ;iBAAQ;gBAAE;gBAAO;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC/C,0BAAoE,eAAe;aAClF;YAAE;gBAAC;oBAAC;oBAAQ;iBAAQ;gBAAE;gBAAO;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC/C,aAAoE,iBAAiB;aACpF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAY;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACnD,sCAAoE,kBAAkB;aACrF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAU;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACjD,uBAAoE,oBAAoB;aACvF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAY;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACnD,iBAAoE,gBAAgB;aACnF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAQ;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC/C,oBAAoE,wBAAwB;aAC3F;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAS;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAChD,kBAAoE,gBAAgB;aACnF;YAAE;gBAAC;gBAAQ;gBAAO;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACpC,qBAAoE,gBAAgB;aACnF;YAAE;gBAAC;oBAAC;oBAAO;oBAAO;iBAAI;gBAAE;oBAAC;oBAAQ;iBAAU;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC/D,wDAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC7C;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAE7C,mBAAmB;gBACnB,WAAW;gBACX,mBAAmB;gBAEnB,uBAAoE,UAAU;aAC7E;YAAE;gBAAC;gBAAQ;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAC9B;aACC;YAAE;gBAAC;oBAAC;oBAAO;oBAAK;iBAAU;gBAAE;oBAAC;oBAAQ;iBAAQ;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAClE,6DAAoE,aAAa;aAChF;YAAE;gBAAC;oBAAC;oBAAQ;iBAAG;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBACpC,eAAoE,WAAW;aAC9E;YAAE;gBAAC;gBAAQ;oBAAC;oBAAO,QAAM;iBAAM;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBACpD,SAAoE,oBAAoB;aACvF;YAAE;gBAAC;oBAAC;oBAAO,SAAO;iBAAO;gBAAE;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAChE,4BAAoE,UAAU;aAC7E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAC/C;gBACA,sBAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAC;gBAC7C,2BAAwE,OAAO;aAC9E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAK;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAC7C,oBAAoE,SAAS;aAC5E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAC/C,4BAAoE,YAAY;aAC/E;YAAE;gBAAC;gBAAQ;gBAAO;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBACrC;gBACA,4DAAoE,gBAAgB;aACnF;YAAE;gBAAC;oBAAC;oBAAQ;iBAAK;gBAAE;oBAAC;oBAAO;iBAAK;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBACrD,kDAAoE,oCAAoC;aACvG;YAAE;gBAAC;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAEtB,mBAAmB;gBACnB,WAAW;gBACX,mBAAmB;gBAEnB;gBACA,6BAAoE,WAAW;aAC9E;YAAE;gBAAC;gBAAQ;gBAAO;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBACrC,yBAAoE,SAAS;aAC5E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAS;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBACjD,kCAAoE,cAAc;aACjF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAK;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAC7C,qCAAoE,iBAAiB;aACpF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAU;gBAAE;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAElD,mBAAmB;gBACnB,YAAY;gBACZ,mBAAmB;gBAEnB,kCAAoE,uBAAuB;aAC1F;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAQ;gBAAE;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBACjD,iBAAoE,SAAS;aAC5E;YAAE;gBAAC;gBAAQ;gBAAO;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBACtC,uCAAoE,cAAc;aACjF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAC/C,uBAAoE,eAAe;aAClF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAChD;aACC;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAM;gBAAE;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAE/C,mBAAmB;gBACnB,KAAK;gBACL,mBAAmB;gBAEnB,uBAAoE,eAAe;aAClF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAChD,iCAAoE,OAAO;aAC1E;YAAE;gBAAC;gBAAQ;gBAAO;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBACtC,wBAAoE,eAAe;aAClF;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAS;gBAAE;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAElD,mBAAmB;gBACnB,WAAW;gBACX,mBAAmB;gBAEnB,uCAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAQ;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAC/B,aAAoE,WAAW;aAC9E;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAO;gBAAE;oBAAC;oBAAM;iBAAS;aAAC;YAAE;gBAEhD,oBAAoB;gBACpB,kBAAkB;gBAClB,mBAAmB;gBAEnB,iEAAoE,2CAA2C;aAC9G;YAAE;gBAAC;gBAAO;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC5B,8DAAoE,4CAA4C;aAC/G;YAAE;gBAAC;gBAAO;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBAC5B,+CAAoE,wBAAwB;aAC3F;YAAE;gBAAC;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACrB,iEAAoE,wBAAwB;aAC3F;YAAE;gBAAC;oBAAC;oBAAM;iBAAO;aAAC;YAAE;gBACrB,iCAAoE,yBAAyB;aAC5F;YAAE;gBAAC;gBAAO;oBAAC;oBAAQ;iBAAU;aAAC;SAClC;QAED,QAAS;YAAC;gBAEN,6BAAmE,WAAW;aAC7E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,OAAK;iBAAO;aAAC;YAAE;gBAEnC,uBAAoE,SAAS;aAC5E;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBAEpB,4CAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAE/B;gBACA;gBACA;gBACA;gBACA;gBACA;aACC;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBAEpB,gCAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAS;aAAK;SACrB;QAED,IAAK;YAAC;gBAEF,UAAU;gBACV,kCAAoE,mBAAmB;aACtF;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBACpB,wDAAoE,gBAAgB;aACnF;YAAE;gBAAC;gBAAM;oBAAC;oBAAS;oBAAW;iBAAkB;aAAC;YAAE;gBACpD;gBACA;gBACA;aACC;YAAE;gBAAC;oBAAC;oBAAS;oBAAW;iBAAkB;gBAAE;oBAAC;oBAAM;iBAAU;aAAC;YAAE;gBAEjE,YAAY;gBACZ;gBACA;gBACA;aACC;YAAE;gBAAC;oBAAC;oBAAS;oBAAM;iBAAI;gBAAE;oBAAC;oBAAM;iBAAM;aAAC;YAAE;gBAC1C;gBACA,wCAAoE,SAAS;aAC5E;YAAE;gBAAC;oBAAC;oBAAM;iBAAO;gBAAE;oBAAC;oBAAS;oBAAM;iBAAI;aAAC;YAAE;gBAE3C,cAAc;gBACd,iDAAoE,wBAAwB;aAC3F;YAAE;gBAAC;gBAAS;aAAK;YAAE;gBACpB;gBACA;gBACA;gBACA,iBAAoE,YAAY;aAC/E;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBACpB,aAAoE,gBAAgB;aACnF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAW;aAAC;YAAE;gBAClC,4DAAoE,UAAU;aAC7E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAU;aAAC;YAAE;gBACjC,kFAAkF,aAAa;aAC9F;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,UAAQ;iBAAM;aAAC;YAAE;gBACrC;gBACA,uCAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAQ;aAAC;YAAE;gBAC/B,uCAAoE,UAAU;aAC7E;YAAE;gBAAC;gBAAS;oBAAC;oBAAM;iBAAU;aAAC;YAAE;gBAEjC,oBAAoB;gBACpB,oBAAoE,oBAAoB;aACvF;YAAE;gBAAC;gBAAS;oBAAC;oBAAM,SAAO;iBAAO;aAAC;YAAE;gBACrC,mCAAoE,cAAc;aACjF;YAAE;gBAAC;oBAAC;oBAAM;iBAAY;gBAAE;aAAQ;YAAC;gBAElC,YAAY;gBACZ;gBACA;gBACA;gBAEA,UAAU;gBACV;gBACA;gBAEA,QAAQ;gBACR;gBACA;gBACA;gBACA;gBACoE,iLAAiL;gBACrP;gBACA;gBACA;gBACA,iBAAoE,QAAQ;aAC3E;YAAE;gBAAC;gBAAM;aAAQ;YAAE;gBACpB,wBAAoE,UAAU;aAC7E;YAAE;gBAAC;oBAAC;oBAAM;iBAAU;gBAAE;aAAQ;YAAE;gBACjC;gBACA;gBACA;gBACA,qBAAoE,OAAO;aAC1E;YAAE;gBAAC;gBAAM;aAAQ;SACrB;IACL;IAEA,iBAAiB;IACjB,cAAc;IACd,gBAAgB;IAEhB,IAAI,WAAW,SAAU,EAAE,EAAE,UAAU;QAEnC,IAAI,OAAO,OAAO,UAAU;YACxB,aAAa;YACb,KAAK;QACT;QAEA,IAAI,CAAC,CAAC,IAAI,YAAY,QAAQ,GAAG;YAC7B,OAAO,IAAI,SAAS,IAAI,YAAY,SAAS;QACjD;QAEA,IAAI,aAAa,AAAC,OAAO,YAAW,cAAc,QAAO,SAAS,GAAI,QAAO,SAAS,GAAG;QACzF,IAAI,MAAM,MAAM,CAAC,AAAC,cAAc,WAAW,SAAS,GAAI,WAAW,SAAS,GAAG,KAAK;QACpF,IAAI,QAAQ,AAAC,cAAc,WAAW,aAAa,GAAI,WAAW,aAAa,GAAG;QAClF,IAAI,UAAU,aAAa,OAAO,SAAS,cAAc;QACzD,IAAI,aAAa,cAAc,WAAW,SAAS,IAAI;QAEvD,IAAI,CAAC,UAAU,GAAG;YACd,IAAI,WAAW,CAAC;YAChB,QAAQ,CAAC,KAAK,GAAG;YACjB,QAAQ,CAAC,QAAQ,GAAG;YACpB,UAAU,IAAI,CAAC,UAAU,KAAK,QAAQ,OAAO;YAC7C,QAAQ,CAAC,MAAM,GAAG,SAAS,QAAQ,CAAC,QAAQ;YAC5C,2BAA2B;YAC3B,IAAI,cAAc,cAAc,WAAW,KAAK,IAAI,OAAO,WAAW,KAAK,CAAC,OAAO,IAAI,WAAW;gBAC9F,QAAQ,CAAC,KAAK,GAAG;YACrB;YACA,OAAO;QACX;QACA,IAAI,CAAC,MAAM,GAAG;YACV,IAAI,OAAO,CAAC;YACZ,IAAI,CAAC,aAAa,GAAG;YACrB,UAAU,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG;YACrC,OAAO;QACX;QACA,IAAI,CAAC,SAAS,GAAG;YACb,IAAI,UAAU,CAAC;YACf,OAAO,CAAC,OAAO,GAAG;YAClB,OAAO,CAAC,MAAM,GAAG;YACjB,OAAO,CAAC,KAAK,GAAG;YAChB,UAAU,IAAI,CAAC,SAAS,KAAK,QAAQ,MAAM;YAC3C,IAAI,cAAc,CAAC,OAAO,CAAC,KAAK,IAAI,SAAS,MAAM,MAAM,EAAE;gBACvD,OAAO,CAAC,KAAK,GAAG;YACpB;YACA,iFAAiF;YACjF,IAAI,cAAc,OAAO,CAAC,MAAM,IAAI,eAAe,cAAc,OAAO,WAAW,UAAU,KAAK,cAAc,WAAW,cAAc,IAAI,WAAW,cAAc,GAAG,GAAG;gBACxK,OAAO,CAAC,MAAM,GAAG;gBACjB,OAAO,CAAC,KAAK,GAAG;YACpB;YACA,OAAO;QACX;QACA,IAAI,CAAC,SAAS,GAAG;YACb,IAAI,UAAU,CAAC;YACf,OAAO,CAAC,KAAK,GAAG;YAChB,OAAO,CAAC,QAAQ,GAAG;YACnB,UAAU,IAAI,CAAC,SAAS,KAAK,QAAQ,MAAM;YAC3C,OAAO;QACX;QACA,IAAI,CAAC,KAAK,GAAG;YACT,IAAI,MAAM,CAAC;YACX,GAAG,CAAC,KAAK,GAAG;YACZ,GAAG,CAAC,QAAQ,GAAG;YACf,UAAU,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YACnC,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK,IAAI,SAAS,MAAM,QAAQ,IAAI,MAAM,QAAQ,IAAI,WAAW;gBACpF,GAAG,CAAC,KAAK,GAAG,MAAM,QAAQ,CACL,OAAO,CAAC,cAAc,aACtB,OAAO,CAAC,UAAU,SAAmB,yBAAyB;YACvF;YACA,OAAO;QACX;QACA,IAAI,CAAC,SAAS,GAAG;YACb,OAAO;gBACH,IAAU,IAAI,CAAC,KAAK;gBACpB,SAAU,IAAI,CAAC,UAAU;gBACzB,QAAU,IAAI,CAAC,SAAS;gBACxB,IAAU,IAAI,CAAC,KAAK;gBACpB,QAAU,IAAI,CAAC,SAAS;gBACxB,KAAU,IAAI,CAAC,MAAM;YACzB;QACJ;QACA,IAAI,CAAC,KAAK,GAAG;YACT,OAAO;QACX;QACA,IAAI,CAAC,KAAK,GAAG,SAAU,EAAE;YACrB,MAAM,AAAC,OAAO,OAAO,YAAY,GAAG,MAAM,GAAG,gBAAiB,KAAK,IAAI,iBAAiB;YACxF,OAAO,IAAI;QACf;QACA,IAAI,CAAC,KAAK,CAAC;QACX,OAAO,IAAI;IACf;IAEA,SAAS,OAAO,GAAG;IACnB,SAAS,OAAO,GAAI,UAAU;QAAC;QAAM;QAAS;KAAM;IACpD,SAAS,GAAG,GAAG,UAAU;QAAC;KAAa;IACvC,SAAS,MAAM,GAAG,UAAU;QAAC;QAAO;QAAQ;QAAM;QAAS;QAAQ;QAAS;QAAQ;QAAU;KAAS;IACvG,SAAS,MAAM,GAAG,SAAS,EAAE,GAAG,UAAU;QAAC;QAAM;KAAQ;IAEzD,WAAW;IACX,SAAS;IACT,UAAU;IAEV,uBAAuB;IACvB,IAAI,OAAO,YAAa,YAAY;QAChC,aAAa;QACb,IAAI,+CAAkB,cAAc,OAAO,OAAO,EAAE;YAChD,UAAU,OAAO,OAAO,GAAG;QAC/B;QACA,QAAQ,QAAQ,GAAG;IACvB,OAAO;QACH,2BAA2B;QAC3B,IAAI,OAAO,WAAY,aAAa,OAAO,GAAG,EAAE;YAC5C,qDAAO;gBACH,OAAO;YACX;QACJ,OAAO,IAAI,OAAO,YAAW,YAAY;YACrC,cAAc;YACd,QAAO,QAAQ,GAAG;QACtB;IACJ;IAEA,mCAAmC;IACnC,QAAQ;IACR,kFAAkF;IAClF,mFAAmF;IACnF,8BAA8B;IAC9B,IAAI,IAAI,OAAO,YAAW,cAAc,CAAC,QAAO,MAAM,IAAI,QAAO,KAAK;IACtE,IAAI,KAAK,CAAC,EAAE,EAAE,EAAE;QACZ,IAAI,SAAS,IAAI;QACjB,EAAE,EAAE,GAAG,OAAO,SAAS;QACvB,EAAE,EAAE,CAAC,GAAG,GAAG;YACP,OAAO,OAAO,KAAK;QACvB;QACA,EAAE,EAAE,CAAC,GAAG,GAAG,SAAU,EAAE;YACnB,OAAO,KAAK,CAAC;YACb,IAAI,SAAS,OAAO,SAAS;YAC7B,IAAK,IAAI,QAAQ,OAAQ;gBACrB,EAAE,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;YAC7B;QACJ;IACJ;AAEJ,CAAC,EAAE,OAAO,WAAW,WAAW,SAAS,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/deepmerge/dist/cjs.js"], "sourcesContent": ["'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,oBAAoB,SAAS,kBAAkB,KAAK;IACvD,OAAO,gBAAgB,UACnB,CAAC,UAAU;AAChB;AAEA,SAAS,gBAAgB,KAAK;IAC7B,OAAO,CAAC,CAAC,SAAS,OAAO,UAAU;AACpC;AAEA,SAAS,UAAU,KAAK;IACvB,IAAI,cAAc,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;IAEjD,OAAO,gBAAgB,qBACnB,gBAAgB,mBAChB,eAAe;AACpB;AAEA,6IAA6I;AAC7I,IAAI,eAAe,OAAO,WAAW,cAAc,OAAO,GAAG;AAC7D,IAAI,qBAAqB,eAAe,OAAO,GAAG,CAAC,mBAAmB;AAEtE,SAAS,eAAe,KAAK;IAC5B,OAAO,MAAM,QAAQ,KAAK;AAC3B;AAEA,SAAS,YAAY,GAAG;IACvB,OAAO,MAAM,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;AACnC;AAEA,SAAS,8BAA8B,KAAK,EAAE,OAAO;IACpD,OAAO,AAAC,QAAQ,KAAK,KAAK,SAAS,QAAQ,iBAAiB,CAAC,SAC1D,UAAU,YAAY,QAAQ,OAAO,WACrC;AACJ;AAEA,SAAS,kBAAkB,MAAM,EAAE,MAAM,EAAE,OAAO;IACjD,OAAO,OAAO,MAAM,CAAC,QAAQ,GAAG,CAAC,SAAS,OAAO;QAChD,OAAO,8BAA8B,SAAS;IAC/C;AACD;AAEA,SAAS,iBAAiB,GAAG,EAAE,OAAO;IACrC,IAAI,CAAC,QAAQ,WAAW,EAAE;QACzB,OAAO;IACR;IACA,IAAI,cAAc,QAAQ,WAAW,CAAC;IACtC,OAAO,OAAO,gBAAgB,aAAa,cAAc;AAC1D;AAEA,SAAS,gCAAgC,MAAM;IAC9C,OAAO,OAAO,qBAAqB,GAChC,OAAO,qBAAqB,CAAC,QAAQ,MAAM,CAAC,SAAS,MAAM;QAC5D,OAAO,OAAO,oBAAoB,CAAC,IAAI,CAAC,QAAQ;IACjD,KACE,EAAE;AACN;AAEA,SAAS,QAAQ,MAAM;IACtB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,gCAAgC;AACnE;AAEA,SAAS,mBAAmB,MAAM,EAAE,QAAQ;IAC3C,IAAI;QACH,OAAO,YAAY;IACpB,EAAE,OAAM,GAAG;QACV,OAAO;IACR;AACD;AAEA,mFAAmF;AACnF,SAAS,iBAAiB,MAAM,EAAE,GAAG;IACpC,OAAO,mBAAmB,QAAQ,KAAK,sEAAsE;QACzG,CAAC,CAAC,OAAO,cAAc,CAAC,IAAI,CAAC,QAAQ,KAAK,+CAA+C;QACxF,OAAO,oBAAoB,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,4CAA4C;;AACjG;AAEA,SAAS,YAAY,MAAM,EAAE,MAAM,EAAE,OAAO;IAC3C,IAAI,cAAc,CAAC;IACnB,IAAI,QAAQ,iBAAiB,CAAC,SAAS;QACtC,QAAQ,QAAQ,OAAO,CAAC,SAAS,GAAG;YACnC,WAAW,CAAC,IAAI,GAAG,8BAA8B,MAAM,CAAC,IAAI,EAAE;QAC/D;IACD;IACA,QAAQ,QAAQ,OAAO,CAAC,SAAS,GAAG;QACnC,IAAI,iBAAiB,QAAQ,MAAM;YAClC;QACD;QAEA,IAAI,mBAAmB,QAAQ,QAAQ,QAAQ,iBAAiB,CAAC,MAAM,CAAC,IAAI,GAAG;YAC9E,WAAW,CAAC,IAAI,GAAG,iBAAiB,KAAK,SAAS,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE;QAC7E,OAAO;YACN,WAAW,CAAC,IAAI,GAAG,8BAA8B,MAAM,CAAC,IAAI,EAAE;QAC/D;IACD;IACA,OAAO;AACR;AAEA,SAAS,UAAU,MAAM,EAAE,MAAM,EAAE,OAAO;IACzC,UAAU,WAAW,CAAC;IACtB,QAAQ,UAAU,GAAG,QAAQ,UAAU,IAAI;IAC3C,QAAQ,iBAAiB,GAAG,QAAQ,iBAAiB,IAAI;IACzD,kFAAkF;IAClF,6DAA6D;IAC7D,QAAQ,6BAA6B,GAAG;IAExC,IAAI,gBAAgB,MAAM,OAAO,CAAC;IAClC,IAAI,gBAAgB,MAAM,OAAO,CAAC;IAClC,IAAI,4BAA4B,kBAAkB;IAElD,IAAI,CAAC,2BAA2B;QAC/B,OAAO,8BAA8B,QAAQ;IAC9C,OAAO,IAAI,eAAe;QACzB,OAAO,QAAQ,UAAU,CAAC,QAAQ,QAAQ;IAC3C,OAAO;QACN,OAAO,YAAY,QAAQ,QAAQ;IACpC;AACD;AAEA,UAAU,GAAG,GAAG,SAAS,aAAa,KAAK,EAAE,OAAO;IACnD,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;QAC1B,MAAM,IAAI,MAAM;IACjB;IAEA,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI,EAAE,IAAI;QACtC,OAAO,UAAU,MAAM,MAAM;IAC9B,GAAG,CAAC;AACL;AAEA,IAAI,cAAc;AAElB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/classnames/index.js"], "sourcesContent": ["/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n"], "names": [], "mappings": "AAAA;;;;AAIA,GACA,iBAAiB,GAEhB,CAAA;IACA;IAEA,IAAI,SAAS,CAAC,EAAE,cAAc;IAE9B,SAAS;QACR,IAAI,UAAU;QAEd,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YAC1C,IAAI,MAAM,SAAS,CAAC,EAAE;YACtB,IAAI,KAAK;gBACR,UAAU,YAAY,SAAS,WAAW;YAC3C;QACD;QAEA,OAAO;IACR;IAEA,SAAS,WAAY,GAAG;QACvB,IAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;YACvD,OAAO;QACR;QAEA,IAAI,OAAO,QAAQ,UAAU;YAC5B,OAAO;QACR;QAEA,IAAI,MAAM,OAAO,CAAC,MAAM;YACvB,OAAO,WAAW,KAAK,CAAC,MAAM;QAC/B;QAEA,IAAI,IAAI,QAAQ,KAAK,OAAO,SAAS,CAAC,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,kBAAkB;YACrG,OAAO,IAAI,QAAQ;QACpB;QAEA,IAAI,UAAU;QAEd,IAAK,IAAI,OAAO,IAAK;YACpB,IAAI,OAAO,IAAI,CAAC,KAAK,QAAQ,GAAG,CAAC,IAAI,EAAE;gBACtC,UAAU,YAAY,SAAS;YAChC;QACD;QAEA,OAAO;IACR;IAEA,SAAS,YAAa,KAAK,EAAE,QAAQ;QACpC,IAAI,CAAC,UAAU;YACd,OAAO;QACR;QAEA,IAAI,OAAO;YACV,OAAO,QAAQ,MAAM;QACtB;QAEA,OAAO,QAAQ;IAChB;IAEA,IAAI,+CAAkB,eAAe,OAAO,OAAO,EAAE;QACpD,WAAW,OAAO,GAAG;QACrB,OAAO,OAAO,GAAG;IAClB,OAAO,IAAI,OAAO,WAAW,cAAc,OAAO,OAAO,GAAG,KAAK,YAAY,OAAO,GAAG,EAAE;QACxF,6DAA6D;QAC7D,qDAAyB;YACxB,OAAO;QACR;IACD,OAAO;QACN,OAAO,UAAU,GAAG;IACrB;AACD,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9419, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": ["file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/utils/web.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/components/TonConnectUIProvider.tsx", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/errors/ton-connect-ui-react.error.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/errors/ton-connect-provider-not-set.error.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/utils/errors.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/hooks/useTonConnectUI.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/components/TonConnectButton.tsx", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/hooks/useTonWallet.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/hooks/useTonAddress.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/hooks/useTonConnectModal.ts", "file:///C:/webapp%20PEPE%20CAS/pepe-cas/node_modules/%40tonconnect/ui-react/src/hooks/useIsConnectionRestored.ts"], "sourcesContent": ["export function isClientSide(): boolean {\n    return typeof window !== 'undefined';\n}\n\nexport function isServerSide(): boolean {\n    return !isClientSide();\n}\n", "import { createContext, FunctionComponent, memo, ReactNode } from 'react';\nimport {\n    ActionConfiguration,\n    Locales,\n    TonConnectUI,\n    UIPreferences,\n    WalletsListConfiguration\n} from '@tonconnect/ui';\nimport type { ITonConnect, RequiredFeatures } from '@tonconnect/ui';\nimport { isClientSide } from '../utils/web';\n\nexport const TonConnectUIContext = createContext<TonConnectUI | null>(null);\n\nexport type TonConnectUIProviderProps = {\n    children: ReactNode;\n} & Partial<TonConnectUIProviderPropsBase> &\n    Partial<TonConnectUIProviderPropsWithManifest | TonConnectUIProviderPropsWithConnector>;\n\nexport interface TonConnectUIProviderPropsWithManifest {\n    /**\n     * Url to the [manifest]{@link https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest} with the Dapp metadata that will be displayed in the user's wallet.\n     * If not passed, manifest from `${window.location.origin}/tonconnect-manifest.json` will be taken.\n     */\n    manifestUrl: string;\n}\n\nexport interface TonConnectUIProviderPropsWithConnector {\n    /**\n     * TonConnect instance. Can be helpful if you use custom ITonConnect implementation, or use both of @tonconnect/sdk and @tonconnect/ui in your app.\n     */\n    connector: ITonConnect;\n}\n\nexport interface TonConnectUIProviderPropsBase {\n    /**\n     * Try to restore existing session and reconnect to the corresponding wallet.\n     * @default true.\n     */\n    restoreConnection: boolean;\n\n    /**\n     * Language for the phrases it the UI elements.\n     * @default system\n     */\n    language: Locales;\n\n    /**\n     * HTML element id to attach the modal window element. If not passed, `div#tc-widget-root` in the end of the <body> will be added and used.\n     * @default `div#tc-widget-root`.\n     */\n    widgetRootId: string;\n\n    /**\n     * UI elements configuration.\n     */\n    uiPreferences?: UIPreferences;\n\n    /**\n     * Configuration for the wallets list in the connect wallet modal.\n     */\n    walletsListConfiguration?: WalletsListConfiguration;\n\n    /**\n     * Required features for wallets to be displayed in the connect wallet modal.\n     */\n    walletsRequiredFeatures?: RequiredFeatures;\n\n    /**\n     * Configuration for action-period (e.g. sendTransaction) UI elements: modals and notifications and wallet behaviour (return strategy).\n     */\n    actionsConfiguration?: ActionConfiguration;\n\n    /**\n     * Specifies whether the Android back button should be used to close modals and notifications on Android devices.\n     * @default true\n     */\n    enableAndroidBackHandler?: boolean;\n}\n\nlet tonConnectUI: TonConnectUI | null = null;\n\n/**\n * Add TonConnectUIProvider to the root of the app. You can specify UI options using props.\n * All TonConnect UI hooks calls and `<TonConnectButton />` component must be placed inside `<TonConnectUIProvider>`.\n * @param children JSX to insert.\n * @param [options] additional options.\n * @constructor\n */\nconst TonConnectUIProvider: FunctionComponent<TonConnectUIProviderProps> = ({\n    children,\n    ...options\n}) => {\n    if (isClientSide() && !tonConnectUI) {\n        tonConnectUI = new TonConnectUI(options);\n    }\n\n    return (\n        <TonConnectUIContext.Provider value={tonConnectUI}>{children}</TonConnectUIContext.Provider>\n    );\n};\n\nexport default memo(TonConnectUIProvider);\n", "import { TonConnectUIError } from '@tonconnect/ui';\n\n/**\n * Base class for TonConnectUIReact errors. You can check if the error was triggered by the @tonconnect/ui-react using `err instanceof TonConnectUIReactError`.\n */\nexport class TonConnectUIReactError extends TonConnectUIError {\n    constructor(...args: ConstructorParameters<typeof Error>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, TonConnectUIReactError.prototype);\n    }\n}\n", "import { TonConnectUIReactError } from './ton-connect-ui-react.error';\n\n/**\n * Thrown when either <TonConnectProvider> not added to the top of the tags tree,\n * either there is an attempt using TonConnect UI hook or <TonConnectButton> inside <TonConnectProvider>\n */\nexport class TonConnectProviderNotSetError extends TonConnectUIReactError {\n    constructor(...args: ConstructorParameters<typeof Error>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, TonConnectProviderNotSetError.prototype);\n    }\n}\n", "import { TonConnectUI } from '@tonconnect/ui';\nimport { TonConnectProviderNotSetError } from '../errors/ton-connect-provider-not-set.error';\n\nexport function checkProvider(provider: TonConnectUI | null): provider is TonConnectUI {\n    if (!provider) {\n        throw new TonConnectProviderNotSetError(\n            'You should add <TonConnectUIProvider> on the top of the app to use TonConnect'\n        );\n    }\n\n    return true;\n}\n", "import { useCallback, useContext } from 'react';\nimport { TonConnectUIContext } from '../components/TonConnectUIProvider';\nimport { TonConnectUI, TonConnectUiOptions } from '@tonconnect/ui';\nimport { checkProvider } from '../utils/errors';\nimport { isServerSide } from '../utils/web';\n\n/**\n * Use it to get access to the `TonConnectUI` instance and UI options updating function.\n */\nexport function useTonConnectUI(): [TonConnectUI, (options: TonConnectUiOptions) => void] {\n    const tonConnectUI = useContext(TonConnectUIContext);\n    const setOptions = useCallback(\n        (options: TonConnectUiOptions) => {\n            if (tonConnectUI) {\n                tonConnectUI!.uiOptions = options;\n            }\n        },\n        [tonConnectUI]\n    );\n\n    if (isServerSide()) {\n        return [null as unknown as TonConnectUI, () => {}];\n    }\n\n    checkProvider(tonConnectUI);\n    return [tonConnectUI!, setOptions];\n}\n", "import { CSSProperties, FunctionComponent, memo, useEffect } from 'react';\nimport { useTonConnectUI } from '../hooks/useTonConnectUI';\n\nconst buttonRootId = 'ton-connect-button';\n\nexport interface TonConnectButtonProps {\n    className?: string;\n\n    style?: CSSProperties;\n}\n\n/**\n * TonConnect Button is universal UI component for initializing connection. After wallet is connected it transforms to a wallet menu.\n * It is recommended to place it in the top right corner of your app.\n * @param [className] css class to add to the button container.\n * @param [style] style to add to the button container.\n * @constructor\n */\nconst TonConnectButton: FunctionComponent<TonConnectButtonProps> = ({ className, style }) => {\n    const [_, setOptions] = useTonConnectUI();\n\n    useEffect(() => {\n        setOptions({ buttonRootId });\n        return () => setOptions({ buttonRootId: null });\n    }, [setOptions]);\n\n    return (\n        <div\n            id={buttonRootId}\n            className={className}\n            style={{ width: 'fit-content', ...style }}\n        ></div>\n    );\n};\n\nexport default memo(TonConnectButton);\n", "import { useEffect, useState } from 'react';\nimport { ConnectedWallet, Wallet, WalletInfoWithOpenMethod } from '@tonconnect/ui';\nimport { useTonConnectUI } from './useTonConnectUI';\n\n/**\n * Use it to get user's current ton wallet. If wallet is not connected hook will return null.\n */\nexport function useTonWallet(): Wallet | (Wallet & WalletInfoWithOpenMethod) | null {\n    const [tonConnectUI] = useTonConnectUI();\n    const [wallet, setWallet] = useState<Wallet | (Wallet & WalletInfoWithOpenMethod) | null>(\n        tonConnectUI?.wallet || null\n    );\n\n    useEffect(() => {\n        if (tonConnectUI) {\n            setWallet(tonConnectUI.wallet);\n            return tonConnectUI.onStatusChange((value: ConnectedWallet | null) => {\n                setWallet(value);\n            });\n        }\n    }, [tonConnectUI]);\n\n    return wallet;\n}\n", "import { CHAIN, toUserFriendlyAddress } from '@tonconnect/ui';\nimport { useTonWallet } from './useTonWallet';\nimport { useMemo } from 'react';\n\n/**\n * Use it to get user's current ton wallet address. If wallet is not connected hook will return empty string.\n * @param [userFriendly=true] allows to choose format of the address.\n */\nexport function useTonAddress(userFriendly = true): string {\n    const wallet = useTonWallet();\n    return useMemo(() => {\n        if (wallet) {\n            return userFriendly\n                ? toUserFriendlyAddress(\n                      wallet.account.address,\n                      wallet.account.chain === CHAIN.TESTNET\n                  )\n                : wallet.account.address;\n        } else {\n            return '';\n        }\n    }, [wallet, userFriendly, wallet?.account.address, wallet?.account.chain]);\n}\n", "import { WalletsModal, WalletsModalState } from '@tonconnect/ui';\nimport { useTonConnectUI } from './useTonConnectUI';\nimport { useEffect, useState } from 'react';\n\n/**\n * Use it to get access to the open/close modal functions.\n */\nexport function useTonConnectModal(): Omit<WalletsModal, 'onStateChange'> {\n    const [tonConnectUI] = useTonConnectUI();\n    const [state, setState] = useState(tonConnectUI?.modal.state || null);\n\n    useEffect(() => {\n        if (tonConnectUI) {\n            setState(tonConnectUI.modal.state);\n            return tonConnectUI.onModalStateChange((value: WalletsModalState) => {\n                setState(value);\n            });\n        }\n    }, [tonConnectUI]);\n\n    return {\n        state: state,\n        open: () => tonConnectUI?.modal.open(),\n        close: () => tonConnectUI?.modal.close()\n    };\n}\n", "import { useEffect, useState } from 'react';\nimport { useTonConnectUI } from './useTonConnectUI';\n\n/**\n * Indicates current status of the connection restoring process.\n */\nexport function useIsConnectionRestored(): boolean {\n    const [restored, setRestored] = useState(false);\n    const [tonConnectUI] = useTonConnectUI();\n\n    useEffect(() => {\n        if (tonConnectUI) {\n            tonConnectUI.connectionRestored.then(() => setRestored(true));\n        }\n    }, [tonConnectUI]);\n\n    return restored;\n}\n"], "names": ["tonConnectUI"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,SAAS,eAAwB;IACpC,OAAO,OAAO,WAAW;AAC7B;AAEO,SAAS,eAAwB;IACpC,OAAO,CAAC,aAAa;AACzB;ACKa,MAAA,gOAAsB,gBAAA,EAAmC,IAAI;AAoE1E,IAAI,eAAoC;AASxC,MAAM,uBAAqE,CAAC,OAGtE;IAHsE,IAAA,KAAA,IACxE,EAAA,QAAA,EAAA,GADwE,IAErE,UAAA,UAFqE,IAErE;QADH;KAAA;IAGI,IAAA,aAAA,KAAkB,CAAC,cAAc;QAClB,eAAA,wKAAI,eAAA,CAAa,OAAO;IAAA;IAG3C,OAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EACK,oBAAoB,QAAA,EAApB;QAA6B,OAAO;QAAe;IAAA,CAAS;AAErE;AAEA,MAAe,mOAAA,OAAA,EAAK,oBAAoB;AChGjC,MAAM,+BAA+B,wLAAA,CAAkB;IAC1D,YAAA,GAAe,IAAA,CAA2C;QACtD,KAAA,CAAM,GAAG,IAAI;QAEN,OAAA,cAAA,CAAe,IAAA,EAAM,uBAAuB,SAAS;IAAA;AAEpE;ACLO,MAAM,sCAAsC,uBAAuB;IACtE,YAAA,GAAe,IAAA,CAA2C;QACtD,KAAA,CAAM,GAAG,IAAI;QAEN,OAAA,cAAA,CAAe,IAAA,EAAM,8BAA8B,SAAS;IAAA;AAE3E;ACTO,SAAS,cAAc,QAAA,EAAyD;IACnF,IAAI,CAAC,UAAU;QACX,MAAM,IAAI,8BACN;IACJ;IAGG,OAAA;AACX;ACFO,SAAS,kBAA0E;IAChF,MAAAA,iBAAe,sNAAA,EAAW,mBAAmB;IACnD,MAAM,uNAAa,cAAA,EACf,CAAC,YAAiC;QAC9B,IAAIA,eAAc;YACdA,cAAc,SAAA,GAAY;QAAA;IAElC,GACA;QAACA,aAAY;KAAA;IAGjB,IAAI,gBAAgB;QACT,OAAA;YAAC;YAAiC,KAAM,CAAA,AAAE;SAAA;IAAA;IAGrD,cAAcA,aAAY;IACnB,OAAA;QAACA;QAAe,UAAU;KAAA;AACrC;ACvBA,MAAM,eAAe;AAerB,MAAM,mBAA6D,CAAC,EAAE,SAAA,EAAW,KAAA,EAAA,KAAY;IACzF,MAAM,CAAC,GAAG,UAAU,CAAA,GAAI,gBAAgB;IAExC,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QACD,WAAA;YAAE;QAAA,CAAc;QAC3B,OAAO,IAAM,WAAW;gBAAE,cAAc;YAAA,CAAM;IAAA,GAC/C;QAAC,UAAU;KAAC;IAGX,OAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,OAAA;QACG,IAAI;QACJ;QACA,OAAO,eAAA;YAAE,OAAO;QAAA,GAAkB;IAAM;AAGpD;AAEA,MAAe,qBAAA,iNAAA,EAAK,gBAAgB;AC5B7B,SAAS,eAAoE;IAC1E,MAAA,CAACA,aAAY,CAAA,GAAI,gBAAgB;IACjC,MAAA,CAAC,QAAQ,SAAS,CAAA,OAAI,iNAAA,EAAA,CACxBA,iBAAA,OAAA,KAAA,IAAAA,cAAc,MAAA,KAAU;IAG5B,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QACZ,IAAIA,eAAc;YACd,UAAUA,cAAa,MAAM;YACtB,OAAAA,cAAa,cAAA,CAAe,CAAC,UAAkC;gBAClE,UAAU,KAAK;YAAA,CAClB;QAAA;IACL,GACD;QAACA,aAAY;KAAC;IAEV,OAAA;AACX;ACfgB,SAAA,cAAc,eAAe,IAAA,EAAc;IACvD,MAAM,SAAS,aAAa;IAC5B,iNAAO,UAAA,EAAQ,MAAM;QACjB,IAAI,QAAQ;YACR,OAAO,+LACD,wBAAA,EACI,OAAO,OAAA,CAAQ,OAAA,EACf,OAAO,OAAA,CAAQ,KAAA,KAAU,yKAAA,CAAM,OAAA,IAEnC,OAAO,OAAA,CAAQ,OAAA;QAAA,OAClB;YACI,OAAA;QAAA;IACX,GACD;QAAC;QAAQ;QAAc,UAAA,OAAA,KAAA,IAAA,OAAQ,OAAA,CAAQ,OAAA;QAAS,UAAA,OAAA,KAAA,IAAA,OAAQ,OAAA,CAAQ,KAAK;KAAC;AAC7E;ACfO,SAAS,qBAA0D;IAChE,MAAA,CAACA,aAAY,CAAA,GAAI,gBAAgB;IACjC,MAAA,CAAC,OAAO,QAAQ,CAAA,6MAAI,WAAA,EAAA,CAASA,iBAAA,OAAA,KAAA,IAAAA,cAAc,KAAA,CAAM,KAAA,KAAS,IAAI;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QACZ,IAAIA,eAAc;YACL,SAAAA,cAAa,KAAA,CAAM,KAAK;YAC1B,OAAAA,cAAa,kBAAA,CAAmB,CAAC,UAA6B;gBACjE,SAAS,KAAK;YAAA,CACjB;QAAA;IACL,GACD;QAACA,aAAY;KAAC;IAEV,OAAA;QACH;QACA,MAAM,IAAMA,iBAAA,OAAA,KAAA,IAAAA,cAAc,KAAA,CAAM,IAAA;QAChC,OAAO,IAAMA,iBAAA,OAAA,KAAA,IAAAA,cAAc,KAAA,CAAM,KAAA;IACrC;AACJ;ACnBO,SAAS,0BAAmC;IAC/C,MAAM,CAAC,UAAU,WAAW,CAAA,6MAAI,WAAA,EAAS,KAAK;IACxC,MAAA,CAACA,aAAY,CAAA,GAAI,gBAAgB;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QACZ,IAAIA,eAAc;YACdA,cAAa,kBAAA,CAAmB,IAAA,CAAK,IAAM,YAAY,IAAI,CAAC;QAAA;IAChE,GACD;QAACA,aAAY;KAAC;IAEV,OAAA;AACX", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "debugId": null}}]}